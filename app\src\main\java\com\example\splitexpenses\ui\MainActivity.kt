package com.example.splitexpenses.ui

import android.content.Intent
import android.os.Bundle
import android.view.WindowManager

import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

import dagger.hilt.android.AndroidEntryPoint

import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.rememberNavController

import com.google.firebase.database.ktx.database
import com.google.firebase.ktx.Firebase

import javax.inject.Inject

import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.repositories.GroupRepository
import com.example.splitexpenses.data.source.LocalDataSource
import com.example.splitexpenses.ui.components.CreateGroupDialog
import com.example.splitexpenses.ui.components.DeleteExpenseDialog
import com.example.splitexpenses.ui.components.DeleteGroupDialog
import com.example.splitexpenses.ui.components.EditGroupNameDialog
import com.example.splitexpenses.ui.components.EditMemberInfoDialog
import com.example.splitexpenses.ui.components.ImportDialog
import com.example.splitexpenses.ui.components.InvitationAcceptDialog
import com.example.splitexpenses.ui.components.JoinGroupDialog
import com.example.splitexpenses.ui.components.ManageMembersDialog
import com.example.splitexpenses.ui.navigation.NavDestinations
import com.example.splitexpenses.ui.navigation.SplitExpensesNavHost
import com.example.splitexpenses.ui.navigation.navigateWithoutAnimation
import com.example.splitexpenses.ui.theme.SplitExpensesTheme
import com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel
import com.example.splitexpenses.ui.viewmodels.GroupListViewModel
import com.example.splitexpenses.util.CsvImportError
import com.example.splitexpenses.util.CsvImportResult
import com.example.splitexpenses.util.InvitationLinkUtil

/**
 * Main activity for the SplitExpenses app
 * Annotated with @AndroidEntryPoint to enable Hilt dependency injection
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    @Inject
    lateinit var groupRepository: GroupRepository

    @Inject
    lateinit var localDataSource: LocalDataSource

    private val groupListViewModel: GroupListViewModel by viewModels()
    private val expenseListViewModel: ExpenseListViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Handle deep links
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)

        // Handle deep links in the new intent
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent) {
        // Check if the intent is a deep link
        if (intent.action == Intent.ACTION_VIEW && intent.data != null) {
            // Extract the group ID from the URI
            val uri = intent.data
            val groupId = InvitationLinkUtil.extractGroupId(uri)

            if (groupId != null) {
                // Store the group ID to be handled in the MainScreen
                intent.putExtra("invitation_group_id", groupId)
            }
        }

        setContent {
            val navController = rememberNavController()
            var showCreateGroupDialog by remember { mutableStateOf(false) }
            var showJoinGroupDialog by remember { mutableStateOf(false) }
            var showManageMembersDialog by remember { mutableStateOf(false) }
            var showImportDialog by remember { mutableStateOf(false) }
            var showDeleteGroupDialog by remember { mutableStateOf(false) }
            var showDeleteExpenseDialog by remember { mutableStateOf("") }
            var showEditGroupNameDialog by remember { mutableStateOf(false) }
            var showEditMemberInfoDialog by remember { mutableStateOf(false) }

            // State for invitation handling
            var showInvitationDialog by remember { mutableStateOf(false) }
            var invitationGroupId by remember { mutableStateOf("") }
            var invitationGroupName by remember { mutableStateOf("") }
            var unassignedMembers by remember { mutableStateOf(listOf<String>()) }

            // Handle invitation deep link
            LaunchedEffect(intent) {
                intent?.let {
                    val groupId = it.getStringExtra("invitation_group_id")

                    if (groupId != null) {
                        // Get group details
                        try {
                            // Check if the group exists
                            val database = Firebase.database.reference
                            val snapshot = database.child("groups").child(groupId).get().await()

                            if (!snapshot.exists()) {
                                return@let
                            }

                            val group = snapshot.getValue(com.example.splitexpenses.data.GroupData::class.java)

                            if (group != null) {
                                // Store the group information
                                invitationGroupId = groupId
                                invitationGroupName = group.name

                                // Get unassigned members and member status
                                val deviceUid = localDataSource.getDeviceUid()

                                // Variables to store member information
                                var allMembers = emptyList<String>()
                                var assignedMembers = emptyList<String>()

                                // Get the list of members with their status
                                try {
                                    val (members, assigned) = groupListViewModel.getGroupMembersWithStatus(groupId)
                                    allMembers = members
                                    assignedMembers = assigned
                                    unassignedMembers = members.filter { it !in assigned }
                                } catch (e: Exception) {
                                    println("Error getting members with status: ${e.message}")
                                    try {
                                        // Fallback to just getting unassigned members
                                        unassignedMembers = groupListViewModel.getUnassignedMembers(groupId)
                                    } catch (e2: Exception) {
                                        println("Error getting unassigned members: ${e2.message}")
                                        unassignedMembers = emptyList()
                                    }
                                }

                                // Check if the current user is already in the group
                                if (deviceUid in group.allowedUsers) {
                                    // Get the user's name in this group
                                    val userName = localDataSource.getSavedUserForGroup(groupId)
                                    if (userName != null) {
                                        // Join the group directly
                                        groupListViewModel.joinGroup(groupId, userName)
                                        navController.navigateWithoutAnimation("${NavDestinations.EXPENSE_LIST_ROUTE}/$groupId")
                                        return@let
                                    }
                                }

                                // Show the invitation dialog
                                showInvitationDialog = true
                            }
                        } catch (e: Exception) {
                            // Handle error
                        }
                    }
                }
            }

            // Observe access lost events and navigate back to group list
            LaunchedEffect(Unit) {
                expenseListViewModel.groupRepository.accessLost.collect { accessLost ->
                    println("MainActivity: Access lost state changed: $accessLost")
                    if (accessLost) {
                        println("MainActivity: User lost access to group, navigating to group list")
                        navController.navigateWithoutAnimation(NavDestinations.GROUP_LIST_ROUTE) {
                            popUpTo(NavDestinations.GROUP_LIST_ROUTE) { inclusive = true }
                        }
                        // Reset the access lost flag
                        expenseListViewModel.groupRepository.resetAccessLost()
                        println("MainActivity: Navigation completed and access lost flag reset")
                    }
                }
            }

            SplitExpensesTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    SplitExpensesNavHost(
                        navController = navController,
                        modifier = Modifier.padding(innerPadding),
                        onShowCreateGroupDialog = { showCreateGroupDialog = true },
                        onShowJoinGroupDialog = { showJoinGroupDialog = true },
                        onShowManageMembersDialog = { showManageMembersDialog = true },
                        onShowDeleteGroupDialog = { showDeleteGroupDialog = true },
                        onShowDeleteExpenseDialog = { expenseId -> showDeleteExpenseDialog = expenseId },
                        onShowEditGroupNameDialog = {
                            println("MainActivity: Setting showEditGroupNameDialog to true")
                            showEditGroupNameDialog = true
                        },
                        onShowEditMemberInfoDialog = {
                            println("MainActivity: Setting showEditMemberInfoDialog to true")
                            showEditMemberInfoDialog = true
                        },
                        onInviteClick = { groupId, groupName ->
                            println("MainActivity: Sharing invitation link for group: $groupId")
                            try {
                                val context = this@MainActivity
                                InvitationLinkUtil.shareInvitationLink(context, groupId, groupName)
                                println("MainActivity: Invitation link shared successfully")
                            } catch (e: Exception) {
                                println("MainActivity: Error sharing invitation link: ${e.message}")
                                e.printStackTrace()
                            }
                        }
                    )

                    // Dialogs
                    if (showCreateGroupDialog) {
                        CreateGroupDialog(
                            onDismiss = { showCreateGroupDialog = false },
                            onCreate = { name, members, creatorName, callback ->
                                lifecycleScope.launch {
                                    try {
                                        val groupId = groupListViewModel.createGroup(name, members, creatorName)
                                        navController.navigateWithoutAnimation("${NavDestinations.EXPENSE_LIST_ROUTE}/$groupId")
                                        showCreateGroupDialog = false
                                        callback(true, null)
                                    } catch (e: Exception) {
                                        println("Error creating group: ${e.message}")
                                        e.printStackTrace()
                                        // Keep dialog open if there's an error
                                        callback(false, e.message)
                                    }
                                }
                            },
                            onImportClick = {
                                showImportDialog = true
                                showCreateGroupDialog = false
                            }
                        )
                    }

                    if (showJoinGroupDialog) {
                        // Local state for join group dialog errors
                        var joinGroupError by remember { mutableStateOf<String?>(null) }
                        var joinGroupLoading by remember { mutableStateOf(false) }

                        JoinGroupDialog(
                            onDismiss = {
                                showJoinGroupDialog = false
                                joinGroupError = null
                            },
                            onJoin = { groupId, userName ->
                                lifecycleScope.launch {
                                    joinGroupLoading = true
                                    joinGroupError = null

                                    try {
                                        // Check if the group exists
                                        val database = Firebase.database.reference
                                        val snapshot = database.child("groups").child(groupId).get().await()
                                        val group = snapshot.getValue(GroupData::class.java)

                                        if (group == null) {
                                            // Group not found
                                            joinGroupError = "Group not found. Please check the Group ID and try again."
                                            return@launch
                                        }

                                        // Check if the current user is already a member of this group
                                        val currentUserUid = localDataSource.getDeviceUid()
                                        if (currentUserUid in group.allowedUsers) {
                                            joinGroupError = "You are already a member of this group."
                                            return@launch
                                        }

                                        // Join the group with the provided user name
                                        groupListViewModel.joinGroup(groupId, userName)
                                        navController.navigateWithoutAnimation("${NavDestinations.EXPENSE_LIST_ROUTE}/$groupId")

                                        // Close join dialog on success
                                        showJoinGroupDialog = false
                                        joinGroupError = null

                                    } catch (e: Exception) {
                                        println("Error joining group: ${e.message}")
                                        joinGroupError = "Failed to join group: ${e.message}"
                                    } finally {
                                        joinGroupLoading = false
                                    }
                                }
                            },
                            onFetchUnassignedMembers = { groupId ->
                                // Suspend function to fetch unassigned members
                                groupListViewModel.getUnassignedMembers(groupId)
                            },
                            onFetchMembersWithStatus = { groupId ->
                                // Suspend function to fetch all members with their status
                                groupListViewModel.getGroupMembersWithStatus(groupId)
                            },
                            isLoading = joinGroupLoading,
                            error = joinGroupError
                        )
                    }

                    if (showManageMembersDialog) {
                        // Use collectAsState to observe changes to the currentGroup StateFlow
                        val currentGroup = expenseListViewModel.currentGroup.collectAsState().value
                        if (currentGroup != null) {
                            println("MainActivity: Showing ManageMembersDialog with group: ${currentGroup.id}, members: ${currentGroup.members}")
                            // Get the current user from UserPreferences
                            val currentUser = groupRepository.getSavedUserForGroup(currentGroup.id) ?: ""

                            ManageMembersDialog(
                                group = currentGroup,
                                currentUser = currentUser,
                                onDismiss = { showManageMembersDialog = false },
                                onSave = { members ->
                                    lifecycleScope.launch {
                                        expenseListViewModel.updateGroupMembers(members)
                                    }
                                    showManageMembersDialog = false
                                },
                                onAddMember = { newMember ->
                                    println("MainActivity: Adding new member: $newMember")
                                    // We'll update the database immediately when a member is added
                                    lifecycleScope.launch {
                                        try {
                                            // Get the latest group data from the StateFlow
                                            val latestGroup = expenseListViewModel.currentGroup.value
                                            if (latestGroup != null) {
                                                val currentMembers = latestGroup.members.toMutableList()
                                                if (!currentMembers.contains(newMember)) {
                                                    currentMembers.add(newMember)
                                                    expenseListViewModel.updateGroupMembers(currentMembers)
                                                    println("MainActivity: Member added successfully: $newMember")
                                                }
                                            }
                                        } catch (e: Exception) {
                                            println("MainActivity: Error adding member: ${e.message}")
                                            e.printStackTrace()
                                        }
                                    }
                                },
                                onRemoveMember = { member ->
                                    println("MainActivity: Removing member and kicking: $member")
                                    // Use the proper removeMemberAndKick function to remove the member and kick them from allowedUsers
                                    lifecycleScope.launch {
                                        try {
                                            val success = expenseListViewModel.removeMemberAndKick(member)
                                            if (success) {
                                                println("MainActivity: Member removed and kicked successfully: $member")
                                            } else {
                                                println("MainActivity: Failed to remove and kick member: $member")
                                            }
                                        } catch (e: Exception) {
                                            println("MainActivity: Error removing and kicking member: ${e.message}")
                                            e.printStackTrace()
                                        }
                                    }
                                },
                                isCurrentUserGroupCreator = expenseListViewModel.isCurrentUserGroupCreator(),
                                onInviteClick = { groupId, groupName ->
                                    println("MainActivity: Sharing invitation link for group: $groupId")
                                    try {
                                        val context = this@MainActivity
                                        InvitationLinkUtil.shareInvitationLink(context, groupId, groupName)
                                        println("MainActivity: Invitation link shared successfully")
                                    } catch (e: Exception) {
                                        println("MainActivity: Error sharing invitation link: ${e.message}")
                                        e.printStackTrace()
                                    }
                                },
                                onEditGroupName = {
                                    println("MainActivity: Setting showEditGroupNameDialog to true from ManageMembersDialog")
                                    showEditGroupNameDialog = true
                                },
                                onEditMemberInfo = {
                                    println("MainActivity: Setting showEditMemberInfoDialog to true from ManageMembersDialog")
                                    showEditMemberInfoDialog = true
                                },
                                getMemberAvatar = { memberName ->
                                    expenseListViewModel.getMemberAvatar(memberName)
                                }
                            )
                        }
                    }

                    if (showImportDialog) {
                        ImportDialog(
                            onDismiss = { showImportDialog = false },
                            onImport = { uri, currentUser ->
                                // Create a suspend function that returns CsvImportResult
                                suspend fun importCsv(): com.example.splitexpenses.util.CsvImportResult {
                                    try {
                                        val inputStream = contentResolver.openInputStream(uri)
                                        if (inputStream != null) {
                                            val result = groupRepository.importGroupFromCsv(inputStream, currentUser)
                                            if (result.success && result.group != null) {
                                                navController.navigateWithoutAnimation("${NavDestinations.EXPENSE_LIST_ROUTE}/${result.group.id}")
                                            }
                                            return result
                                        } else {
                                            return com.example.splitexpenses.util.CsvImportResult(
                                                success = false,
                                                errors = listOf(com.example.splitexpenses.util.CsvImportError("Could not open file"))
                                            )
                                        }
                                    } catch (e: Exception) {
                                        return com.example.splitexpenses.util.CsvImportResult(
                                            success = false,
                                            errors = listOf(com.example.splitexpenses.util.CsvImportError(e.message ?: "Unknown error"))
                                        )
                                    }
                                }

                                // Launch the import process
                                lifecycleScope.launch {
                                    importCsv()
                                }

                                showImportDialog = false

                                // Return a dummy result for the synchronous part
                                com.example.splitexpenses.util.CsvImportResult(
                                    success = true,
                                    group = null
                                )
                            }
                        )
                    }

                    if (showDeleteGroupDialog) {
                        val currentGroup = expenseListViewModel.currentGroup.value
                        if (currentGroup != null) {
                            // Check if the current user is the creator
                            val isCreator = expenseListViewModel.isCurrentUserGroupCreator()

                            DeleteGroupDialog(
                                groupName = currentGroup.name,
                                isCreator = isCreator,
                                onConfirm = {
                                    lifecycleScope.launch {
                                        val success = expenseListViewModel.deleteCurrentGroup()
                                        if (success) {
                                            navController.navigateWithoutAnimation(NavDestinations.GROUP_LIST_ROUTE) {
                                                popUpTo(NavDestinations.GROUP_LIST_ROUTE) { inclusive = true }
                                            }
                                        }
                                    }
                                    showDeleteGroupDialog = false
                                },
                                onDismiss = { showDeleteGroupDialog = false }
                            )
                        }
                    }

                    if (showDeleteExpenseDialog.isNotEmpty()) {
                        val currentGroup = expenseListViewModel.currentGroup.value
                        if (currentGroup != null) {
                            val expense = currentGroup.expenses.find { expense -> expense.id == showDeleteExpenseDialog }
                            if (expense != null) {
                                DeleteExpenseDialog(
                                    expenseDescription = expense.description,
                                    onConfirm = {
                                        lifecycleScope.launch {
                                            expenseListViewModel.deleteExpense(expense.id)

                                            // Get the current group ID
                                            val currentGroupId = expenseListViewModel.currentGroup.value?.id

                                            if (currentGroupId != null) {
                                                // Navigate to the expense list screen for the current group
                                                navController.navigateWithoutAnimation("${NavDestinations.EXPENSE_LIST_ROUTE}/$currentGroupId") {
                                                    // Pop up to the expense list screen, inclusive means replace it
                                                    popUpTo("${NavDestinations.EXPENSE_LIST_ROUTE}/$currentGroupId") {
                                                        inclusive = true
                                                    }
                                                }
                                            } else {
                                                // Fallback to just popping back if no group ID is available
                                                navController.popBackStack()
                                            }
                                        }
                                        showDeleteExpenseDialog = ""
                                    },
                                    onDismiss = { showDeleteExpenseDialog = "" }
                                )
                            }
                        }
                    }

                    if (showInvitationDialog) {
                        // Get the latest member information
                        var allMembers = emptyList<String>()
                        var assignedMembers = emptyList<String>()

                        // Use LaunchedEffect to fetch the data when the dialog is shown
                        LaunchedEffect(showInvitationDialog) {
                            try {
                                val (members, assigned) = groupListViewModel.getGroupMembersWithStatus(invitationGroupId)
                                allMembers = members
                                assignedMembers = assigned
                                unassignedMembers = members.filter { it !in assigned }
                            } catch (e: Exception) {
                                println("Error getting members with status in dialog: ${e.message}")
                            }
                        }

                        InvitationAcceptDialog(
                            groupName = invitationGroupName,
                            unassignedMembers = unassignedMembers,
                            onAccept = { userName ->
                                lifecycleScope.launch {
                                    groupListViewModel.joinGroup(invitationGroupId, userName)
                                    navController.navigateWithoutAnimation("${NavDestinations.EXPENSE_LIST_ROUTE}/$invitationGroupId")
                                }
                                showInvitationDialog = false
                            },
                            onDismiss = { showInvitationDialog = false },
                            allMembers = allMembers,
                            assignedMembers = assignedMembers
                        )
                    }

                    // Edit Group Name Dialog
                    if (showEditGroupNameDialog) {
                        println("MainActivity: Showing EditGroupNameDialog")
                        val currentGroup = expenseListViewModel.currentGroup.value
                        if (currentGroup != null) {
                            EditGroupNameDialog(
                                currentName = currentGroup.name,
                                onDismiss = {
                                    println("MainActivity: EditGroupNameDialog dismissed")
                                    showEditGroupNameDialog = false
                                },
                                onSave = { newName, callback ->
                                    println("MainActivity: EditGroupNameDialog save with newName=$newName")
                                    lifecycleScope.launch {
                                        try {
                                            val success = expenseListViewModel.updateGroupName(newName)
                                            if (success) {
                                                println("MainActivity: Group name updated successfully")
                                                callback(true, null)
                                            } else {
                                                println("MainActivity: Failed to update group name")
                                                callback(false, "Only the group creator can update the group name")
                                            }
                                        } catch (e: Exception) {
                                            println("MainActivity: Error updating group name: ${e.message}")
                                            e.printStackTrace()
                                            callback(false, e.message)
                                        }
                                    }
                                },
                                isOffline = !expenseListViewModel.isConnected.collectAsState().value
                            )
                        } else {
                            // If there's no current group, just hide the dialog
                            showEditGroupNameDialog = false
                        }
                    }

                    // Edit Member Info Dialog
                    if (showEditMemberInfoDialog) {
                        println("MainActivity: Showing EditMemberInfoDialog")
                        val currentGroup = expenseListViewModel.currentGroup.value
                        if (currentGroup != null) {
                            // Get the current user from UserPreferences
                            val currentUser = groupRepository.getSavedUserForGroup(currentGroup.id) ?: ""
                            val currentAvatar = expenseListViewModel.getMemberAvatar(currentUser)

                            EditMemberInfoDialog(
                                currentName = currentUser,
                                currentAvatar = currentAvatar,
                                onDismiss = {
                                    println("MainActivity: EditMemberInfoDialog dismissed")
                                    showEditMemberInfoDialog = false
                                },
                                isOffline = !expenseListViewModel.isConnected.collectAsState().value,
                                onSave = { newName, avatarEmoji, callback ->
                                    println("MainActivity: EditMemberInfoDialog save with newName=$newName, avatarEmoji=$avatarEmoji")
                                    lifecycleScope.launch {
                                        try {
                                            // First update the avatar
                                            val avatarSuccess = expenseListViewModel.updateCurrentUserAvatar(avatarEmoji)

                                            // Then update the name if it has changed
                                            val nameSuccess = if (newName != currentUser) {
                                                expenseListViewModel.updateCurrentUserName(newName)
                                            } else {
                                                true // Name didn't change, so consider it a success
                                            }

                                            if (avatarSuccess && nameSuccess) {
                                                println("MainActivity: Member info updated successfully")
                                                callback(true, null)
                                            } else {
                                                println("MainActivity: Failed to update member info")
                                                callback(false, "Failed to update member information")
                                            }
                                        } catch (e: Exception) {
                                            println("MainActivity: Error updating member info: ${e.message}")
                                            e.printStackTrace()
                                            callback(false, e.message)
                                        }
                                    }
                                }
                            )
                        } else {
                            // If there's no current group, just hide the dialog
                            showEditMemberInfoDialog = false
                        }
                    }
                }
            }
        }
    }
}
