// Test code to verify round-trip functionality
// This would be used in a unit test

import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.util.CsvUtil

fun testRoundTripFunctionality() {
    // Create a sample group for testing
    val testGroup = GroupData(
        id = "test-group-id",
        name = "Test Group",
        members = listOf("<PERSON>", "<PERSON>", "<PERSON>"),
        expenses = listOf(
            Expense(
                id = "expense-1",
                amount = 25.50,
                description = "Lunch at restaurant",
                paidBy = "Alice",
                splitBetween = listOf("Alice", "<PERSON>"),
                category = "Food",
                date = System.currentTimeMillis(),
                isCategoryLocked = true
            ),
            Expense(
                id = "expense-2",
                amount = 15.75,
                description = "Coffee break",
                paidBy = "Bob",
                splitBetween = listOf("Alice", "<PERSON>", "<PERSON>"),
                category = "Food",
                date = System.currentTimeMillis(),
                isCategoryLocked = false
            )
        ),
        memberAvatars = mapOf(
            "Alice" to "👩",
            "<PERSON>" to "👨",
            "<PERSON>" to "🧑"
        ),
        categories = listOf(
            Category(name = "Food", emoji = "🍔", keywords = listOf("restaurant", "lunch")),
            Category(name = "Transport", emoji = "🚗", keywords = listOf("taxi", "gas"))
        )
    )

    // Test the round-trip functionality
    val testResult = CsvUtil.testExportImportRoundTrip(testGroup)

    if (testResult.success) {
        println("✅ Round-trip test PASSED")
        println("Exported CSV:")
        println(testResult.exportedCsv)
    } else {
        println("❌ Round-trip test FAILED")
        println("Error: ${testResult.error}")
        if (testResult.exportedCsv != null) {
            println("Exported CSV:")
            println(testResult.exportedCsv)
        }
        if (testResult.importResult != null) {
            println("Import errors: ${testResult.importResult.errors}")
            println("Import warnings: ${testResult.importResult.warnings}")
        }
    }
}

// Expected output for successful test:
// ✅ Round-trip test PASSED
// Exported CSV:
// name,members,memberAvatars,categories
// "Test Group","Alice|Bob|Charlie","Alice=👩|Bob=👨|Charlie=🧑","Food~🍔~restaurant^lunch|Transport~🚗~taxi^gas"
// amount,description,paidBy,splitBetween,category,date,isCategoryLocked
// 25.50,"Lunch at restaurant","Alice","Alice|Bob","Food","2024-01-15",true
// 15.75,"Coffee break","Bob","Alice|Bob|Charlie","Food","2024-01-15",false
