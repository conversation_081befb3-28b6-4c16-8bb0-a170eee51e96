package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\u001aV\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052,\u0010\u0006\u001a(\u0012\u0004\u0012\u00020\u0003\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\b\u0012\u0006\u0012\u0004\u0018\u00010\u0003\u0012\u0004\u0012\u00020\u00010\u0007\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\t\u001a\u00020\bH\u0007\u00a8\u0006\n"}, d2 = {"EditGroupNameDialog", "", "currentName", "", "onDismiss", "Lkotlin/Function0;", "onSave", "Lkotlin/Function2;", "", "isOffline", "app_debug"})
public final class EditGroupNameDialogKt {
    
    /**
     * Dialog for editing a group's name
     * @param currentName The current name of the group
     * @param onDismiss Callback for when the dialog is dismissed
     * @param onSave Callback for when the name is saved, with a callback for success/failure
     * @param isOffline Whether the device is currently offline
     */
    @androidx.compose.runtime.Composable()
    public static final void EditGroupNameDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String currentName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit>, kotlin.Unit> onSave, boolean isOffline) {
    }
}