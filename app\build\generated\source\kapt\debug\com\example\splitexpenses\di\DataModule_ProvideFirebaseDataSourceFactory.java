package com.example.splitexpenses.di;

import com.example.splitexpenses.data.source.DataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideFirebaseDataSourceFactory implements Factory<DataSource> {
  @Override
  public DataSource get() {
    return provideFirebaseDataSource();
  }

  public static DataModule_ProvideFirebaseDataSourceFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DataSource provideFirebaseDataSource() {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideFirebaseDataSource());
  }

  private static final class InstanceHolder {
    private static final DataModule_ProvideFirebaseDataSourceFactory INSTANCE = new DataModule_ProvideFirebaseDataSourceFactory();
  }
}
