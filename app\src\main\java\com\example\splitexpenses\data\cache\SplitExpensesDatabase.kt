package com.example.splitexpenses.data.cache

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.splitexpenses.data.cache.dao.CategoryDao
import com.example.splitexpenses.data.cache.dao.ExpenseDao
import com.example.splitexpenses.data.cache.dao.GroupDao
import com.example.splitexpenses.data.cache.dao.SyncQueueDao
import com.example.splitexpenses.data.cache.entities.CategoryEntity
import com.example.splitexpenses.data.cache.entities.ExpenseEntity
import com.example.splitexpenses.data.cache.entities.GroupEntity
import com.example.splitexpenses.data.cache.entities.SyncQueueEntity

/**
 * Room database for offline caching of SplitExpenses data
 */
@Database(
    entities = [
        GroupEntity::class,
        ExpenseEntity::class,
        CategoryEntity::class,
        SyncQueueEntity::class
    ],
    version = 2,
    exportSchema = false
)
abstract class SplitExpensesDatabase : RoomDatabase() {

    abstract fun groupDao(): GroupDao
    abstract fun expenseDao(): ExpenseDao
    abstract fun categoryDao(): CategoryDao
    abstract fun syncQueueDao(): SyncQueueDao

    companion object {
        @Volatile
        private var INSTANCE: SplitExpensesDatabase? = null

        /**
         * Get the database instance
         */
        fun getDatabase(context: Context): SplitExpensesDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    SplitExpensesDatabase::class.java,
                    "split_expenses_database"
                )
                    // For the initial implementation, we'll use fallbackToDestructiveMigration
                    // This is safe since this is the first version of the offline database
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }

        /**
         * Clear the database instance (useful for testing)
         */
        fun clearInstance() {
            INSTANCE = null
        }
    }
}
