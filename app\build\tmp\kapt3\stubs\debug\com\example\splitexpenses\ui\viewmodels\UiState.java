package com.example.splitexpenses.ui.viewmodels;

/**
 * Base interface for UI states
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001R\u0014\u0010\u0002\u001a\u0004\u0018\u00010\u0003X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0004\u0010\u0005R\u0012\u0010\u0006\u001a\u00020\u0007X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0006\u0010\b\u00a8\u0006\t"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/UiState;", "", "error", "", "getError", "()Ljava/lang/String;", "isLoading", "", "()Z", "app_debug"})
public abstract interface UiState {
    
    public abstract boolean isLoading();
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.String getError();
}