package com.example.splitexpenses.data.repositories;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExpenseRepository_Factory implements Factory<ExpenseRepository> {
  private final Provider<OfflineCapableRepository> offlineCapableRepositoryProvider;

  private final Provider<GroupRepository> groupRepositoryProvider;

  public ExpenseRepository_Factory(
      Provider<OfflineCapableRepository> offlineCapableRepositoryProvider,
      Provider<GroupRepository> groupRepositoryProvider) {
    this.offlineCapableRepositoryProvider = offlineCapableRepositoryProvider;
    this.groupRepositoryProvider = groupRepositoryProvider;
  }

  @Override
  public ExpenseRepository get() {
    return newInstance(offlineCapableRepositoryProvider.get(), groupRepositoryProvider.get());
  }

  public static ExpenseRepository_Factory create(
      Provider<OfflineCapableRepository> offlineCapableRepositoryProvider,
      Provider<GroupRepository> groupRepositoryProvider) {
    return new ExpenseRepository_Factory(offlineCapableRepositoryProvider, groupRepositoryProvider);
  }

  public static ExpenseRepository newInstance(OfflineCapableRepository offlineCapableRepository,
      GroupRepository groupRepository) {
    return new ExpenseRepository(offlineCapableRepository, groupRepository);
  }
}
