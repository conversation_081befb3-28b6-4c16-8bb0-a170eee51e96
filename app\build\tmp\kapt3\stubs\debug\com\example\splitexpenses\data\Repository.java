package com.example.splitexpenses.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00c0\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\"\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u000f\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010/\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u00102J\u001e\u00103\u001a\u0002042\u0006\u00100\u001a\u00020\u00062\u0006\u00105\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u00102J\u001e\u00106\u001a\u0002042\u0006\u00100\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u00102JN\u00107\u001a\u0002042\u0006\u00108\u001a\u0002092\u0006\u0010:\u001a\u00020\u00062\u0006\u0010;\u001a\u00020\u00062\f\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\u0006\u0010=\u001a\u00020\u00062\u0006\u0010>\u001a\u00020?2\b\b\u0002\u0010@\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010AJ\f\u0010B\u001a\b\u0012\u0004\u0012\u00020C0\u000bJ\u0014\u0010D\u001a\b\u0012\u0004\u0012\u00020C0\u000b2\u0006\u0010E\u001a\u00020\fJ,\u0010F\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u0002090G0\u000b2\f\u0010H\u001a\b\u0012\u0004\u0012\u00020C0\u000bJ\u0006\u0010I\u001a\u000204J\u0006\u0010J\u001a\u000204J,\u0010K\u001a\u00020\u00062\u0006\u0010L\u001a\u00020\u00062\f\u0010M\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\u0006\u0010N\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010OJ\u0016\u0010P\u001a\u0002042\u0006\u0010Q\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010RJ\u001c\u0010S\u001a\u0002042\f\u0010T\u001a\b\u0012\u0004\u0012\u00020\u00060UH\u0086@\u00a2\u0006\u0002\u0010VJ\u0016\u0010W\u001a\u00020\t2\u0006\u00100\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010RJ\u000e\u0010X\u001a\u00020\t2\u0006\u0010Y\u001a\u00020ZJ\u0018\u0010[\u001a\u0004\u0018\u00010\f2\u0006\u00100\u001a\u00020\u0006H\u0082@\u00a2\u0006\u0002\u0010RJ\u0014\u0010\\\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\u0006\u00100\u001a\u00020\u0006J.\u0010]\u001a\u001a\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u000b\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u000b0^2\u0006\u00100\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010RJ\u0018\u0010_\u001a\u0004\u0018\u00010\u00062\u0006\u00100\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u0006J\u0010\u0010`\u001a\u0004\u0018\u00010\u00062\u0006\u00100\u001a\u00020\u0006J\u001c\u0010a\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\u0006\u00100\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010RJ&\u0010b\u001a\u0002042\u0006\u0010c\u001a\u00020\u00062\n\u0010d\u001a\u00060ej\u0002`f2\b\b\u0002\u0010g\u001a\u00020\tH\u0002J\"\u0010h\u001a\u00020i2\u0006\u0010j\u001a\u00020k2\n\b\u0002\u0010N\u001a\u0004\u0018\u00010\u0006H\u0086@\u00a2\u0006\u0002\u0010lJ\u000e\u0010m\u001a\u00020\t2\u0006\u00100\u001a\u00020\u0006J\u001e\u0010n\u001a\u0002042\u0006\u00100\u001a\u00020\u00062\u0006\u0010N\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u00102J\u000e\u0010o\u001a\u000204H\u0086@\u00a2\u0006\u0002\u0010pJ\u0010\u0010q\u001a\u0002042\u0006\u0010r\u001a\u00020sH\u0002J\u000e\u0010t\u001a\u000204H\u0086@\u00a2\u0006\u0002\u0010pJ\u001e\u0010u\u001a\u0002042\u0006\u00100\u001a\u00020\u00062\u0006\u00105\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u00102J\u001e\u0010v\u001a\u0002042\u0006\u00100\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u00102J\u001e\u0010w\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u00102J\u0006\u0010x\u001a\u000204J\u0006\u0010y\u001a\u000204J\u000e\u0010z\u001a\u0002042\u0006\u00100\u001a\u00020\u0006J\u0006\u0010{\u001a\u000204J\u0006\u0010|\u001a\u000204JV\u0010}\u001a\u0002042\u0006\u0010Q\u001a\u00020\u00062\u0006\u00108\u001a\u0002092\u0006\u0010:\u001a\u00020\u00062\u0006\u0010;\u001a\u00020\u00062\f\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\u0006\u0010=\u001a\u00020\u00062\u0006\u0010>\u001a\u00020?2\b\b\u0002\u0010@\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010~J \u0010\u007f\u001a\u0002042\u0006\u00100\u001a\u00020\u00062\u0007\u0010\u0080\u0001\u001a\u00020\fH\u0082@\u00a2\u0006\u0003\u0010\u0081\u0001J(\u0010\u0082\u0001\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00062\u000e\u0010\u0083\u0001\u001a\t\u0012\u0005\u0012\u00030\u0084\u00010\u000bH\u0086@\u00a2\u0006\u0003\u0010\u0085\u0001J*\u0010\u0086\u0001\u001a\u0002042\u0006\u00100\u001a\u00020\u00062\u0007\u0010\u0087\u0001\u001a\u00020\u00062\u0007\u0010\u0088\u0001\u001a\u00020\u0001H\u0082@\u00a2\u0006\u0003\u0010\u0089\u0001J&\u0010\u008a\u0001\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00062\f\u0010M\u001a\b\u0012\u0004\u0012\u00020\u00060\u000bH\u0086@\u00a2\u0006\u0003\u0010\u0085\u0001J \u0010\u008b\u0001\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00062\u0007\u0010\u008c\u0001\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u00102J+\u0010\u008d\u0001\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u00062\t\u0010\u008e\u0001\u001a\u0004\u0018\u00010\u0006H\u0086@\u00a2\u0006\u0003\u0010\u008f\u0001J*\u0010\u0090\u0001\u001a\u00020\t2\u0006\u00100\u001a\u00020\u00062\u0007\u0010\u0091\u0001\u001a\u00020\u00062\u0007\u0010\u0092\u0001\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0003\u0010\u008f\u0001R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0015R\u0019\u0010 \u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0015R\u0010\u0010\"\u001a\u0004\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0011\u0010#\u001a\u00020$\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0019\u0010\'\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u0015R\u0017\u0010)\u001a\b\u0012\u0004\u0012\u00020\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u0015R\u0017\u0010*\u001a\b\u0012\u0004\u0012\u00020\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u0015R\u0011\u0010+\u001a\u00020,\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010.\u00a8\u0006\u0093\u0001"}, d2 = {"Lcom/example/splitexpenses/data/Repository;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "TAG", "", "_accessLost", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_availableGroups", "", "Lcom/example/splitexpenses/data/GroupData;", "_currentGroup", "_currentGroupError", "_groupsError", "_isLoadingCurrentGroup", "_isLoadingGroups", "accessLost", "Lkotlinx/coroutines/flow/StateFlow;", "getAccessLost", "()Lkotlinx/coroutines/flow/StateFlow;", "availableGroups", "getAvailableGroups", "availableGroupsListener", "Lcom/google/firebase/database/ValueEventListener;", "getContext", "()Landroid/content/Context;", "coroutineScope", "Lkotlinx/coroutines/CoroutineScope;", "currentGroup", "getCurrentGroup", "currentGroupError", "getCurrentGroupError", "currentGroupListener", "database", "Lcom/google/firebase/database/DatabaseReference;", "getDatabase", "()Lcom/google/firebase/database/DatabaseReference;", "groupsError", "getGroupsError", "isLoadingCurrentGroup", "isLoadingGroups", "userPreferences", "Lcom/example/splitexpenses/data/UserPreferences;", "getUserPreferences", "()Lcom/example/splitexpenses/data/UserPreferences;", "acceptInvitation", "groupId", "memberName", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addAllowedUser", "", "userUid", "addAllowedUserByName", "addExpense", "amount", "", "description", "paidBy", "splitBetween", "category", "date", "", "isCategoryLocked", "(DLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "calculateFinance", "Lcom/example/splitexpenses/data/UserFinance;", "calculateFinances", "group", "calculateSettlements", "Lkotlin/Triple;", "finances", "cleanup", "clearCurrentGroup", "createGroup", "name", "members", "currentUser", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpense", "expenseId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenses", "expenseIds", "", "(Ljava/util/Set;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGroup", "exportGroupToCsv", "outputStream", "Ljava/io/OutputStream;", "fetchGroupById", "getAllowedMemberNames", "getGroupMembersWithStatus", "Lkotlin/Pair;", "getMemberAvatar", "getSavedUserForGroup", "getUnassignedMembers", "handleError", "operation", "e", "Ljava/lang/Exception;", "Lkotlin/Exception;", "isCurrentGroup", "importGroupFromCsv", "Lcom/example/splitexpenses/util/CsvImportResult;", "inputStream", "Ljava/io/InputStream;", "(Ljava/io/InputStream;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isCurrentUserGroupCreator", "joinGroup", "loadAvailableGroups", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processAvailableGroupsSnapshot", "snapshot", "Lcom/google/firebase/database/DataSnapshot;", "refreshCurrentGroup", "removeAllowedUser", "removeAllowedUserByName", "removeMemberAndKick", "resetAccessLost", "startListeningForAvailableGroups", "startListeningForCurrentGroup", "stopListeningForAvailableGroups", "stopListeningForCurrentGroup", "updateExpense", "(Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGroup", "updatedGroup", "(Ljava/lang/String;Lcom/example/splitexpenses/data/GroupData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGroupCategories", "categories", "Lcom/example/splitexpenses/data/Category;", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGroupField", "field", "value", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGroupMembers", "updateGroupName", "newName", "updateMemberAvatar", "avatarEmoji", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateMemberName", "oldMemberName", "newMemberName", "app_debug"})
public final class Repository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "Repository";
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.database.DatabaseReference database = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.UserPreferences userPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope coroutineScope = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.splitexpenses.data.GroupData> _currentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> currentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.splitexpenses.data.GroupData>> _availableGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.splitexpenses.data.GroupData>> availableGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoadingGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoadingCurrentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingCurrentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _groupsError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> groupsError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _currentGroupError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> currentGroupError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _accessLost = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> accessLost = null;
    @org.jetbrains.annotations.Nullable()
    private com.google.firebase.database.ValueEventListener availableGroupsListener;
    @org.jetbrains.annotations.Nullable()
    private com.google.firebase.database.ValueEventListener currentGroupListener;
    
    public Repository(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.content.Context getContext() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.google.firebase.database.DatabaseReference getDatabase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.UserPreferences getUserPreferences() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> getCurrentGroup() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.splitexpenses.data.GroupData>> getAvailableGroups() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingGroups() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingCurrentGroup() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getGroupsError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getCurrentGroupError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getAccessLost() {
        return null;
    }
    
    /**
     * Load available groups once (for backward compatibility)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object loadAvailableGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Start listening for real-time updates to available groups
     */
    public final void startListeningForAvailableGroups() {
    }
    
    /**
     * Stop listening for real-time updates to available groups
     */
    public final void stopListeningForAvailableGroups() {
    }
    
    /**
     * Process a snapshot of available groups
     */
    private final void processAvailableGroupsSnapshot(com.google.firebase.database.DataSnapshot snapshot) {
    }
    
    public final void clearCurrentGroup() {
    }
    
    /**
     * Reset the access lost flag (call this when navigating back to group list)
     */
    public final void resetAccessLost() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> members, @org.jetbrains.annotations.NotNull()
    java.lang.String currentUser, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object joinGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String currentUser, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Start listening for real-time updates to the current group
     */
    public final void startListeningForCurrentGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
    }
    
    /**
     * Stop listening for real-time updates to the current group
     */
    public final void stopListeningForCurrentGroup() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSavedUserForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addExpense(double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String paidBy, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> splitBetween, @org.jetbrains.annotations.NotNull()
    java.lang.String category, long date, boolean isCategoryLocked, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String paidBy, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> splitBetween, @org.jetbrains.annotations.NotNull()
    java.lang.String category, long date, boolean isCategoryLocked, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Calculate financial data for each user in the current group
     * @param group The group to calculate finances for
     * @return A list of UserFinance objects
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.data.UserFinance> calculateFinances(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.GroupData group) {
        return null;
    }
    
    /**
     * Calculate settlements for the current group
     * @param finances The list of UserFinance objects
     * @return A list of Triple objects (debtor, creditor, amount)
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<kotlin.Triple<java.lang.String, java.lang.String, java.lang.Double>> calculateSettlements(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.UserFinance> finances) {
        return null;
    }
    
    /**
     * Calculate financial data for the current group
     * @return A list of UserFinance objects
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.data.UserFinance> calculateFinance() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpenses(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> expenseIds, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupMembers(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> members, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Handle Firebase operation errors consistently
     * @param operation A description of the operation that failed
     * @param e The exception that was thrown
     * @param isCurrentGroup Whether this is related to the current group or all groups
     */
    private final void handleError(java.lang.String operation, java.lang.Exception e, boolean isCurrentGroup) {
    }
    
    /**
     * Fetch a group by ID from Firebase
     * @param groupId The ID of the group to fetch
     * @return The group data or null if not found
     */
    private final java.lang.Object fetchGroupById(java.lang.String groupId, kotlin.coroutines.Continuation<? super com.example.splitexpenses.data.GroupData> $completion) {
        return null;
    }
    
    /**
     * Update a group in Firebase and update local state if needed
     * @param groupId The ID of the group to update
     * @param updatedGroup The updated group data
     */
    private final java.lang.Object updateGroup(java.lang.String groupId, com.example.splitexpenses.data.GroupData updatedGroup, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Update a specific field in a group for better performance
     * @param groupId The ID of the group to update
     * @param field The field to update (e.g., "members", "allowedUsers")
     * @param value The new value for the field
     */
    private final java.lang.Object updateGroupField(java.lang.String groupId, java.lang.String field, java.lang.Object value, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Adds a user to the allowedUsers list for a group by their member name
     * The system will automatically convert the member name to a UID
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addAllowedUserByName(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Adds a user to the allowedUsers list for a group by their UID
     * This is kept for backward compatibility
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addAllowedUser(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String userUid, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Removes a user from the allowedUsers list for a group by their member name
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeAllowedUserByName(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Removes a member from a group and kicks them out (removes from allowedUsers)
     * This should only be called by the group creator
     * @param groupId The ID of the group
     * @param memberName The name of the member to remove
     * @return True if successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeMemberAndKick(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Removes a user from the allowedUsers list for a group by their UID
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeAllowedUser(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String userUid, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Gets a list of member names who are allowed to access the group
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAllowedMemberNames(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return null;
    }
    
    /**
     * Gets information about all members in a group, with their assignment status
     * @param groupId The ID of the group
     * @return Pair of (all members, assigned members)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getGroupMembersWithStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Pair<? extends java.util.List<java.lang.String>, ? extends java.util.List<java.lang.String>>> $completion) {
        return null;
    }
    
    /**
     * Gets a list of unassigned member names (members without an associated UID)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnassignedMembers(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * Accept an invitation to join a group
     * @param groupId The ID of the group to join
     * @param memberName The name of the member to join as
     * @return True if successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object acceptInvitation(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Checks if the current user is the creator of the group
     * This method works for any group, not just the current one
     */
    public final boolean isCurrentUserGroupCreator(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return false;
    }
    
    /**
     * Refresh the current group data from Firebase
     * This is kept for backward compatibility, but real-time updates are preferred
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object refreshCurrentGroup(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Update the categories for a group
     * @param groupId The ID of the group to update
     * @param categories The new list of categories
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupCategories(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Category> categories, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update the group name
     * @param groupId The ID of the group to update
     * @param newName The new name for the group
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupName(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String newName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update a member's avatar
     * @param groupId The ID of the group
     * @param memberName The name of the member
     * @param avatarEmoji The emoji to use as the avatar, or null to remove the avatar
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateMemberAvatar(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.Nullable()
    java.lang.String avatarEmoji, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update a member's name while preserving their UID
     * @param groupId The ID of the group
     * @param oldMemberName The current name of the member
     * @param newMemberName The new name for the member
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateMemberName(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String oldMemberName, @org.jetbrains.annotations.NotNull()
    java.lang.String newMemberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Get the avatar for a member
     * @param groupId The ID of the group
     * @param memberName The name of the member
     * @return The avatar emoji or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMemberAvatar(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
        return null;
    }
    
    /**
     * Clean up all resources when the app is closed
     */
    public final void cleanup() {
    }
    
    /**
     * Export the current group to a CSV file
     * @param outputStream The output stream to write the CSV data to
     * @return True if export was successful, false otherwise
     */
    public final boolean exportGroupToCsv(@org.jetbrains.annotations.NotNull()
    java.io.OutputStream outputStream) {
        return false;
    }
    
    /**
     * Import a group from a CSV file
     * @param inputStream The input stream to read the CSV data from
     * @param currentUser Optional current user name to use (will use from CSV if not provided)
     * @return CsvImportResult containing the import results, errors, and warnings
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object importGroupFromCsv(@org.jetbrains.annotations.NotNull()
    java.io.InputStream inputStream, @org.jetbrains.annotations.Nullable()
    java.lang.String currentUser, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.util.CsvImportResult> $completion) {
        return null;
    }
}