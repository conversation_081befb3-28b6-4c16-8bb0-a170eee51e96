package com.example.splitexpenses.ui;

/**
 * Main activity for the SplitExpenses app
 * Annotated with @AndroidEntryPoint to enable Hilt dependency injection
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\u0012\u0010\u001e\u001a\u00020\u001b2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0014J\u0010\u0010!\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dH\u0014R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\b\u001a\u0004\b\u000b\u0010\fR\u001e\u0010\u000e\u001a\u00020\u000f8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0004\b\u0012\u0010\u0013R\u001e\u0010\u0014\u001a\u00020\u00158\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u0017\"\u0004\b\u0018\u0010\u0019\u00a8\u0006\""}, d2 = {"Lcom/example/splitexpenses/ui/MainActivity;", "Landroidx/activity/ComponentActivity;", "()V", "expenseListViewModel", "Lcom/example/splitexpenses/ui/viewmodels/ExpenseListViewModel;", "getExpenseListViewModel", "()Lcom/example/splitexpenses/ui/viewmodels/ExpenseListViewModel;", "expenseListViewModel$delegate", "Lkotlin/Lazy;", "groupListViewModel", "Lcom/example/splitexpenses/ui/viewmodels/GroupListViewModel;", "getGroupListViewModel", "()Lcom/example/splitexpenses/ui/viewmodels/GroupListViewModel;", "groupListViewModel$delegate", "groupRepository", "Lcom/example/splitexpenses/data/repositories/GroupRepository;", "getGroupRepository", "()Lcom/example/splitexpenses/data/repositories/GroupRepository;", "setGroupRepository", "(Lcom/example/splitexpenses/data/repositories/GroupRepository;)V", "localDataSource", "Lcom/example/splitexpenses/data/source/LocalDataSource;", "getLocalDataSource", "()Lcom/example/splitexpenses/data/source/LocalDataSource;", "setLocalDataSource", "(Lcom/example/splitexpenses/data/source/LocalDataSource;)V", "handleIntent", "", "intent", "Landroid/content/Intent;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onNewIntent", "app_debug"})
public final class MainActivity extends androidx.activity.ComponentActivity {
    @javax.inject.Inject()
    public com.example.splitexpenses.data.repositories.GroupRepository groupRepository;
    @javax.inject.Inject()
    public com.example.splitexpenses.data.source.LocalDataSource localDataSource;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy groupListViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy expenseListViewModel$delegate = null;
    
    public MainActivity() {
        super(0);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.repositories.GroupRepository getGroupRepository() {
        return null;
    }
    
    public final void setGroupRepository(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.GroupRepository p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.source.LocalDataSource getLocalDataSource() {
        return null;
    }
    
    public final void setLocalDataSource(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.source.LocalDataSource p0) {
    }
    
    private final com.example.splitexpenses.ui.viewmodels.GroupListViewModel getGroupListViewModel() {
        return null;
    }
    
    private final com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel getExpenseListViewModel() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    protected void onNewIntent(@org.jetbrains.annotations.NotNull()
    android.content.Intent intent) {
    }
    
    private final void handleIntent(android.content.Intent intent) {
    }
}