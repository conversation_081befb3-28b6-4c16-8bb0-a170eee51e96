package com.example.splitexpenses.di;

/**
 * Hilt module that provides data source dependencies
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\u0012\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\u0007\u001a\u00020\bH\u0007J\u0012\u0010\t\u001a\u00020\n2\b\b\u0001\u0010\u0007\u001a\u00020\bH\u0007J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0007J\u0012\u0010\u000f\u001a\u00020\u000e2\b\b\u0001\u0010\u0007\u001a\u00020\bH\u0007J\u001a\u0010\u0010\u001a\u00020\u00112\u0006\u0010\r\u001a\u00020\u000e2\b\b\u0001\u0010\u0012\u001a\u00020\u0004H\u0007\u00a8\u0006\u0013"}, d2 = {"Lcom/example/splitexpenses/di/DataModule;", "", "()V", "provideFirebaseDataSource", "Lcom/example/splitexpenses/data/source/DataSource;", "provideLocalDataSource", "Lcom/example/splitexpenses/data/source/LocalDataSource;", "context", "Landroid/content/Context;", "provideNetworkConnectivityManager", "Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;", "provideOfflineDataSource", "Lcom/example/splitexpenses/data/source/OfflineDataSource;", "database", "Lcom/example/splitexpenses/data/cache/SplitExpensesDatabase;", "provideSplitExpensesDatabase", "provideSyncQueueManager", "Lcom/example/splitexpenses/data/sync/SyncQueueManager;", "remoteDataSource", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class DataModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.splitexpenses.di.DataModule INSTANCE = null;
    
    private DataModule() {
        super();
    }
    
    /**
     * Provides the Firebase data source
     * @return The Firebase data source implementation
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @javax.inject.Named(value = "firebase")
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.source.DataSource provideFirebaseDataSource() {
        return null;
    }
    
    /**
     * Provides the local data source
     * @param context The application context
     * @return The local data source implementation
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.source.LocalDataSource provideLocalDataSource(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Provides the network connectivity manager
     * @param context The application context
     * @return The network connectivity manager
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.connectivity.NetworkConnectivityManager provideNetworkConnectivityManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Provides the Room database
     * @param context The application context
     * @return The Room database instance
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.cache.SplitExpensesDatabase provideSplitExpensesDatabase(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Provides the offline data source
     * @param database The Room database
     * @return The offline data source implementation
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @javax.inject.Named(value = "offline")
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.source.OfflineDataSource provideOfflineDataSource(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.SplitExpensesDatabase database) {
        return null;
    }
    
    /**
     * Provides the sync queue manager
     * @param database The Room database
     * @param remoteDataSource The Firebase data source
     * @return The sync queue manager
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.sync.SyncQueueManager provideSyncQueueManager(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.SplitExpensesDatabase database, @javax.inject.Named(value = "firebase")
    @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.source.DataSource remoteDataSource) {
        return null;
    }
}