package com.example.splitexpenses.di;

import android.content.Context;
import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideNetworkConnectivityManagerFactory implements Factory<NetworkConnectivityManager> {
  private final Provider<Context> contextProvider;

  public DataModule_ProvideNetworkConnectivityManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public NetworkConnectivityManager get() {
    return provideNetworkConnectivityManager(contextProvider.get());
  }

  public static DataModule_ProvideNetworkConnectivityManagerFactory create(
      Provider<Context> contextProvider) {
    return new DataModule_ProvideNetworkConnectivityManagerFactory(contextProvider);
  }

  public static NetworkConnectivityManager provideNetworkConnectivityManager(Context context) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideNetworkConnectivityManager(context));
  }
}
