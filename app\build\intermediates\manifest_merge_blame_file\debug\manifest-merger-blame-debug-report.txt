1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.splitexpenses"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
13-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa5a886aff1e44070bb5a30fc19fda22\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
13-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa5a886aff1e44070bb5a30fc19fda22\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
14
15    <permission
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc092afbc3265754b7f229c00d6f1b35\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc092afbc3265754b7f229c00d6f1b35\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc092afbc3265754b7f229c00d6f1b35\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc092afbc3265754b7f229c00d6f1b35\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc092afbc3265754b7f229c00d6f1b35\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:5:5-26:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:6:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc092afbc3265754b7f229c00d6f1b35\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:7:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:8:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:9:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:10:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:11:9-54
31        android:supportsRtl="true"
31-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:12:9-35
32        android:testOnly="true"
33        android:theme="@style/Theme.SplitExpenses" >
33-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:13:9-51
34        <activity
34-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:15:9-25:20
35            android:name="com.example.splitexpenses.MainActivity"
35-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:16:13-41
36            android:exported="true"
36-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:17:13-36
37            android:label="@string/app_name"
37-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:18:13-45
38            android:theme="@style/Theme.SplitExpenses" >
38-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:19:13-55
39            <intent-filter>
39-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:20:13-24:29
40                <action android:name="android.intent.action.MAIN" />
40-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:21:17-69
40-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:21:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:23:17-77
42-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses3\app\src\main\AndroidManifest.xml:23:27-74
43            </intent-filter>
44        </activity>
45        <activity
45-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b89cdf1aa2ad60ea066067e4f4f6a99\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
46            android:name="androidx.compose.ui.tooling.PreviewActivity"
46-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b89cdf1aa2ad60ea066067e4f4f6a99\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
47            android:exported="true" />
47-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b89cdf1aa2ad60ea066067e4f4f6a99\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
48        <activity
48-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e1fcc8706b700b046b8a79c0819e5b1\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
49            android:name="androidx.activity.ComponentActivity"
49-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e1fcc8706b700b046b8a79c0819e5b1\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
50            android:exported="true" />
50-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e1fcc8706b700b046b8a79c0819e5b1\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
51
52        <service
52-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30f3f97d2222e1629d49b532c0e0c555\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:8:9-14:19
53            android:name="com.google.firebase.components.ComponentDiscoveryService"
53-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30f3f97d2222e1629d49b532c0e0c555\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:9:13-84
54            android:directBootAware="true"
54-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
55            android:exported="false" >
55-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30f3f97d2222e1629d49b532c0e0c555\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:10:13-37
56            <meta-data
56-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30f3f97d2222e1629d49b532c0e0c555\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:11:13-13:85
57                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
57-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30f3f97d2222e1629d49b532c0e0c555\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:12:17-119
58                android:value="com.google.firebase.components.ComponentRegistrar" />
58-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30f3f97d2222e1629d49b532c0e0c555\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:13:17-82
59            <meta-data
59-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
60                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
60-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
61                android:value="com.google.firebase.components.ComponentRegistrar" />
61-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
62            <meta-data
62-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6cb7dfe98d6d9a4018f83721975d9ea2\transformed\firebase-database-ktx-20.3.0\AndroidManifest.xml:12:13-14:85
63                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
63-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6cb7dfe98d6d9a4018f83721975d9ea2\transformed\firebase-database-ktx-20.3.0\AndroidManifest.xml:13:17-127
64                android:value="com.google.firebase.components.ComponentRegistrar" />
64-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6cb7dfe98d6d9a4018f83721975d9ea2\transformed\firebase-database-ktx-20.3.0\AndroidManifest.xml:14:17-82
65            <meta-data
65-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6bdd091c40beb0da87bf99f92af5572\transformed\firebase-database-20.3.0\AndroidManifest.xml:29:13-31:85
66                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
66-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6bdd091c40beb0da87bf99f92af5572\transformed\firebase-database-20.3.0\AndroidManifest.xml:30:17-120
67                android:value="com.google.firebase.components.ComponentRegistrar" />
67-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6bdd091c40beb0da87bf99f92af5572\transformed\firebase-database-20.3.0\AndroidManifest.xml:31:17-82
68            <meta-data
68-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6bdd091c40beb0da87bf99f92af5572\transformed\firebase-database-20.3.0\AndroidManifest.xml:32:13-34:85
69                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
69-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6bdd091c40beb0da87bf99f92af5572\transformed\firebase-database-20.3.0\AndroidManifest.xml:33:17-109
70                android:value="com.google.firebase.components.ComponentRegistrar" />
70-->[com.google.firebase:firebase-database:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6bdd091c40beb0da87bf99f92af5572\transformed\firebase-database-20.3.0\AndroidManifest.xml:34:17-82
71            <meta-data
71-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe39b96931c6c3c47a772e072e7ef81d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
72                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
72-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe39b96931c6c3c47a772e072e7ef81d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
73                android:value="com.google.firebase.components.ComponentRegistrar" />
73-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe39b96931c6c3c47a772e072e7ef81d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
74            <meta-data
74-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
75                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
75-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
76                android:value="com.google.firebase.components.ComponentRegistrar" />
76-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
77        </service>
78
79        <activity
79-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
80            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
80-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
81            android:excludeFromRecents="true"
81-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
82            android:exported="true"
82-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
83            android:launchMode="singleTask"
83-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
84            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
84-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
85            <intent-filter>
85-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
86                <action android:name="android.intent.action.VIEW" />
86-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
86-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
87
88                <category android:name="android.intent.category.DEFAULT" />
88-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
88-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
89                <category android:name="android.intent.category.BROWSABLE" />
89-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
89-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
90
91                <data
91-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
92                    android:host="firebase.auth"
92-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
93                    android:path="/"
93-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
94                    android:scheme="genericidp" />
94-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
95            </intent-filter>
96        </activity>
97        <activity
97-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
98            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
98-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
99            android:excludeFromRecents="true"
99-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
100            android:exported="true"
100-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
101            android:launchMode="singleTask"
101-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
102            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
102-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
103            <intent-filter>
103-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
104                <action android:name="android.intent.action.VIEW" />
104-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
104-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
105
106                <category android:name="android.intent.category.DEFAULT" />
106-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
106-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
107                <category android:name="android.intent.category.BROWSABLE" />
107-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
107-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
108
109                <data
109-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
110                    android:host="firebase.auth"
110-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
111                    android:path="/"
111-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
112                    android:scheme="recaptcha" />
112-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc34edc55df932abf8c9cb6497e5b07\transformed\firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
113            </intent-filter>
114        </activity>
115
116        <provider
116-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
117            android:name="com.google.firebase.provider.FirebaseInitProvider"
117-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
118            android:authorities="com.example.splitexpenses.firebaseinitprovider"
118-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
119            android:directBootAware="true"
119-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
120            android:exported="false"
120-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
121            android:initOrder="100" />
121-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e972808947c3ec68cd9a2024126781\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
122
123        <activity
123-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e3de49936999f043c9f33d3c2681798\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
124            android:name="com.google.android.gms.common.api.GoogleApiActivity"
124-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e3de49936999f043c9f33d3c2681798\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
125            android:exported="false"
125-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e3de49936999f043c9f33d3c2681798\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
126            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
126-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e3de49936999f043c9f33d3c2681798\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
127
128        <provider
128-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91fc4fd0c8b0536260c20ff4bef5c40d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
129            android:name="androidx.startup.InitializationProvider"
129-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91fc4fd0c8b0536260c20ff4bef5c40d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
130            android:authorities="com.example.splitexpenses.androidx-startup"
130-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91fc4fd0c8b0536260c20ff4bef5c40d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
131            android:exported="false" >
131-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91fc4fd0c8b0536260c20ff4bef5c40d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
132            <meta-data
132-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91fc4fd0c8b0536260c20ff4bef5c40d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.emoji2.text.EmojiCompatInitializer"
133-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91fc4fd0c8b0536260c20ff4bef5c40d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
134                android:value="androidx.startup" />
134-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91fc4fd0c8b0536260c20ff4bef5c40d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25b8c9e56e3d6e035a7e023ac442f0ad\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
136-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25b8c9e56e3d6e035a7e023ac442f0ad\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
137                android:value="androidx.startup" />
137-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25b8c9e56e3d6e035a7e023ac442f0ad\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
138            <meta-data
138-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
139                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
140                android:value="androidx.startup" />
140-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
141        </provider>
142
143        <meta-data
143-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba70e4be3125798d66c67f23c602941e\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
144            android:name="com.google.android.gms.version"
144-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba70e4be3125798d66c67f23c602941e\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
145            android:value="@integer/google_play_services_version" />
145-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba70e4be3125798d66c67f23c602941e\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
146
147        <receiver
147-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
148            android:name="androidx.profileinstaller.ProfileInstallReceiver"
148-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
149            android:directBootAware="false"
149-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
150            android:enabled="true"
150-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
151            android:exported="true"
151-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
152            android:permission="android.permission.DUMP" >
152-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
154                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
154-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
154-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
155            </intent-filter>
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
157                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
157-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
157-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
160                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
160-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
160-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
161            </intent-filter>
162            <intent-filter>
162-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
163                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
163-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
163-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38639b6b71924c93ab47938ef3de0e99\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
164            </intent-filter>
165        </receiver>
166    </application>
167
168</manifest>
