{"logs": [{"outputFile": "com.example.splitexpenses.app-mergeDebugResources-52:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SplitExpenses3\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "90", "endOffsets": "142"}, "to": {"startLines": "1856", "startColumns": "4", "startOffsets": "124270", "endColumns": "89", "endOffsets": "124355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4b93732545937544d33791981782409f\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "29,30,31,32,176,177,357,361,362,363", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2065,2123,2189,2252,11844,11915,24401,24694,24761,24840", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2118,2184,2247,2309,11910,11982,24464,24756,24835,24904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0e3de49936999f043c9f33d3c2681798\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "37,38,39,40,41,42,43,44,339,340,341,342,343,344,345,346,348,349,350,351,352,353,354,355,356,2839,3068", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2560,2650,2730,2820,2910,2990,3071,3151,22068,22173,22354,22479,22586,22766,22889,23005,23275,23463,23568,23749,23874,24049,24197,24260,24322,162883,170139", "endLines": "37,38,39,40,41,42,43,44,339,340,341,342,343,344,345,346,348,349,350,351,352,353,354,355,356,2851,3086", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2645,2725,2815,2905,2985,3066,3146,3226,22168,22349,22474,22581,22761,22884,23000,23103,23458,23563,23744,23869,24044,24192,24255,24317,24396,163193,170551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d61303a38bace0aaebbce3cdfd9d6620\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "269,290", "startColumns": "4,4", "startOffsets": "17689,18776", "endColumns": "41,59", "endOffsets": "17726,18831"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SplitExpenses3\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "22,77,78,79,90,91,94", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1497,5304,5351,5398,6142,6187,6353", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1534,5346,5393,5440,6182,6227,6390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c289649cf1789b549ce4631328d0d5\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "259,270,293,2755,2760", "startColumns": "4,4,4,4,4", "startOffsets": "17205,17731,18940,160502,160672", "endLines": "259,270,293,2759,2763", "endColumns": "56,64,63,24,24", "endOffsets": "17257,17791,18999,160667,160816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ad447dfb771b12c96448a33b3f3c5256\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "292", "startColumns": "4", "startOffsets": "18890", "endColumns": "49", "endOffsets": "18935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a52a33b73b656582f91bade801587d5\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "328", "startColumns": "4", "startOffsets": "21304", "endColumns": "82", "endOffsets": "21382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\788b752142643fa42217929c5ddc0860\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,256,257,258,260,262,294,337,338,358,359,360,364,365,426,427,428,429,430,431,433,435,436,437,1528,1531,1534", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14903,14962,15021,15081,15141,15201,15261,15321,15381,15441,15501,15561,15621,15680,15740,15800,15860,15920,15980,16040,16100,16160,16220,16280,16339,16399,16459,16518,16577,16636,16695,16754,17018,17092,17150,17262,17347,19004,21949,22014,24469,24535,24636,24909,24961,29388,29450,29504,29540,29574,29624,29731,29848,29895,29931,99572,99684,99795", "endLines": "220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,256,257,258,260,262,294,337,338,358,359,360,364,365,426,427,428,429,430,431,433,435,436,437,1530,1533,1537", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "14957,15016,15076,15136,15196,15256,15316,15376,15436,15496,15556,15616,15675,15735,15795,15855,15915,15975,16035,16095,16155,16215,16275,16334,16394,16454,16513,16572,16631,16690,16749,16808,17087,17145,17200,17308,17397,19052,22009,22063,24530,24631,24689,24956,25016,29445,29499,29535,29569,29619,29673,29772,29890,29926,30016,99679,99790,99985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc092afbc3265754b7f229c00d6f1b35\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "5,16,17,35,36,67,68,178,179,180,181,182,183,184,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,217,218,219,264,265,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,300,330,331,332,333,334,335,336,434,1809,1810,1814,1815,1819,1964,1965,2611,2645,2701,2734,2764,2797", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1018,1090,2429,2494,4584,4653,11987,12057,12125,12197,12267,12328,12402,13259,13320,13381,13443,13507,13569,13630,13698,13798,13858,13924,13997,14066,14123,14175,14690,14762,14838,17456,17491,17842,17897,17960,18015,18073,18131,18192,18255,18312,18363,18413,18474,18531,18597,18631,18666,19368,21438,21505,21577,21646,21715,21789,21861,29777,120876,120993,121194,121304,121505,133019,133091,154287,155860,158090,159821,160821,161503", "endLines": "5,16,17,35,36,67,68,178,179,180,181,182,183,184,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,217,218,219,264,265,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,300,330,331,332,333,334,335,336,434,1809,1813,1814,1818,1819,1964,1965,2616,2654,2733,2754,2796,2802", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1085,1173,2489,2555,4648,4711,12052,12120,12192,12262,12323,12397,12470,13315,13376,13438,13502,13564,13625,13693,13793,13853,13919,13992,14061,14118,14170,14232,14757,14833,14898,17486,17521,17892,17955,18010,18068,18126,18187,18250,18307,18358,18408,18469,18526,18592,18626,18661,18696,19433,21500,21572,21641,21710,21784,21856,21944,29843,120988,121189,121299,121500,121629,133086,133153,154485,156156,159816,160497,161498,161665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c48d49745c1352589e08245207f01c02\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,23,24,25,26,27,28,33,34,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,185,186,187,188,189,190,191,192,193,209,210,211,212,213,214,215,216,252,253,254,255,261,267,268,271,288,295,296,297,298,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,432,440,441,442,443,444,445,453,454,458,462,466,471,477,484,488,492,497,501,505,509,513,517,521,527,531,537,541,547,551,556,560,563,567,573,577,583,587,593,596,600,604,608,612,616,617,618,619,622,625,628,631,635,636,637,638,639,642,644,646,648,653,654,658,664,668,669,671,683,684,688,694,698,699,700,704,731,735,736,740,768,940,966,1137,1163,1194,1202,1208,1224,1246,1251,1256,1266,1275,1284,1288,1295,1314,1321,1322,1331,1334,1337,1341,1345,1349,1352,1353,1358,1363,1373,1378,1385,1391,1392,1395,1399,1404,1406,1408,1411,1414,1416,1420,1423,1430,1433,1436,1440,1442,1446,1448,1450,1452,1456,1464,1472,1484,1490,1499,1502,1513,1516,1517,1522,1523,1538,1607,1677,1678,1688,1697,1698,1700,1704,1707,1710,1713,1716,1719,1722,1725,1729,1732,1735,1738,1742,1745,1749,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1775,1777,1778,1779,1780,1781,1782,1783,1784,1786,1787,1789,1790,1792,1794,1795,1797,1798,1799,1800,1801,1802,1804,1805,1806,1807,1808,1820,1822,1824,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1840,1841,1842,1843,1844,1845,1846,1848,1852,1857,1858,1859,1860,1861,1862,1866,1867,1868,1869,1871,1873,1875,1877,1879,1880,1881,1882,1884,1886,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1902,1903,1904,1905,1907,1909,1910,1912,1913,1915,1917,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1932,1933,1934,1935,1937,1938,1939,1940,1941,1943,1945,1947,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1966,2041,2044,2047,2050,2064,2070,2112,2115,2144,2171,2180,2244,2607,2617,2655,2683,2803,2827,2833,2852,2873,2997,3017,3023,3027,3033,3087,3119,3185,3205,3260,3272,3298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,359,400,455,517,581,651,712,787,863,940,1178,1263,1345,1421,1539,1616,1694,1800,1906,1985,2314,2371,3231,3305,3380,3445,3511,3571,3632,3704,3777,3844,3912,3971,4030,4089,4148,4207,4261,4315,4368,4422,4476,4530,4716,4790,4869,4942,5016,5087,5159,5231,5445,5502,5560,5633,5707,5781,5856,5928,6001,6071,6232,6292,6395,6464,6533,6603,6677,6753,6817,6894,6970,7047,7112,7181,7258,7333,7402,7470,7547,7613,7674,7771,7836,7905,8004,8075,8134,8192,8249,8308,8372,8443,8515,8587,8659,8731,8798,8866,8934,8993,9056,9120,9210,9301,9361,9427,9494,9560,9630,9694,9747,9814,9875,9942,10055,10113,10176,10241,10306,10381,10454,10526,10570,10617,10663,10712,10773,10834,10895,10957,11021,11085,11149,11214,11277,11337,11398,11464,11523,11583,11645,11716,11776,12475,12561,12648,12738,12825,12913,12995,13078,13168,14237,14289,14347,14392,14458,14522,14579,14636,16813,16870,16918,16967,17313,17593,17640,17796,18701,19057,19121,19183,19243,19438,19512,19582,19660,19714,19784,19869,19917,19963,20024,20087,20153,20217,20288,20351,20416,20480,20541,20602,20654,20727,20801,20870,20945,21019,21093,21234,29678,30132,30210,30300,30388,30484,30574,31156,31245,31492,31773,32025,32310,32703,33180,33402,33624,33900,34127,34357,34587,34817,35047,35274,35693,35919,36344,36574,37002,37221,37504,37712,37843,38070,38496,38721,39148,39369,39794,39914,40190,40491,40815,41106,41420,41557,41688,41793,42035,42202,42406,42614,42885,42997,43109,43214,43331,43545,43691,43831,43917,44265,44353,44599,45017,45266,45348,45446,46103,46203,46455,46879,47134,47228,47317,47554,49578,49820,49922,50175,52331,63012,64528,75223,76751,78508,79134,79554,80815,82080,82336,82572,83119,83613,84218,84416,84996,86364,86739,86857,87395,87552,87748,88021,88277,88447,88588,88652,89017,89384,90060,90324,90662,91015,91109,91295,91601,91863,91988,92115,92354,92565,92684,92877,93054,93509,93690,93812,94071,94184,94371,94473,94580,94709,94984,95492,95988,96865,97159,97729,97878,98610,98782,98866,99202,99294,99990,105221,110592,110654,111232,111816,111907,112020,112249,112409,112561,112732,112898,113067,113234,113397,113640,113810,113983,114154,114428,114627,114832,115162,115246,115342,115438,115536,115636,115738,115840,115942,116044,116146,116246,116342,116454,116583,116706,116837,116968,117066,117180,117274,117414,117548,117644,117756,117856,117972,118068,118180,118280,118420,118556,118720,118850,119008,119158,119299,119443,119578,119690,119840,119968,120096,120232,120364,120494,120624,120736,121634,121780,121924,122062,122128,122218,122294,122398,122488,122590,122698,122806,122906,122986,123078,123176,123286,123338,123416,123522,123614,123718,123828,123950,124113,124360,124440,124540,124630,124740,124830,125071,125165,125271,125363,125463,125575,125689,125805,125921,126015,126129,126241,126343,126463,126585,126667,126771,126891,127017,127115,127209,127297,127409,127525,127647,127759,127934,128050,128136,128228,128340,128464,128531,128657,128725,128853,128997,129125,129194,129289,129404,129517,129616,129725,129836,129947,130048,130153,130253,130383,130474,130597,130691,130803,130889,130993,131089,131177,131295,131399,131503,131629,131717,131825,131925,132015,132125,132209,132311,132395,132449,132513,132619,132705,132815,132899,133158,135774,135892,136007,136087,136448,136681,138085,138163,139507,140868,141256,144099,154152,154490,156161,157518,161670,162421,162683,163198,163577,167855,168461,168690,168841,169056,170556,171406,174432,175176,177307,177647,178958", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,23,24,25,26,27,28,33,34,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,185,186,187,188,189,190,191,192,193,209,210,211,212,213,214,215,216,252,253,254,255,261,267,268,271,288,295,296,297,298,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,432,440,441,442,443,444,452,453,457,461,465,470,476,483,487,491,496,500,504,508,512,516,520,526,530,536,540,546,550,555,559,562,566,572,576,582,586,592,595,599,603,607,611,615,616,617,618,621,624,627,630,634,635,636,637,638,641,643,645,647,652,653,657,663,667,668,670,682,683,687,693,697,698,699,703,730,734,735,739,767,939,965,1136,1162,1193,1201,1207,1223,1245,1250,1255,1265,1274,1283,1287,1294,1313,1320,1321,1330,1333,1336,1340,1344,1348,1351,1352,1357,1362,1372,1377,1384,1390,1391,1394,1398,1403,1405,1407,1410,1413,1415,1419,1422,1429,1432,1435,1439,1441,1445,1447,1449,1451,1455,1463,1471,1483,1489,1498,1501,1512,1515,1516,1521,1522,1527,1606,1676,1677,1687,1696,1697,1699,1703,1706,1709,1712,1715,1718,1721,1724,1728,1731,1734,1737,1741,1744,1748,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1774,1776,1777,1778,1779,1780,1781,1782,1783,1785,1786,1788,1789,1791,1793,1794,1796,1797,1798,1799,1800,1801,1803,1804,1805,1806,1807,1808,1821,1823,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1839,1840,1841,1842,1843,1844,1845,1847,1851,1855,1857,1858,1859,1860,1861,1865,1866,1867,1868,1870,1872,1874,1876,1878,1879,1880,1881,1883,1885,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1901,1902,1903,1904,1906,1908,1909,1911,1912,1914,1916,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1931,1932,1933,1934,1936,1937,1938,1939,1940,1942,1944,1946,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,2040,2043,2046,2049,2063,2069,2079,2114,2143,2170,2179,2243,2606,2610,2644,2682,2700,2826,2832,2838,2872,2996,3016,3022,3026,3032,3067,3098,3184,3204,3259,3271,3297,3304", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,395,450,512,576,646,707,782,858,935,1013,1258,1340,1416,1492,1611,1689,1795,1901,1980,2060,2366,2424,3300,3375,3440,3506,3566,3627,3699,3772,3839,3907,3966,4025,4084,4143,4202,4256,4310,4363,4417,4471,4525,4579,4785,4864,4937,5011,5082,5154,5226,5299,5497,5555,5628,5702,5776,5851,5923,5996,6066,6137,6287,6348,6459,6528,6598,6672,6748,6812,6889,6965,7042,7107,7176,7253,7328,7397,7465,7542,7608,7669,7766,7831,7900,7999,8070,8129,8187,8244,8303,8367,8438,8510,8582,8654,8726,8793,8861,8929,8988,9051,9115,9205,9296,9356,9422,9489,9555,9625,9689,9742,9809,9870,9937,10050,10108,10171,10236,10301,10376,10449,10521,10565,10612,10658,10707,10768,10829,10890,10952,11016,11080,11144,11209,11272,11332,11393,11459,11518,11578,11640,11711,11771,11839,12556,12643,12733,12820,12908,12990,13073,13163,13254,14284,14342,14387,14453,14517,14574,14631,14685,16865,16913,16962,17013,17342,17635,17684,17837,18728,19116,19178,19238,19295,19507,19577,19655,19709,19779,19864,19912,19958,20019,20082,20148,20212,20283,20346,20411,20475,20536,20597,20649,20722,20796,20865,20940,21014,21088,21229,21299,29726,30205,30295,30383,30479,30569,31151,31240,31487,31768,32020,32305,32698,33175,33397,33619,33895,34122,34352,34582,34812,35042,35269,35688,35914,36339,36569,36997,37216,37499,37707,37838,38065,38491,38716,39143,39364,39789,39909,40185,40486,40810,41101,41415,41552,41683,41788,42030,42197,42401,42609,42880,42992,43104,43209,43326,43540,43686,43826,43912,44260,44348,44594,45012,45261,45343,45441,46098,46198,46450,46874,47129,47223,47312,47549,49573,49815,49917,50170,52326,63007,64523,75218,76746,78503,79129,79549,80810,82075,82331,82567,83114,83608,84213,84411,84991,86359,86734,86852,87390,87547,87743,88016,88272,88442,88583,88647,89012,89379,90055,90319,90657,91010,91104,91290,91596,91858,91983,92110,92349,92560,92679,92872,93049,93504,93685,93807,94066,94179,94366,94468,94575,94704,94979,95487,95983,96860,97154,97724,97873,98605,98777,98861,99197,99289,99567,105216,110587,110649,111227,111811,111902,112015,112244,112404,112556,112727,112893,113062,113229,113392,113635,113805,113978,114149,114423,114622,114827,115157,115241,115337,115433,115531,115631,115733,115835,115937,116039,116141,116241,116337,116449,116578,116701,116832,116963,117061,117175,117269,117409,117543,117639,117751,117851,117967,118063,118175,118275,118415,118551,118715,118845,119003,119153,119294,119438,119573,119685,119835,119963,120091,120227,120359,120489,120619,120731,120871,121775,121919,122057,122123,122213,122289,122393,122483,122585,122693,122801,122901,122981,123073,123171,123281,123333,123411,123517,123609,123713,123823,123945,124108,124265,124435,124535,124625,124735,124825,125066,125160,125266,125358,125458,125570,125684,125800,125916,126010,126124,126236,126338,126458,126580,126662,126766,126886,127012,127110,127204,127292,127404,127520,127642,127754,127929,128045,128131,128223,128335,128459,128526,128652,128720,128848,128992,129120,129189,129284,129399,129512,129611,129720,129831,129942,130043,130148,130248,130378,130469,130592,130686,130798,130884,130988,131084,131172,131290,131394,131498,131624,131712,131820,131920,132010,132120,132204,132306,132390,132444,132508,132614,132700,132810,132894,133014,135769,135887,136002,136082,136443,136676,137193,138158,139502,140863,141251,144094,154147,154282,155855,157513,158085,162416,162678,162878,163572,167850,168456,168685,168836,169051,170134,170863,174427,175171,177302,177642,178953,179156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ba70e4be3125798d66c67f23c602941e\\transformed\\play-services-basement-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "299,347", "startColumns": "4,4", "startOffsets": "19300,23108", "endColumns": "67,166", "endOffsets": "19363,23270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dd1b88358a5da69ffb1cdeb6351a3852\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "289", "startColumns": "4", "startOffsets": "18733", "endColumns": "42", "endOffsets": "18771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\15fafe37c4de2582e43cc388fbc271ac\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "438,439", "startColumns": "4,4", "startOffsets": "30021,30077", "endColumns": "55,54", "endOffsets": "30072,30127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da9a0bc96c440be9eadea6899b8425ef\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2080,2096,2102,3099,3115", "startColumns": "4,4,4,4,4", "startOffsets": "137198,137623,137801,170868,171279", "endLines": "2095,2101,2111,3114,3118", "endColumns": "24,24,24,24,24", "endOffsets": "137618,137796,138080,171274,171401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e0240389a8e9ad5ff064a853fded58c7\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "366,367,368,369,370,371,372,373,374,375,378,379,380,381,382,383,384,385,386,387,388,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25021,25109,25195,25276,25360,25429,25494,25577,25683,25769,25889,25943,26012,26073,26142,26231,26326,26400,26497,26590,26688,26837,26928,27016,27112,27210,27274,27342,27429,27523,27590,27662,27734,27835,27944,28020,28089,28137,28203,28267,28324,28381,28453,28503,28557,28628,28699,28769,28838,28896,28972,29043,29117,29203,29253,29323", "endLines": "366,367,368,369,370,371,372,373,374,377,378,379,380,381,382,383,384,385,386,387,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "25104,25190,25271,25355,25424,25489,25572,25678,25764,25884,25938,26007,26068,26137,26226,26321,26395,26492,26585,26683,26832,26923,27011,27107,27205,27269,27337,27424,27518,27585,27657,27729,27830,27939,28015,28084,28132,28198,28262,28319,28376,28448,28498,28552,28623,28694,28764,28833,28891,28967,29038,29112,29198,29248,29318,29383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fab608c512b41d4bbff20871e4e010d2\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "263,266", "startColumns": "4,4", "startOffsets": "17402,17526", "endColumns": "53,66", "endOffsets": "17451,17588"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SplitExpenses3\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "50", "endOffsets": "62"}, "to": {"startLines": "329", "startColumns": "4", "startOffsets": "21387", "endColumns": "50", "endOffsets": "21433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\80ace2eda25eba1deb71f2c0dad7bcd6\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "291", "startColumns": "4", "startOffsets": "18836", "endColumns": "53", "endOffsets": "18885"}}]}]}