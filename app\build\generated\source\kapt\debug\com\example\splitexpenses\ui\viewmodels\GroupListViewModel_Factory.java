package com.example.splitexpenses.ui.viewmodels;

import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager;
import com.example.splitexpenses.data.repositories.GroupRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GroupListViewModel_Factory implements Factory<GroupListViewModel> {
  private final Provider<GroupRepository> groupRepositoryProvider;

  private final Provider<NetworkConnectivityManager> connectivityManagerProvider;

  public GroupListViewModel_Factory(Provider<GroupRepository> groupRepositoryProvider,
      Provider<NetworkConnectivityManager> connectivityManagerProvider) {
    this.groupRepositoryProvider = groupRepositoryProvider;
    this.connectivityManagerProvider = connectivityManagerProvider;
  }

  @Override
  public GroupListViewModel get() {
    return newInstance(groupRepositoryProvider.get(), connectivityManagerProvider.get());
  }

  public static GroupListViewModel_Factory create(Provider<GroupRepository> groupRepositoryProvider,
      Provider<NetworkConnectivityManager> connectivityManagerProvider) {
    return new GroupListViewModel_Factory(groupRepositoryProvider, connectivityManagerProvider);
  }

  public static GroupListViewModel newInstance(GroupRepository groupRepository,
      NetworkConnectivityManager connectivityManager) {
    return new GroupListViewModel(groupRepository, connectivityManager);
  }
}
