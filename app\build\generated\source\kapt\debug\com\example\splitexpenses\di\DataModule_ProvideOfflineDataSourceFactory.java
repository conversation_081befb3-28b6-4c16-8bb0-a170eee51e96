package com.example.splitexpenses.di;

import com.example.splitexpenses.data.cache.SplitExpensesDatabase;
import com.example.splitexpenses.data.source.OfflineDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideOfflineDataSourceFactory implements Factory<OfflineDataSource> {
  private final Provider<SplitExpensesDatabase> databaseProvider;

  public DataModule_ProvideOfflineDataSourceFactory(
      Provider<SplitExpensesDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public OfflineDataSource get() {
    return provideOfflineDataSource(databaseProvider.get());
  }

  public static DataModule_ProvideOfflineDataSourceFactory create(
      Provider<SplitExpensesDatabase> databaseProvider) {
    return new DataModule_ProvideOfflineDataSourceFactory(databaseProvider);
  }

  public static OfflineDataSource provideOfflineDataSource(SplitExpensesDatabase database) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideOfflineDataSource(database));
  }
}
