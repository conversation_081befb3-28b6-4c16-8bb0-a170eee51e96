package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000H\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u00ae\u0002\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0018\u0010\n\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00120\u00052\u0006\u0010\u0013\u001a\u00020\u00122\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0018\u0010\u0016\u001a\u0014\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010\u00172\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u0019\u001a\u00020\u00122\u000e\b\u0002\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u001b\u001a\u00020\u00122\b\b\u0002\u0010\u001c\u001a\u00020\u001d2\u0014\b\u0002\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006\u001f"}, d2 = {"ExpenseListScreen", "", "group", "Lcom/example/splitexpenses/data/GroupData;", "onExpenseClick", "Lkotlin/Function1;", "Lcom/example/splitexpenses/data/Expense;", "onShowBalanceDetailsClick", "Lkotlin/Function0;", "onAddExpenseClick", "onDeleteExpense", "", "", "onShowStatisticsClick", "onShowManageMembersClick", "onShowDeleteGroupDialog", "onExportToCsv", "Ljava/io/OutputStream;", "", "isMultiSelectMode", "onMultiSelectModeChange", "selectedExpenses", "onSelectedExpensesChange", "Lkotlin/Function2;", "onNavigateToManageCategories", "isCurrentUserGroupCreator", "onEditGroupName", "isOffline", "filterState", "Lcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;", "onFilterStateChange", "app_debug"})
public final class ExpenseListScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void ExpenseListScreen(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.splitexpenses.data.Expense, kotlin.Unit> onExpenseClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowBalanceDetailsClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddExpenseClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.Set<java.lang.String>, kotlin.Unit> onDeleteExpense, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowStatisticsClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowManageMembersClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowDeleteGroupDialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.io.OutputStream, java.lang.Boolean> onExportToCsv, boolean isMultiSelectMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onMultiSelectModeChange, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedExpenses, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> onSelectedExpensesChange, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToManageCategories, boolean isCurrentUserGroupCreator, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditGroupName, boolean isOffline, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.ExpenseFilterState filterState, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.splitexpenses.ui.viewmodels.ExpenseFilterState, kotlin.Unit> onFilterStateChange) {
    }
}