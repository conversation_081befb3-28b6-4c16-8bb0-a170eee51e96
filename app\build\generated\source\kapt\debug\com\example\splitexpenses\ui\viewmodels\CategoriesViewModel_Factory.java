package com.example.splitexpenses.ui.viewmodels;

import com.example.splitexpenses.data.repositories.GroupRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CategoriesViewModel_Factory implements Factory<CategoriesViewModel> {
  private final Provider<GroupRepository> groupRepositoryProvider;

  public CategoriesViewModel_Factory(Provider<GroupRepository> groupRepositoryProvider) {
    this.groupRepositoryProvider = groupRepositoryProvider;
  }

  @Override
  public CategoriesViewModel get() {
    return newInstance(groupRepositoryProvider.get());
  }

  public static CategoriesViewModel_Factory create(
      Provider<GroupRepository> groupRepositoryProvider) {
    return new CategoriesViewModel_Factory(groupRepositoryProvider);
  }

  public static CategoriesViewModel newInstance(GroupRepository groupRepository) {
    return new CategoriesViewModel(groupRepository);
  }
}
