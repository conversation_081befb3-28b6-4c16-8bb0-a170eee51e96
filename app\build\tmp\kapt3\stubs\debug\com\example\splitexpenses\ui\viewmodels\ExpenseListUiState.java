package com.example.splitexpenses.ui.viewmodels;

/**
 * UI state for the expense list screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B?\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\u0006\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0002\u0010\u000bJ\u000f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003JC\u0010\u0018\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\u00062\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0004H\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u00062\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0004H\u00d6\u0001R\u0016\u0010\n\u001a\u0004\u0018\u00010\u0004X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0014\u0010\t\u001a\u00020\u0006X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0010R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0010R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001f"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/ExpenseListUiState;", "Lcom/example/splitexpenses/ui/viewmodels/UiState;", "selectedExpenses", "", "", "isMultiSelectMode", "", "filterState", "Lcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;", "isLoading", "error", "(Ljava/util/Set;ZLcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;ZLjava/lang/String;)V", "getError", "()Ljava/lang/String;", "getFilterState", "()Lcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;", "()Z", "getSelectedExpenses", "()Ljava/util/Set;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "", "hashCode", "", "toString", "app_debug"})
public final class ExpenseListUiState implements com.example.splitexpenses.ui.viewmodels.UiState {
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> selectedExpenses = null;
    private final boolean isMultiSelectMode = false;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.ui.viewmodels.ExpenseFilterState filterState = null;
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String error = null;
    
    public ExpenseListUiState(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedExpenses, boolean isMultiSelectMode, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.ExpenseFilterState filterState, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> getSelectedExpenses() {
        return null;
    }
    
    public final boolean isMultiSelectMode() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.ui.viewmodels.ExpenseFilterState getFilterState() {
        return null;
    }
    
    @java.lang.Override()
    public boolean isLoading() {
        return false;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.String getError() {
        return null;
    }
    
    public ExpenseListUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.ui.viewmodels.ExpenseFilterState component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.ui.viewmodels.ExpenseListUiState copy(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedExpenses, boolean isMultiSelectMode, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.ExpenseFilterState filterState, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}