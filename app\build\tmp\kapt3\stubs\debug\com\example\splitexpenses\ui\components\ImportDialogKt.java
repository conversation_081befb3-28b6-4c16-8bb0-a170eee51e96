package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\u001aG\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032*\u0010\u0004\u001a&\b\u0001\u0012\u0004\u0012\u00020\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0012\u0006\u0012\u0004\u0018\u00010\n0\u0005H\u0007\u00a2\u0006\u0002\u0010\u000b\u00a8\u0006\f"}, d2 = {"ImportDialog", "", "onDismiss", "Lkotlin/Function0;", "onImport", "Lkotlin/Function3;", "Landroid/net/Uri;", "", "Lkotlin/coroutines/Continuation;", "Lcom/example/splitexpenses/util/CsvImportResult;", "", "(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function3;)V", "app_debug"})
public final class ImportDialogKt {
    
    /**
     * Dialog for importing group data from CSV
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ImportDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super android.net.Uri, ? super java.lang.String, ? super kotlin.coroutines.Continuation<? super com.example.splitexpenses.util.CsvImportResult>, ? extends java.lang.Object> onImport) {
    }
}