package com.example.splitexpenses.data.cache.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.example.splitexpenses.data.Category
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Room entity for storing category data locally
 */
@Entity(tableName = "categories")
@TypeConverters(CategoryEntity.Converters::class)
data class CategoryEntity(
    @PrimaryKey
    val id: String, // Composite key: groupId + categoryName
    val groupId: String,
    val name: String,
    val emoji: String,
    val keywords: List<String>,
    val lastModified: Long = System.currentTimeMillis(),
    val isSynced: Boolean = true
) {
    companion object {
        /**
         * Convert Category to CategoryEntity
         */
        fun fromCategory(category: Category, groupId: String, isSynced: Boolean = true): CategoryEntity {
            return CategoryEntity(
                id = "${groupId}_${category.name}",
                groupId = groupId,
                name = category.name,
                emoji = category.emoji,
                keywords = category.keywords,
                lastModified = System.currentTimeMillis(),
                isSynced = isSynced
            )
        }
    }

    /**
     * Convert CategoryEntity to Category
     */
    fun toCategory(): Category {
        return Category(
            name = name,
            emoji = emoji,
            keywords = keywords
        )
    }

    /**
     * Type converters for Room database
     */
    class Converters {
        private val gson = Gson()

        @TypeConverter
        fun fromStringList(value: List<String>): String {
            return gson.toJson(value)
        }

        @TypeConverter
        fun toStringList(value: String): List<String> {
            val listType = object : TypeToken<List<String>>() {}.type
            return gson.fromJson(value, listType)
        }
    }
}
