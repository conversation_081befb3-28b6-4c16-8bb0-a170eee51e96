package com.example.splitexpenses.ui.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u001e\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001*\b\u0012\u0004\u0012\u00020\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"applyFilters", "", "Lcom/example/splitexpenses/data/Expense;", "filterState", "Lcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;", "app_debug"})
public final class ExpenseFilterStateKt {
    
    /**
     * Extension functions for filtering expenses (matching Statistics screen logic exactly)
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.example.splitexpenses.data.Expense> applyFilters(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Expense> $this$applyFilters, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.ExpenseFilterState filterState) {
        return null;
    }
}