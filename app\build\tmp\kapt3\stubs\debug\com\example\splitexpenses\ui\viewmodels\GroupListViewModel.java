package com.example.splitexpenses.ui.viewmodels;

/**
 * ViewModel for the group list screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\"\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J,\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u000f2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000f0\n2\u0006\u0010\u0019\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u001aJ\b\u0010\u001b\u001a\u00020\u0002H\u0016J\u0014\u0010\u001c\u001a\u00020\u001d2\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u001fJ.\u0010 \u001a\u001a\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\n\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\n0!2\u0006\u0010\"\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010#J\u0010\u0010$\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\"\u001a\u00020\u000fJ\u001c\u0010%\u001a\b\u0012\u0004\u0012\u00020\u000f0\n2\u0006\u0010\"\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010#J\"\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020)2\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u000fH\u0086@\u00a2\u0006\u0002\u0010*J\u000e\u0010+\u001a\u00020\u00122\u0006\u0010\"\u001a\u00020\u000fJ\u0016\u0010,\u001a\u00020\u001d2\u0006\u0010\"\u001a\u00020\u000f2\u0006\u0010-\u001a\u00020\u000fJ\b\u0010.\u001a\u00020\u001dH\u0014J\u000e\u0010/\u001a\u00020\u001d2\u0006\u00100\u001a\u00020\u0012J\u0016\u00101\u001a\u00020\u001d2\u0006\u0010\"\u001a\u00020\u000f2\u0006\u00102\u001a\u00020\u0012R\u001d\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\r\u00a8\u00063"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/GroupListViewModel;", "Lcom/example/splitexpenses/ui/viewmodels/OfflineAwareViewModel;", "Lcom/example/splitexpenses/ui/viewmodels/GroupListUiState;", "groupRepository", "Lcom/example/splitexpenses/data/repositories/GroupRepository;", "connectivityManager", "Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;", "(Lcom/example/splitexpenses/data/repositories/GroupRepository;Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;)V", "availableGroups", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/example/splitexpenses/data/GroupData;", "getAvailableGroups", "()Lkotlinx/coroutines/flow/StateFlow;", "groupsError", "", "getGroupsError", "isLoadingGroups", "", "pendingSyncCount", "", "getPendingSyncCount", "createGroup", "name", "members", "currentUser", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInitialState", "deleteGroups", "", "groupIds", "", "getGroupMembersWithStatus", "Lkotlin/Pair;", "groupId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSavedUserForGroup", "getUnassignedMembers", "importGroupFromCsv", "Lcom/example/splitexpenses/util/CsvImportResult;", "inputStream", "Ljava/io/InputStream;", "(Ljava/io/InputStream;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isCurrentUserGroupCreator", "joinGroup", "memberName", "onCleared", "setMultiSelectMode", "enabled", "toggleGroupSelection", "selected", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class GroupListViewModel extends com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel<com.example.splitexpenses.ui.viewmodels.GroupListUiState> {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.GroupRepository groupRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.splitexpenses.data.GroupData>> availableGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> groupsError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> pendingSyncCount = null;
    
    @javax.inject.Inject()
    public GroupListViewModel(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.GroupRepository groupRepository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.connectivity.NetworkConnectivityManager connectivityManager) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.splitexpenses.data.GroupData>> getAvailableGroups() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingGroups() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getGroupsError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getPendingSyncCount() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.splitexpenses.ui.viewmodels.GroupListUiState createInitialState() {
        return null;
    }
    
    /**
     * Join a group
     * @param groupId The ID of the group to join
     * @param memberName The name of the member joining the group
     */
    public final void joinGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
    }
    
    /**
     * Create a new group
     * @param name The name of the group
     * @param members The initial members of the group
     * @param currentUser The current user's name
     * @return The ID of the newly created group
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> members, @org.jetbrains.annotations.NotNull()
    java.lang.String currentUser, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Delete selected groups
     * @param groupIds The IDs of the groups to delete
     */
    public final void deleteGroups(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> groupIds) {
    }
    
    /**
     * Toggle multi-select mode
     * @param enabled Whether multi-select mode should be enabled
     */
    public final void setMultiSelectMode(boolean enabled) {
    }
    
    /**
     * Toggle selection of a group
     * @param groupId The ID of the group to toggle
     * @param selected Whether the group should be selected
     */
    public final void toggleGroupSelection(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, boolean selected) {
    }
    
    /**
     * Get the saved user name for a group
     * @param groupId The ID of the group
     * @return The user name or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSavedUserForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return null;
    }
    
    /**
     * Import a group from CSV
     * @param inputStream The input stream to read from
     * @param currentUser The current user's name (optional)
     * @return The result of the import operation
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object importGroupFromCsv(@org.jetbrains.annotations.NotNull()
    java.io.InputStream inputStream, @org.jetbrains.annotations.Nullable()
    java.lang.String currentUser, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.util.CsvImportResult> $completion) {
        return null;
    }
    
    /**
     * Get unassigned members for a group
     * @param groupId The ID of the group
     * @return List of unassigned member names
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnassignedMembers(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * Get all members in a group with their assignment status
     * @param groupId The ID of the group
     * @return Pair of (all members, assigned members)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getGroupMembersWithStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Pair<? extends java.util.List<java.lang.String>, ? extends java.util.List<java.lang.String>>> $completion) {
        return null;
    }
    
    /**
     * Check if the current user is the creator of a group
     * @param groupId The ID of the group
     * @return True if the current user is the creator, false otherwise
     */
    public final boolean isCurrentUserGroupCreator(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return false;
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
}