package com.example.splitexpenses.data.connectivity;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkConnectivityManager_Factory implements Factory<NetworkConnectivityManager> {
  private final Provider<Context> contextProvider;

  public NetworkConnectivityManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public NetworkConnectivityManager get() {
    return newInstance(contextProvider.get());
  }

  public static NetworkConnectivityManager_Factory create(Provider<Context> contextProvider) {
    return new NetworkConnectivityManager_Factory(contextProvider);
  }

  public static NetworkConnectivityManager newInstance(Context context) {
    return new NetworkConnectivityManager(context);
  }
}
