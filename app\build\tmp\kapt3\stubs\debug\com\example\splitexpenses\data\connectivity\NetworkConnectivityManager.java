package com.example.splitexpenses.data.connectivity;

/**
 * Manager for monitoring network connectivity status
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nJ\u0006\u0010\f\u001a\u00020\u000bR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "TAG", "", "connectivityManager", "Landroid/net/ConnectivityManager;", "isConnected", "Lkotlinx/coroutines/flow/Flow;", "", "isCurrentlyConnected", "app_debug"})
public final class NetworkConnectivityManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "NetworkConnectivityManager";
    @org.jetbrains.annotations.NotNull()
    private final android.net.ConnectivityManager connectivityManager = null;
    
    @javax.inject.Inject()
    public NetworkConnectivityManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Flow that emits the current connectivity status
     * @return Flow<Boolean> where true means connected, false means disconnected
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isConnected() {
        return null;
    }
    
    /**
     * Check if the device is currently connected to the internet
     * @return true if connected, false otherwise
     */
    public final boolean isCurrentlyConnected() {
        return false;
    }
}