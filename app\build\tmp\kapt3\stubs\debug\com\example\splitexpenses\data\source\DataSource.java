package com.example.splitexpenses.data.source;

/**
 * Interface for data sources that provide access to groups and expenses
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\"\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\n\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\bJ\b\u0010\t\u001a\u00020\u0003H&J\u001e\u0010\n\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u000b\u001a\u00020\u0005H\u00a6@\u00a2\u0006\u0002\u0010\fJ$\u0010\r\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000fH\u00a6@\u00a2\u0006\u0002\u0010\u0010J\u0016\u0010\u0011\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a6@\u00a2\u0006\u0002\u0010\u0012J\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00150\u0014H\u00a6@\u00a2\u0006\u0002\u0010\u0016J\u0018\u0010\u0017\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0004\u001a\u00020\u0005H\u00a6@\u00a2\u0006\u0002\u0010\u0012J\u0014\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0019H&J\u0018\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00150\u00192\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0016\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u001c\u001a\u00020\u0015H\u00a6@\u00a2\u0006\u0002\u0010\u001dJ\u001e\u0010\u001e\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\bJ&\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010 \u001a\u00020\u00052\u0006\u0010!\u001a\u00020\u0001H\u00a6@\u00a2\u0006\u0002\u0010\"\u00a8\u0006#"}, d2 = {"Lcom/example/splitexpenses/data/source/DataSource;", "", "addExpense", "", "groupId", "", "expense", "Lcom/example/splitexpenses/data/Expense;", "(Ljava/lang/String;Lcom/example/splitexpenses/data/Expense;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cleanup", "deleteExpense", "expenseId", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenses", "expenseIds", "", "(Ljava/lang/String;Ljava/util/Set;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGroup", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAvailableGroups", "", "Lcom/example/splitexpenses/data/GroupData;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getGroup", "observeAvailableGroups", "Lkotlinx/coroutines/flow/Flow;", "observeGroup", "saveGroup", "group", "(Lcom/example/splitexpenses/data/GroupData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateExpense", "updateGroupField", "field", "value", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface DataSource {
    
    /**
     * Get a group by ID
     * @param groupId The ID of the group to fetch
     * @return The group data or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.data.GroupData> $completion);
    
    /**
     * Get all available groups
     * @return A list of all available groups
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAvailableGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.GroupData>> $completion);
    
    /**
     * Observe a specific group for changes
     * @param groupId The ID of the group to observe
     * @return A Flow emitting the group data whenever it changes
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.splitexpenses.data.GroupData> observeGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId);
    
    /**
     * Observe all available groups for changes
     * @return A Flow emitting the list of available groups whenever it changes
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.splitexpenses.data.GroupData>> observeAvailableGroups();
    
    /**
     * Save a group
     * @param group The group to save
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object saveGroup(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Update a specific field in a group
     * @param groupId The ID of the group to update
     * @param field The field to update
     * @param value The new value for the field
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateGroupField(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String field, @org.jetbrains.annotations.NotNull()
    java.lang.Object value, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete a group
     * @param groupId The ID of the group to delete
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Add an expense to a group
     * @param groupId The ID of the group to add the expense to
     * @param expense The expense to add
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Update an expense in a group
     * @param groupId The ID of the group containing the expense
     * @param expense The updated expense
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete an expense from a group
     * @param groupId The ID of the group containing the expense
     * @param expenseId The ID of the expense to delete
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete multiple expenses from a group
     * @param groupId The ID of the group containing the expenses
     * @param expenseIds The IDs of the expenses to delete
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpenses(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> expenseIds, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Clean up any resources used by this data source
     */
    public abstract void cleanup();
}