package com.example.splitexpenses.ui.components

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp

/**
 * Dialog for confirming group deletion
 * @param groupName The name of the group to delete
 * @param isCreator Whether the current user is the creator of the group
 * @param onDismiss Callback for when the dialog is dismissed
 * @param onConfirm Callback for when the deletion is confirmed
 */
@Composable
fun DeleteGroupDialog(
    groupName: String,
    isCreator: Boolean,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = { Text("Delete Group") },
        text = {
            if (isCreator) {
                Text("Are you sure you want to delete the group \"$groupName\"? This action cannot be undone.")
            } else {
                Text("Only the creator of the group can delete it.")
            }
        },
        confirmButton = {
            if (isCreator) {
                Button(onClick = onConfirm) {
                    Text("Delete")
                }
            } else {
                Button(onClick = onDismiss) {
                    Text("OK")
                }
            }
        },
        dismissButton = {
            if (isCreator) {
                TextButton(onClick = onDismiss) {
                    Text("Cancel")
                }
            }
        }
    )
}
