package com.example.splitexpenses.data.cache.dao;

/**
 * Data Access Object for Expense entities
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\t\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0018\u0010\u000f\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001c\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u00112\u0006\u0010\u000e\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001c\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u00132\u0006\u0010\u000e\u001a\u00020\u000bH\'J\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00070\u00112\u0006\u0010\u000e\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\u0016\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u0017\u001a\u00020\u00032\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0019J\u0016\u0010\u001a\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ \u0010\u001b\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\u001c\u001a\u00020\u001dH\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006 "}, d2 = {"Lcom/example/splitexpenses/data/cache/dao/ExpenseDao;", "", "clearAllExpenses", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpense", "expense", "Lcom/example/splitexpenses/data/cache/entities/ExpenseEntity;", "(Lcom/example/splitexpenses/data/cache/entities/ExpenseEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenseById", "expenseId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpensesForGroup", "groupId", "getExpenseById", "getExpensesForGroup", "", "getExpensesForGroupFlow", "Lkotlinx/coroutines/flow/Flow;", "getUnsyncedExpenses", "getUnsyncedExpensesForGroup", "insertExpense", "insertExpenses", "expenses", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markExpenseAsSynced", "markExpenseAsUnsynced", "timestamp", "", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateExpense", "app_debug"})
@androidx.room.Dao()
public abstract interface ExpenseDao {
    
    /**
     * Get all expenses for a specific group as Flow
     */
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE groupId = :groupId ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.splitexpenses.data.cache.entities.ExpenseEntity>> getExpensesForGroupFlow(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId);
    
    /**
     * Get all expenses for a specific group
     */
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE groupId = :groupId ORDER BY date DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getExpensesForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.ExpenseEntity>> $completion);
    
    /**
     * Get a specific expense by ID
     */
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE id = :expenseId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getExpenseById(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.data.cache.entities.ExpenseEntity> $completion);
    
    /**
     * Get expenses that are not synced
     */
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE isSynced = 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUnsyncedExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.ExpenseEntity>> $completion);
    
    /**
     * Get unsynced expenses for a specific group
     */
    @androidx.room.Query(value = "SELECT * FROM expenses WHERE groupId = :groupId AND isSynced = 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUnsyncedExpensesForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.ExpenseEntity>> $completion);
    
    /**
     * Insert or replace an expense
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertExpense(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.ExpenseEntity expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Insert or replace multiple expenses
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertExpenses(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.cache.entities.ExpenseEntity> expenses, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Update an expense
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateExpense(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.ExpenseEntity expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete an expense
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.ExpenseEntity expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete an expense by ID
     */
    @androidx.room.Query(value = "DELETE FROM expenses WHERE id = :expenseId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpenseById(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete all expenses for a group
     */
    @androidx.room.Query(value = "DELETE FROM expenses WHERE groupId = :groupId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpensesForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Mark an expense as synced
     */
    @androidx.room.Query(value = "UPDATE expenses SET isSynced = 1 WHERE id = :expenseId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object markExpenseAsSynced(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Mark an expense as unsynced
     */
    @androidx.room.Query(value = "UPDATE expenses SET isSynced = 0, lastModified = :timestamp WHERE id = :expenseId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object markExpenseAsUnsynced(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Clear all expenses
     */
    @androidx.room.Query(value = "DELETE FROM expenses")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Data Access Object for Expense entities
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}