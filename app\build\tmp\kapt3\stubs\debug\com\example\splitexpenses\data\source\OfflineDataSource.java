package com.example.splitexpenses.data.source;

/**
 * Data source for offline operations using Room database
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0010\"\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0000\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0010J\u001c\u0010\u0011\u001a\u00020\b2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J\b\u0010\u0015\u001a\u00020\bH\u0016J\u001e\u0010\u0016\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u00062\u0006\u0010\u0017\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0018J$\u0010\u0019\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u00062\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00060\u001bH\u0096@\u00a2\u0006\u0002\u0010\u001cJ\u0016\u0010\u001d\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u001eJ\u0014\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0013H\u0096@\u00a2\u0006\u0002\u0010 J\u0018\u0010!\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\t\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0002\u0010\u001eJ\u0014\u0010\"\u001a\b\u0012\u0004\u0012\u00020#0\u0013H\u0086@\u00a2\u0006\u0002\u0010 J\u0014\u0010$\u001a\b\u0012\u0004\u0012\u00020%0\u0013H\u0086@\u00a2\u0006\u0002\u0010 J\u0016\u0010&\u001a\u00020\b2\u0006\u0010\u0017\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\'\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u0014\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00130)H\u0016J\u0018\u0010*\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0)2\u0006\u0010\t\u001a\u00020\u0006H\u0016J\u0016\u0010+\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0096@\u00a2\u0006\u0002\u0010\u0010J\u001e\u0010,\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010\fJ&\u0010-\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u00062\u0006\u0010.\u001a\u00020\u00062\u0006\u0010/\u001a\u000200H\u0096@\u00a2\u0006\u0002\u00101R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/example/splitexpenses/data/source/OfflineDataSource;", "Lcom/example/splitexpenses/data/source/DataSource;", "database", "Lcom/example/splitexpenses/data/cache/SplitExpensesDatabase;", "(Lcom/example/splitexpenses/data/cache/SplitExpensesDatabase;)V", "TAG", "", "addExpense", "", "groupId", "expense", "Lcom/example/splitexpenses/data/Expense;", "(Ljava/lang/String;Lcom/example/splitexpenses/data/Expense;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cacheGroup", "group", "Lcom/example/splitexpenses/data/GroupData;", "(Lcom/example/splitexpenses/data/GroupData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cacheGroups", "groups", "", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cleanup", "deleteExpense", "expenseId", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenses", "expenseIds", "", "(Ljava/lang/String;Ljava/util/Set;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGroup", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAvailableGroups", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getGroup", "getUnsyncedExpenses", "Lcom/example/splitexpenses/data/cache/entities/ExpenseEntity;", "getUnsyncedGroups", "Lcom/example/splitexpenses/data/cache/entities/GroupEntity;", "markExpenseAsSynced", "markGroupAsSynced", "observeAvailableGroups", "Lkotlinx/coroutines/flow/Flow;", "observeGroup", "saveGroup", "updateExpense", "updateGroupField", "field", "value", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class OfflineDataSource implements com.example.splitexpenses.data.source.DataSource {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.cache.SplitExpensesDatabase database = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "OfflineDataSource";
    
    @javax.inject.Inject()
    public OfflineDataSource(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.SplitExpensesDatabase database) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.data.GroupData> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getAvailableGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.GroupData>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.example.splitexpenses.data.GroupData> observeGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.splitexpenses.data.GroupData>> observeAvailableGroups() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object saveGroup(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateGroupField(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String field, @org.jetbrains.annotations.NotNull()
    java.lang.Object value, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object addExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteExpenses(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> expenseIds, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    public void cleanup() {
    }
    
    /**
     * Cache data from remote source
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cacheGroup(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Cache multiple groups from remote source
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cacheGroups(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.GroupData> groups, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Get unsynced groups
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnsyncedGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.GroupEntity>> $completion) {
        return null;
    }
    
    /**
     * Get unsynced expenses
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnsyncedExpenses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.ExpenseEntity>> $completion) {
        return null;
    }
    
    /**
     * Mark group as synced
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markGroupAsSynced(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Mark expense as synced
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markExpenseAsSynced(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}