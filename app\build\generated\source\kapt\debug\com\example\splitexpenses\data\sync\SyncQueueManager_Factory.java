package com.example.splitexpenses.data.sync;

import com.example.splitexpenses.data.cache.SplitExpensesDatabase;
import com.example.splitexpenses.data.source.DataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncQueueManager_Factory implements Factory<SyncQueueManager> {
  private final Provider<SplitExpensesDatabase> databaseProvider;

  private final Provider<DataSource> remoteDataSourceProvider;

  public SyncQueueManager_Factory(Provider<SplitExpensesDatabase> databaseProvider,
      Provider<DataSource> remoteDataSourceProvider) {
    this.databaseProvider = databaseProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public SyncQueueManager get() {
    return newInstance(databaseProvider.get(), remoteDataSourceProvider.get());
  }

  public static SyncQueueManager_Factory create(Provider<SplitExpensesDatabase> databaseProvider,
      Provider<DataSource> remoteDataSourceProvider) {
    return new SyncQueueManager_Factory(databaseProvider, remoteDataSourceProvider);
  }

  public static SyncQueueManager newInstance(SplitExpensesDatabase database,
      DataSource remoteDataSource) {
    return new SyncQueueManager(database, remoteDataSource);
  }
}
