package com.example.splitexpenses.ui.components

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.SizeTransform
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.unit.dp

import com.example.splitexpenses.R
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.ui.components.OfflineStatusIndicator

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun GroupListScreen(
    groups: List<GroupData>,
    onGroupClick: (String, String) -> Unit,
    onCreateGroupClick: () -> Unit,
    onJoinGroupClick: () -> Unit,
    isMultiSelectMode: Boolean,
    onMultiSelectModeChange: (Boolean) -> Unit,
    selectedGroups: Set<String>,
    onSelectedGroupsChange: (String, Boolean) -> Unit,
    onDeleteSelectedGroups: () -> Unit,
    getSavedUserForGroup: (String) -> String?,
    isCurrentUserGroupCreator: (String) -> Boolean,
    isLoading: Boolean = true,
    isOffline: Boolean = false,
    pendingSyncCount: Int = 0
) {
    var showDeleteConfirmationDialog by remember { mutableStateOf(false) }

    // Handle back button specifically within this screen
    BackHandler(enabled = isMultiSelectMode) {
        // Exit multi-select mode when back button is pressed
        onMultiSelectModeChange(false)
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Split Expenses",
                    style = MaterialTheme.typography.displayMedium,
                    color = MaterialTheme.colorScheme.primary
                )


                AnimatedVisibility(
                    visible = isMultiSelectMode,
                    enter = slideInHorizontally(initialOffsetX = { fullWidth -> fullWidth })+ fadeIn(), // from right
                    exit = slideOutHorizontally(targetOffsetX = { fullWidth -> fullWidth })+ fadeOut()   // to right
                ) {
                    TextButton(onClick = { showDeleteConfirmationDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Delete selected",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Show loading indicator when loading
            if (isLoading) {
                LinearProgressIndicator(
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
            }

            // Show offline status indicator if offline or if there are pending sync items
            if (isOffline || pendingSyncCount > 0) {
                OfflineStatusIndicator(
                    isOffline = isOffline,
                    pendingSyncCount = pendingSyncCount,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            if (groups.isEmpty() && !isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text("No groups available. Create one to get started!")
                }
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    contentPadding = PaddingValues(bottom = 62.dp) // Add padding to the bottom
                ) {
                    itemsIndexed(groups) { index, group ->
                        var showUserSelection by remember { mutableStateOf(false) }
                        var selectedUser by remember { mutableStateOf("") }

                        // Check for saved user
                        val savedUser = getSavedUserForGroup(group.id)

                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp)
                                .combinedClickable(
                                    onClick = {
                                        if (isMultiSelectMode) {
                                            if (!isOffline) {
                                                onSelectedGroupsChange(
                                                    group.id,
                                                    group.id !in selectedGroups
                                                )
                                            }
                                        } else {
                                            if (savedUser != null) {
                                                onGroupClick(group.id, savedUser)
                                            } else {
                                                showUserSelection = true
                                            }
                                        }
                                    },
                                    onLongClick = {
                                        if (!isMultiSelectMode && !isOffline) {
                                            onMultiSelectModeChange(true)
                                            onSelectedGroupsChange(group.id, true)
                                        }
                                    }
                                ),
                            shape = MaterialTheme.shapes.medium,
                            color = if (group.id in selectedGroups)
                                MaterialTheme.colorScheme.primaryContainer
                            else
                                MaterialTheme.colorScheme.surface,
                            border = BorderStroke(
                                width = if (group.id in selectedGroups)
                                    2.dp
                                else
                                    1.dp,
                                color = if (group.id in selectedGroups)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.secondaryContainer
                            )
                        )
                        {
                            ListItem(
                                headlineContent = {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        //horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Text(
                                            text = group.name,
                                            style = MaterialTheme.typography.titleLarge
                                            )
                                        // Check if the current user is the creator of this group
                                        val isCreator = isCurrentUserGroupCreator(group.id)

                                        // Show a star icon for groups created by the current user
                                        if (isCreator) {
                                            Icon(
                                                painter = painterResource(id = R.drawable.crown),//imageVector = Icons.Default.Star,
                                                contentDescription = "Group Creator",
                                                tint = MaterialTheme.colorScheme.primary,
                                                modifier = Modifier.size(20.dp)
                                            )
                                        }
                                    }
                                },
                                supportingContent = {
                                    // Get the most recent expense date
                                    val dateFormat =
                                        SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
                                    val mostRecentDate = group.expenses.maxOfOrNull { it.date }

                                    Column {
                                        Text(
                                            text = "${group.members.size} members - ${group.expenses.size} expenses",
                                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                                            fontStyle = FontStyle.Italic
                                        )
                                    }
                                }
                            )
                        }

                        if (showUserSelection) {
                            var enterNewName by remember { mutableStateOf(false) }
                            var newUserName by remember { mutableStateOf("") }

                            if (enterNewName) {
                                // Dialog for entering a new name
                                AlertDialog(
                                    onDismissRequest = {
                                        enterNewName = false
                                        showUserSelection = false
                                    },
                                    containerColor = MaterialTheme.colorScheme.surface,
                                    tonalElevation = 8.dp,
                                    title = { Text("Enter Your Name") },
                                    text = {
                                        Column {
                                            Text("Please enter your name to join this group:")
                                            Spacer(modifier = Modifier.height(8.dp))
                                            OutlinedTextField(
                                                value = newUserName,
                                                onValueChange = { newUserName = it },
                                                label = { Text("Your Name") },
                                                modifier = Modifier.fillMaxWidth(),
                                                singleLine = true
                                            )
                                        }
                                    },
                                    confirmButton = {
                                        Button(
                                            onClick = {
                                                if (newUserName.isNotEmpty()) {
                                                    onGroupClick(group.id, newUserName)
                                                    enterNewName = false
                                                    showUserSelection = false
                                                }
                                            },
                                            enabled = newUserName.isNotEmpty()
                                        ) {
                                            Text("Join")
                                        }
                                    },
                                    dismissButton = {
                                        TextButton(onClick = {
                                            enterNewName = false
                                            showUserSelection = false
                                        }) {
                                            Text("Cancel")
                                        }
                                    }
                                )
                            } else {
                                // Dialog for selecting an existing name
                                AlertDialog(
                                    onDismissRequest = { showUserSelection = false },
                                    containerColor = MaterialTheme.colorScheme.surface,
                                    tonalElevation = 8.dp,
                                    title = { Text("Select Your Name") },
                                    text = {
                                        Column {
                                            group.members.forEach { member ->
                                                Row(
                                                    modifier = Modifier
                                                        .fillMaxWidth()
                                                        .clickable {
                                                            selectedUser = member
                                                            onGroupClick(group.id, member)
                                                            showUserSelection = false
                                                        }
                                                        .padding(vertical = 8.dp),
                                                    verticalAlignment = Alignment.CenterVertically
                                                ) {
                                                    RadioButton(
                                                        selected = selectedUser == member,
                                                        onClick = {
                                                            selectedUser = member
                                                            onGroupClick(group.id, member)
                                                            showUserSelection = false
                                                        }
                                                    )
                                                    Text(
                                                        text = member,
                                                        modifier = Modifier.padding(start = 8.dp)
                                                    )
                                                }
                                            }

                                            Spacer(modifier = Modifier.height(16.dp))

                                            TextButton(
                                                onClick = { enterNewName = true },
                                                modifier = Modifier.align(Alignment.CenterHorizontally)
                                            ) {
                                                Text("Use a different name")
                                            }
                                        }
                                    },
                                    confirmButton = {
                                        Button(
                                            onClick = {
                                                onGroupClick(group.id, selectedUser)
                                                showUserSelection = false
                                            },
                                            enabled = selectedUser.isNotEmpty()
                                        ) {
                                            Text("Join")
                                        }
                                    },
                                    dismissButton = {
                                        TextButton(onClick = { showUserSelection = false }) {
                                            Text("Cancel")
                                        }
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }


        // Join Group button
        Box(
            modifier = Modifier
                .padding(16.dp)
                .align(Alignment.BottomStart)
        ) {
            Button(
                onClick = onJoinGroupClick
            ) {
                Text("Join Group")
            }
        }

        // Create Group button
        Box(
            modifier = Modifier
                .padding(16.dp)
                .align(Alignment.BottomEnd)
        ) {
            FloatingActionButton(
                onClick = onCreateGroupClick
            ) {
                Icon(Icons.Default.Add, contentDescription = "Create Group")
            }
        }
    }

    // Delete confirmation dialog
    if (showDeleteConfirmationDialog) {
        DeleteMultipleGroupsDialog(
            groupCount = selectedGroups.size,
            onDismiss = { showDeleteConfirmationDialog = false },
            onConfirm = {
                onDeleteSelectedGroups()
                showDeleteConfirmationDialog = false
            }
        )
    }
}
