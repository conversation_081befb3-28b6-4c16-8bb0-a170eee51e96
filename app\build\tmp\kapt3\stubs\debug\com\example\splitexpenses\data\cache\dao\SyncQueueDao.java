package com.example.splitexpenses.data.cache.dao;

/**
 * Data Access Object for SyncQueue entities
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\f\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00070\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00120\u0014H\'J\u000e\u0010\u0015\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0014H\'J\u001c\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00070\u00122\u0006\u0010\u000e\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u001c\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00070\u00122\u0006\u0010\u0019\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001e\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00070\u00122\b\b\u0002\u0010\u001d\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001e\u0010\u001e\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010 J\u0016\u0010!\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\"\u001a\u00020\u00032\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00070\u0012H\u00a7@\u00a2\u0006\u0002\u0010$J\u0016\u0010%\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006&"}, d2 = {"Lcom/example/splitexpenses/data/cache/dao/SyncQueueDao;", "", "clearAllSyncItems", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSyncItem", "syncItem", "Lcom/example/splitexpenses/data/cache/entities/SyncQueueEntity;", "(Lcom/example/splitexpenses/data/cache/entities/SyncQueueEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSyncItemById", "id", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSyncItemsByEntityId", "entityId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllSyncItems", "", "getAllSyncItemsFlow", "Lkotlinx/coroutines/flow/Flow;", "getPendingSyncCount", "getPendingSyncCountFlow", "getSyncItemsByEntityId", "getSyncItemsByType", "entityType", "Lcom/example/splitexpenses/data/cache/entities/SyncEntityType;", "(Lcom/example/splitexpenses/data/cache/entities/SyncEntityType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSyncItemsForRetry", "maxRetries", "incrementRetryCount", "error", "(ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertSyncItem", "insertSyncItems", "syncItems", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSyncItem", "app_debug"})
@androidx.room.Dao()
public abstract interface SyncQueueDao {
    
    /**
     * Get all sync queue items as Flow
     */
    @androidx.room.Query(value = "SELECT * FROM sync_queue ORDER BY timestamp ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.splitexpenses.data.cache.entities.SyncQueueEntity>> getAllSyncItemsFlow();
    
    /**
     * Get all sync queue items
     */
    @androidx.room.Query(value = "SELECT * FROM sync_queue ORDER BY timestamp ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllSyncItems(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.SyncQueueEntity>> $completion);
    
    /**
     * Get sync items by entity type
     */
    @androidx.room.Query(value = "SELECT * FROM sync_queue WHERE entityType = :entityType ORDER BY timestamp ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSyncItemsByType(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncEntityType entityType, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.SyncQueueEntity>> $completion);
    
    /**
     * Get sync items by entity ID
     */
    @androidx.room.Query(value = "SELECT * FROM sync_queue WHERE entityId = :entityId ORDER BY timestamp ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSyncItemsByEntityId(@org.jetbrains.annotations.NotNull()
    java.lang.String entityId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.SyncQueueEntity>> $completion);
    
    /**
     * Get sync items with retry count less than max
     */
    @androidx.room.Query(value = "SELECT * FROM sync_queue WHERE retryCount < :maxRetries ORDER BY timestamp ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSyncItemsForRetry(int maxRetries, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.SyncQueueEntity>> $completion);
    
    /**
     * Insert a sync queue item
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertSyncItem(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncQueueEntity syncItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Insert multiple sync queue items
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertSyncItems(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.cache.entities.SyncQueueEntity> syncItems, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Update a sync queue item
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSyncItem(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncQueueEntity syncItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete a sync queue item
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSyncItem(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncQueueEntity syncItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete a sync queue item by ID
     */
    @androidx.room.Query(value = "DELETE FROM sync_queue WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSyncItemById(int id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete sync items by entity ID
     */
    @androidx.room.Query(value = "DELETE FROM sync_queue WHERE entityId = :entityId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSyncItemsByEntityId(@org.jetbrains.annotations.NotNull()
    java.lang.String entityId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Increment retry count for a sync item
     */
    @androidx.room.Query(value = "UPDATE sync_queue SET retryCount = retryCount + 1, lastError = :error WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object incrementRetryCount(int id, @org.jetbrains.annotations.NotNull()
    java.lang.String error, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Clear all sync queue items
     */
    @androidx.room.Query(value = "DELETE FROM sync_queue")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllSyncItems(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Get count of pending sync items
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM sync_queue")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPendingSyncCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * Get count of pending sync items as Flow
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM sync_queue")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Integer> getPendingSyncCountFlow();
    
    /**
     * Data Access Object for SyncQueue entities
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}