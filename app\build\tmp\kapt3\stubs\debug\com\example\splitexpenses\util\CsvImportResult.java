package com.example.splitexpenses.util;

/**
 * Class to hold the results of a CSV import operation
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u001a\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0087\b\u0018\u00002\u00020\u0001BM\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0007\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\f\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J\u000f\u0010 \u001a\b\u0012\u0004\u0012\u00020\n0\u0007H\u00c6\u0003J\t\u0010!\u001a\u00020\fH\u00c6\u0003J\t\u0010\"\u001a\u00020\fH\u00c6\u0003JS\u0010#\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00072\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\fH\u00c6\u0001J\u0013\u0010$\u001a\u00020\u00032\b\u0010%\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010&\u001a\u00020\'J\u0006\u0010(\u001a\u00020\'J\t\u0010)\u001a\u00020\fH\u00d6\u0001J\t\u0010*\u001a\u00020\'H\u00d6\u0001R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0013\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0016\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\u0017\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001aR\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0010\u00a8\u0006+"}, d2 = {"Lcom/example/splitexpenses/util/CsvImportResult;", "", "success", "", "group", "Lcom/example/splitexpenses/data/GroupData;", "errors", "", "Lcom/example/splitexpenses/util/CsvImportError;", "warnings", "Lcom/example/splitexpenses/util/CsvImportWarning;", "totalRowsProcessed", "", "successfulRows", "(ZLcom/example/splitexpenses/data/GroupData;Ljava/util/List;Ljava/util/List;II)V", "getErrors", "()Ljava/util/List;", "getGroup", "()Lcom/example/splitexpenses/data/GroupData;", "hasErrors", "getHasErrors", "()Z", "hasWarnings", "getHasWarnings", "getSuccess", "getSuccessfulRows", "()I", "getTotalRowsProcessed", "getWarnings", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "getDetailedMessages", "", "getSummary", "hashCode", "toString", "app_debug"})
public final class CsvImportResult {
    private final boolean success = false;
    @org.jetbrains.annotations.Nullable()
    private final com.example.splitexpenses.data.GroupData group = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.splitexpenses.util.CsvImportError> errors = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.splitexpenses.util.CsvImportWarning> warnings = null;
    private final int totalRowsProcessed = 0;
    private final int successfulRows = 0;
    
    public CsvImportResult(boolean success, @org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.util.CsvImportError> errors, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.util.CsvImportWarning> warnings, int totalRowsProcessed, int successfulRows) {
        super();
    }
    
    public final boolean getSuccess() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.splitexpenses.data.GroupData getGroup() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.util.CsvImportError> getErrors() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.util.CsvImportWarning> getWarnings() {
        return null;
    }
    
    public final int getTotalRowsProcessed() {
        return 0;
    }
    
    public final int getSuccessfulRows() {
        return 0;
    }
    
    public final boolean getHasErrors() {
        return false;
    }
    
    public final boolean getHasWarnings() {
        return false;
    }
    
    /**
     * Get a summary of the import operation
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSummary() {
        return null;
    }
    
    /**
     * Get detailed error and warning messages
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDetailedMessages() {
        return null;
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.splitexpenses.data.GroupData component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.util.CsvImportError> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.util.CsvImportWarning> component4() {
        return null;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.util.CsvImportResult copy(boolean success, @org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.util.CsvImportError> errors, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.util.CsvImportWarning> warnings, int totalRowsProcessed, int successfulRows) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}