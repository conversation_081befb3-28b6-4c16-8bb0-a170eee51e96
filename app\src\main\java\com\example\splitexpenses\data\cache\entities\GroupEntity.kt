package com.example.splitexpenses.data.cache.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.getDefaultCategories
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Room entity for storing group data locally
 */
@Entity(tableName = "groups")
@TypeConverters(GroupEntity.Converters::class)
data class GroupEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val members: List<String>,
    val memberUidMap: Map<String, String>,
    val allowedUsers: List<String>,
    val creatorUid: String,
    val categories: List<Category>,
    val memberAvatars: Map<String, String>,
    val lastModified: Long = System.currentTimeMillis(),
    val isSynced: Boolean = true
) {
    companion object {
        /**
         * Convert GroupData to GroupEntity
         */
        fun fromGroupData(groupData: GroupData, isSynced: Boolean = true): GroupEntity {
            return GroupEntity(
                id = groupData.id,
                name = groupData.name,
                members = groupData.members,
                memberUidMap = groupData.memberUidMap,
                allowedUsers = groupData.allowedUsers,
                creatorUid = groupData.creatorUid,
                categories = groupData.categories,
                memberAvatars = groupData.memberAvatars,
                lastModified = System.currentTimeMillis(),
                isSynced = isSynced
            )
        }
    }

    /**
     * Convert GroupEntity to GroupData
     * Note: Expenses are stored separately and need to be fetched from ExpenseEntity
     */
    fun toGroupData(expenses: List<com.example.splitexpenses.data.Expense> = emptyList()): GroupData {
        return GroupData(
            id = id,
            name = name,
            members = members,
            expenses = expenses,
            memberUidMap = memberUidMap,
            allowedUsers = allowedUsers,
            creatorUid = creatorUid,
            categories = categories,
            memberAvatars = memberAvatars
        )
    }

    /**
     * Type converters for Room database
     */
    class Converters {
        private val gson = Gson()

        @TypeConverter
        fun fromStringList(value: List<String>): String {
            return gson.toJson(value)
        }

        @TypeConverter
        fun toStringList(value: String): List<String> {
            val listType = object : TypeToken<List<String>>() {}.type
            return gson.fromJson(value, listType)
        }

        @TypeConverter
        fun fromStringMap(value: Map<String, String>): String {
            return gson.toJson(value)
        }

        @TypeConverter
        fun toStringMap(value: String): Map<String, String> {
            val mapType = object : TypeToken<Map<String, String>>() {}.type
            return gson.fromJson(value, mapType)
        }

        @TypeConverter
        fun fromCategoryList(value: List<Category>): String {
            return gson.toJson(value)
        }

        @TypeConverter
        fun toCategoryList(value: String): List<Category> {
            val listType = object : TypeToken<List<Category>>() {}.type
            return gson.fromJson(value, listType) ?: getDefaultCategories()
        }
    }
}
