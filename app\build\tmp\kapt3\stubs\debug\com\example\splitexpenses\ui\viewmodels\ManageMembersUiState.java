package com.example.splitexpenses.ui.viewmodels;

/**
 * UI state for the manage members screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0018\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BM\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\u0007\u0012\b\b\u0002\u0010\n\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\fJ\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0007H\u00c6\u0003JQ\u0010\u001c\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u00052\b\b\u0002\u0010\u000b\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u00072\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001J\t\u0010!\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0011\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0012R\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012\u00a8\u0006\""}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/ManageMembersUiState;", "", "currentGroup", "Lcom/example/splitexpenses/data/GroupData;", "newMemberName", "", "showError", "", "errorMessage", "isLoading", "currentUser", "isCurrentUserGroupCreator", "(Lcom/example/splitexpenses/data/GroupData;Ljava/lang/String;ZLjava/lang/String;ZLjava/lang/String;Z)V", "getCurrentGroup", "()Lcom/example/splitexpenses/data/GroupData;", "getCurrentUser", "()Ljava/lang/String;", "getErrorMessage", "()Z", "getNewMemberName", "getShowError", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ManageMembersUiState {
    @org.jetbrains.annotations.Nullable()
    private final com.example.splitexpenses.data.GroupData currentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String newMemberName = null;
    private final boolean showError = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String errorMessage = null;
    private final boolean isLoading = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String currentUser = null;
    private final boolean isCurrentUserGroupCreator = false;
    
    public ManageMembersUiState(@org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.GroupData currentGroup, @org.jetbrains.annotations.NotNull()
    java.lang.String newMemberName, boolean showError, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage, boolean isLoading, @org.jetbrains.annotations.NotNull()
    java.lang.String currentUser, boolean isCurrentUserGroupCreator) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.splitexpenses.data.GroupData getCurrentGroup() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNewMemberName() {
        return null;
    }
    
    public final boolean getShowError() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCurrentUser() {
        return null;
    }
    
    public final boolean isCurrentUserGroupCreator() {
        return false;
    }
    
    public ManageMembersUiState() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.splitexpenses.data.GroupData component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.ui.viewmodels.ManageMembersUiState copy(@org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.GroupData currentGroup, @org.jetbrains.annotations.NotNull()
    java.lang.String newMemberName, boolean showError, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage, boolean isLoading, @org.jetbrains.annotations.NotNull()
    java.lang.String currentUser, boolean isCurrentUserGroupCreator) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}