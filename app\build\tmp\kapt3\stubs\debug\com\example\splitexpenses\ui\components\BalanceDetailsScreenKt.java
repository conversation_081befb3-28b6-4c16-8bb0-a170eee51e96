package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\u001aX\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00052\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\tH\u0007\u00a8\u0006\u000b"}, d2 = {"BalanceDetailsScreen", "", "viewModel", "Lcom/example/splitexpenses/ui/viewmodels/BalanceDetailsViewModel;", "onBackClick", "Lkotlin/Function0;", "onPreviousClick", "onNextClick", "hasPrevious", "", "hasNext", "app_debug"})
public final class BalanceDetailsScreenKt {
    
    /**
     * Screen that displays detailed balance information for a group
     *
     * Features:
     * - Filter debts by specific users
     * - Real-time updates from Firebase
     * - Consistent UI with the rest of the application
     * - Organized layout for balances
     * - Loading and error states
     */
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.layout.ExperimentalLayoutApi.class})
    @androidx.compose.runtime.Composable()
    public static final void BalanceDetailsScreen(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.BalanceDetailsViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPreviousClick, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNextClick, boolean hasPrevious, boolean hasNext) {
    }
}