package com.example.splitexpenses.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.example.splitexpenses.R
import com.example.splitexpenses.ui.components.OfflineStatusIndicator

// List of common emojis that can be used as avatars
val commonEmojis = listOf(
    "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇",
    "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚",
    "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩",
    "🥳", "😏", "😒", "😞", "😔", "😟", "😕", "🙁", "☹️", "😣",
    "😖", "😫", "😩", "🥺", "😢", "😭", "😤", "😠", "😡", "🤬",
    "🤯", "😳", "🥵", "🥶", "😱", "😨", "😰", "😥", "😓", "🤗",
    "🤔", "🤭", "🤫", "🤥", "😶", "😐", "😑", "😬", "🙄", "😯",
    "😦", "😧", "😮", "😲", "🥱", "😴", "🤤", "😪", "😵", "🤐",
    "🥴", "🤢", "🤮", "🤧", "😷", "🤒", "🤕", "🤑", "🤠", "😈",
    "👿", "👹", "👺", "🤡", "💩", "👻", "💀", "☠️", "👽", "👾",
    "🌚", "🌝", "🌞", "🌛",  "🌜", "😺", "😸", "😹", "😻", "😼",
    "😽", "🙀", "😿", "😾", "🙈", "🙉", "🙊", "🐵", "🦁", "🐯",
    "🐱", "🐶", "🐺", "🐻", "🐨", "🐼", "🐹", "🐭", "🐰", "🦊",
    "🦝", "🐮", "🐷", "🧝‍♂️", "🧙‍♂️", "🧛‍♂️", "🧟‍♂️", "🎅", "👼", "💂‍♂️",
    "🤴", "🤵‍", "👨‍🚀",  "👷‍♂️", "👮‍♂️", "🕵️‍♂️", "👨‍✈️",  "👨‍🔬", "👨‍⚕️", "👨‍🔧",
    "👨‍🏭", "👨‍🚒", "👨‍🌾", "👨‍🏫", "👨‍🎓",  "👨‍💼", "👨‍⚖️", "👨‍💻", "👨‍🎤", "👨‍🎨",
    "👨‍🍳", "👳‍♂️", "🧕", "👲", "👶",  "👦", "👨", "👴", "👨‍🦳", "👨‍🦰",
    "👱‍♂️", "👨‍🦱", "👨‍🦲"

)

// Special value to represent "no avatar" (will use default account_outline drawable)
const val NO_AVATAR = "NO_AVATAR"

/**
 * Dialog for editing a member's information
 * @param currentName The current name of the member
 * @param currentAvatar The current avatar of the member (can be null)
 * @param onDismiss Callback for when the dialog is dismissed
 * @param onSave Callback for when the information is saved, with a callback for success/failure
 */
@Composable
fun EditMemberInfoDialog(
    currentName: String,
    currentAvatar: String?,
    onDismiss: () -> Unit,
    onSave: (String, String?, (Boolean, String?) -> Unit) -> Unit,
    isOffline: Boolean = false
) {
    var memberName by remember { mutableStateOf(currentName) }
    var selectedAvatar by remember { mutableStateOf(currentAvatar) }
    var showError by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = {
            Text(
                text = "Edit Your Information",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
            ) {
                // Show offline indicator if offline
                if (isOffline) {
                    OfflineStatusIndicator(
                        isOffline = true,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }

                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.padding(vertical = 16.dp)
                    )
                } else {
                    // Avatar selection
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Surface(
                            modifier = Modifier.size(60.dp),
                            shape = MaterialTheme.shapes.medium,
                            border = BorderStroke(1.dp, MaterialTheme.colorScheme.secondaryContainer)
                        ) {
                            Box(
                                modifier = Modifier.fillMaxWidth(),
                                contentAlignment = Alignment.Center
                            ) {
                                val avatar = selectedAvatar
                                if (avatar != null) {
                                    Text(
                                        text = avatar,
                                        style = MaterialTheme.typography.headlineLarge
                                    )
                                } else {
                                    Icon(
                                        painter = painterResource(id = R.drawable.account_outline),
                                        contentDescription = null,
                                        modifier = Modifier.size(40.dp),
                                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.width(16.dp))

                        Column {
                            Text(
                                text = "Your Avatar",
                                style = MaterialTheme.typography.titleMedium
                            )
                            Text(
                                text = "Select from options below",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Name field
                    OutlinedTextField(
                        value = memberName,
                        onValueChange = {
                            memberName = it
                            showError = false
                            errorMessage = null
                        },
                        label = { Text("Your Name") },
                        modifier = Modifier.fillMaxWidth(),
                        isError = showError || errorMessage != null,
                        singleLine = true,
                        enabled = !isOffline,
                        colors = TextFieldDefaults.colors(
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    )

                    if (showError) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Name cannot be empty",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    if (errorMessage != null) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = errorMessage!!,
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    // Emoji picker - always shown
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "Select an Avatar",
                        style = MaterialTheme.typography.titleMedium
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    LazyVerticalGrid(
                        columns = GridCells.Fixed(8),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        horizontalArrangement = Arrangement.spacedBy(2.dp),
                        verticalArrangement = Arrangement.spacedBy(2.dp)
                    ) {
                        // Add "None" option at the beginning
                        item {
                            Surface(
                                modifier = Modifier
                                    .size(32.dp)
                                    .clickable(enabled = !isOffline) {
                                        if (!isOffline) {
                                            selectedAvatar = null
                                        }
                                    },
                                shape = MaterialTheme.shapes.small,
                                color = if (selectedAvatar == null) {
                                    MaterialTheme.colorScheme.primaryContainer
                                } else {
                                    MaterialTheme.colorScheme.surface
                                },
                                border = if (selectedAvatar == null) {
                                    BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
                                } else {
                                    null
                                }
                            ) {
                                Box(
                                    modifier = Modifier.fillMaxWidth(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.account_outline),
                                        contentDescription = "No avatar",
                                        modifier = Modifier.size(20.dp),
                                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }

                        // Regular emoji options
                        items(commonEmojis) { emoji ->
                            Surface(
                                modifier = Modifier
                                    .size(32.dp)
                                    .clickable(enabled = !isOffline) {
                                        if (!isOffline) {
                                            selectedAvatar = emoji
                                        }
                                    },
                                shape = MaterialTheme.shapes.small,
                                color = if (selectedAvatar == emoji) {
                                    MaterialTheme.colorScheme.primaryContainer
                                } else {
                                    MaterialTheme.colorScheme.surface
                                },
                                border = if (selectedAvatar == emoji) {
                                    BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
                                } else {
                                    null
                                }
                            ) {
                                Box(
                                    modifier = Modifier.fillMaxWidth(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = emoji,
                                        style = MaterialTheme.typography.titleMedium
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (memberName.isBlank()) {
                        showError = true
                        return@Button
                    }

                    isLoading = true
                    onSave(memberName, selectedAvatar) { success, error ->
                        isLoading = false
                        if (success) {
                            onDismiss()
                        } else {
                            errorMessage = error ?: "Failed to update information"
                        }
                    }
                },
                enabled = !isLoading && memberName.isNotBlank() && !isOffline
            ) {
                Text("Save")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text("Cancel")
            }
        }
    )
}
