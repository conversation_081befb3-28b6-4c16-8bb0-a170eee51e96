package com.example.splitexpenses.data.repositories

import android.util.Log
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager
import com.example.splitexpenses.data.source.DataSource
import com.example.splitexpenses.data.source.OfflineDataSource
import com.example.splitexpenses.data.sync.SyncQueueManager
import com.example.splitexpenses.data.cache.entities.SyncOperationType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Repository that handles both online and offline operations
 * Automatically switches between Firebase and local cache based on connectivity
 */
@Singleton
class OfflineCapableRepository @Inject constructor(
    @Named("firebase") private val remoteDataSource: DataSource,
    @Named("offline") private val offlineDataSource: OfflineDataSource,
    private val networkConnectivityManager: NetworkConnectivityManager,
    private val syncQueueManager: SyncQueueManager
) {
    private val TAG = "OfflineCapableRepository"
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    init {
        // Start monitoring connectivity and sync when online
        coroutineScope.launch {
            networkConnectivityManager.isConnected()
                .distinctUntilChanged()
                .collect { isConnected ->
                    if (isConnected) {
                        Log.d(TAG, "Device came online - starting sync and refresh")
                        syncOfflineChanges()
                        // Force a refresh of data when coming back online
                        refreshDataFromRemote()
                    } else {
                        Log.d(TAG, "Device went offline - switching to cache")
                    }
                }
        }
    }

    /**
     * Get a group - tries remote first if online, falls back to cache
     */
    suspend fun getGroup(groupId: String): GroupData? {
        val isConnected = networkConnectivityManager.isConnected().first()
        return if (isConnected) {
            try {
                val group = remoteDataSource.getGroup(groupId)
                if (group != null) {
                    // Cache the group for offline access
                    try {
                        offlineDataSource.cacheGroup(group)
                    } catch (cacheException: Exception) {
                        Log.w(TAG, "Failed to cache group data", cacheException)
                        // Continue without caching
                    }
                }
                group
            } catch (e: Exception) {
                Log.w(TAG, "Failed to get group from remote, using cache", e)
                try {
                    offlineDataSource.getGroup(groupId)
                } catch (cacheException: Exception) {
                    Log.e(TAG, "Failed to get group from cache", cacheException)
                    null
                }
            }
        } else {
            try {
                offlineDataSource.getGroup(groupId)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get group from cache while offline", e)
                null
            }
        }
    }

    /**
     * Get available groups - tries remote first if online, falls back to cache
     */
    suspend fun getAvailableGroups(): List<GroupData> {
        val isConnected = networkConnectivityManager.isConnected().first()
        return if (isConnected) {
            try {
                val groups = remoteDataSource.getAvailableGroups()
                // Cache the groups for offline access
                try {
                    offlineDataSource.cacheGroups(groups)
                } catch (cacheException: Exception) {
                    Log.w(TAG, "Failed to cache groups data", cacheException)
                    // Continue without caching
                }
                groups
            } catch (e: Exception) {
                Log.w(TAG, "Failed to get groups from remote, using cache", e)
                try {
                    offlineDataSource.getAvailableGroups()
                } catch (cacheException: Exception) {
                    Log.e(TAG, "Failed to get groups from cache", cacheException)
                    emptyList()
                }
            }
        } else {
            try {
                offlineDataSource.getAvailableGroups()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get groups from cache while offline", e)
                emptyList()
            }
        }
    }

    /**
     * Observe a group - combines remote and local data
     */
    fun observeGroup(groupId: String): Flow<GroupData?> {
        return networkConnectivityManager.isConnected()
            .flatMapLatest { isConnected ->
                if (isConnected) {
                    // When online, observe remote and cache updates
                    remoteDataSource.observeGroup(groupId)
                        .onEach { remoteGroup ->
                            if (remoteGroup != null) {
                                // Cache the remote data
                                coroutineScope.launch {
                                    try {
                                        offlineDataSource.cacheGroup(remoteGroup)
                                    } catch (e: Exception) {
                                        Log.w(TAG, "Failed to cache group data", e)
                                        // Continue without caching
                                    }
                                }
                            }
                        }
                        .catch {
                            // If remote fails, fall back to cache
                            try {
                                emitAll(offlineDataSource.observeGroup(groupId))
                            } catch (cacheException: Exception) {
                                Log.e(TAG, "Failed to observe group from cache", cacheException)
                                emit(null)
                            }
                        }
                } else {
                    // When offline, only observe cache
                    offlineDataSource.observeGroup(groupId)
                        .catch { e ->
                            Log.e(TAG, "Failed to observe group from cache while offline", e)
                            emit(null)
                        }
                }
            }
            .distinctUntilChanged()
    }

    /**
     * Observe available groups - combines remote and local data
     */
    fun observeAvailableGroups(): Flow<List<GroupData>> {
        return networkConnectivityManager.isConnected()
            .onEach { isConnected ->
                Log.d(TAG, "observeAvailableGroups: Connectivity changed - isConnected: $isConnected")
            }
            .flatMapLatest { isConnected ->
                Log.d(TAG, "observeAvailableGroups: Switching data source - isConnected: $isConnected")
                if (isConnected) {
                    // When online, observe remote and cache updates
                    Log.d(TAG, "observeAvailableGroups: Using remote data source")
                    remoteDataSource.observeAvailableGroups()
                        .onEach { groups ->
                            Log.d(TAG, "observeAvailableGroups: Received ${groups.size} groups from remote")
                            // Cache the remote data
                            coroutineScope.launch {
                                try {
                                    offlineDataSource.cacheGroups(groups)
                                    Log.d(TAG, "observeAvailableGroups: Successfully cached ${groups.size} groups")
                                } catch (e: Exception) {
                                    Log.w(TAG, "Failed to cache groups data", e)
                                    // Continue without caching
                                }
                            }
                        }
                        .catch { e ->
                            Log.w(TAG, "observeAvailableGroups: Remote failed, falling back to cache", e)
                            // If remote fails, fall back to cache
                            try {
                                emitAll(offlineDataSource.observeAvailableGroups())
                            } catch (cacheException: Exception) {
                                Log.e(TAG, "Failed to observe groups from cache", cacheException)
                                emit(emptyList())
                            }
                        }
                } else {
                    // When offline, only observe cache
                    Log.d(TAG, "observeAvailableGroups: Using offline data source")
                    offlineDataSource.observeAvailableGroups()
                        .onEach { groups ->
                            Log.d(TAG, "observeAvailableGroups: Received ${groups.size} groups from cache")
                        }
                        .catch { e ->
                            Log.e(TAG, "Failed to observe groups from cache while offline", e)
                            emit(emptyList())
                        }
                }
            }
            .distinctUntilChanged()
    }

    /**
     * Save a group - saves to remote if online, queues for sync if offline
     */
    suspend fun saveGroup(group: GroupData) {
        val isConnected = networkConnectivityManager.isConnected().first()
        if (isConnected) {
            try {
                remoteDataSource.saveGroup(group)
                // Also cache locally
                try {
                    offlineDataSource.cacheGroup(group)
                } catch (cacheException: Exception) {
                    Log.w(TAG, "Failed to cache group after saving to remote", cacheException)
                    // Continue - remote save was successful
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save group to remote, saving locally", e)
                try {
                    offlineDataSource.saveGroup(group)
                    syncQueueManager.queueGroupOperation(group, SyncOperationType.CREATE)
                } catch (cacheException: Exception) {
                    Log.e(TAG, "Failed to save group locally", cacheException)
                    throw cacheException
                }
            }
        } else {
            // Save locally and queue for sync
            try {
                offlineDataSource.saveGroup(group)
                syncQueueManager.queueGroupOperation(group, SyncOperationType.CREATE)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save group locally while offline", e)
                throw e
            }
        }
    }

    /**
     * Update a group field - updates remote if online, queues for sync if offline
     */
    suspend fun updateGroupField(groupId: String, field: String, value: Any) {
        val isConnected = networkConnectivityManager.isConnected().first()
        if (isConnected) {
            try {
                remoteDataSource.updateGroupField(groupId, field, value)
                // Also update cache
                offlineDataSource.updateGroupField(groupId, field, value)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update group field on remote, updating locally", e)
                offlineDataSource.updateGroupField(groupId, field, value)
                // Queue the entire group for sync
                val group = offlineDataSource.getGroup(groupId)
                if (group != null) {
                    syncQueueManager.queueGroupOperation(group, SyncOperationType.UPDATE)
                }
            }
        } else {
            // Update locally and queue for sync
            offlineDataSource.updateGroupField(groupId, field, value)
            val group = offlineDataSource.getGroup(groupId)
            if (group != null) {
                syncQueueManager.queueGroupOperation(group, SyncOperationType.UPDATE)
            }
        }
    }

    /**
     * Delete a group - deletes from remote if online, queues for sync if offline
     */
    suspend fun deleteGroup(groupId: String) {
        val isConnected = networkConnectivityManager.isConnected().first()
        if (isConnected) {
            try {
                remoteDataSource.deleteGroup(groupId)
                offlineDataSource.deleteGroup(groupId)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to delete group from remote", e)
                // Don't delete locally if remote delete failed
                throw e
            }
        } else {
            // Can't delete offline - this should be blocked by UI
            throw IllegalStateException("Cannot delete groups while offline")
        }
    }

    /**
     * Add an expense - adds to remote if online, queues for sync if offline
     */
    suspend fun addExpense(groupId: String, expense: Expense) {
        val isConnected = networkConnectivityManager.isConnected().first()
        if (isConnected) {
            try {
                remoteDataSource.addExpense(groupId, expense)
                // Also cache locally
                offlineDataSource.addExpense(groupId, expense)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to add expense to remote, saving locally", e)
                offlineDataSource.addExpense(groupId, expense)
                syncQueueManager.queueExpenseOperation(expense, groupId, SyncOperationType.CREATE)
            }
        } else {
            // Save locally and queue for sync
            offlineDataSource.addExpense(groupId, expense)
            syncQueueManager.queueExpenseOperation(expense, groupId, SyncOperationType.CREATE)
        }
    }

    /**
     * Update an expense - updates remote if online, queues for sync if offline
     */
    suspend fun updateExpense(groupId: String, expense: Expense) {
        val isConnected = networkConnectivityManager.isConnected().first()
        if (isConnected) {
            try {
                remoteDataSource.updateExpense(groupId, expense)
                offlineDataSource.updateExpense(groupId, expense)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update expense on remote", e)
                throw e
            }
        } else {
            // Can't edit existing content offline - this should be blocked by UI
            throw IllegalStateException("Cannot edit existing expenses while offline")
        }
    }

    /**
     * Delete an expense - deletes from remote if online, queues for sync if offline
     */
    suspend fun deleteExpense(groupId: String, expenseId: String) {
        val isConnected = networkConnectivityManager.isConnected().first()
        if (isConnected) {
            try {
                remoteDataSource.deleteExpense(groupId, expenseId)
                offlineDataSource.deleteExpense(groupId, expenseId)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to delete expense from remote", e)
                throw e
            }
        } else {
            // Can't delete offline - this should be blocked by UI
            throw IllegalStateException("Cannot delete expenses while offline")
        }
    }

    /**
     * Delete multiple expenses - deletes from remote if online, queues for sync if offline
     */
    suspend fun deleteExpenses(groupId: String, expenseIds: Set<String>) {
        val isConnected = networkConnectivityManager.isConnected().first()
        if (isConnected) {
            try {
                remoteDataSource.deleteExpenses(groupId, expenseIds)
                offlineDataSource.deleteExpenses(groupId, expenseIds)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to delete expenses from remote", e)
                throw e
            }
        } else {
            // Can't delete offline - this should be blocked by UI
            throw IllegalStateException("Cannot delete expenses while offline")
        }
    }



    /**
     * Sync offline changes to remote
     */
    private suspend fun syncOfflineChanges() {
        try {
            val success = syncQueueManager.processPendingSyncItems()
            if (success) {
                Log.d(TAG, "Successfully synced all offline changes")
            } else {
                Log.w(TAG, "Some offline changes failed to sync")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing offline changes", e)
        }
    }

    /**
     * Force refresh data from remote when coming back online
     */
    private suspend fun refreshDataFromRemote() {
        try {
            Log.d(TAG, "Refreshing data from remote")
            val groups = remoteDataSource.getAvailableGroups()
            offlineDataSource.cacheGroups(groups)
            Log.d(TAG, "Successfully refreshed ${groups.size} groups from remote")
        } catch (e: Exception) {
            Log.w(TAG, "Failed to refresh data from remote", e)
        }
    }

    /**
     * Get pending sync count
     */
    fun getPendingSyncCountFlow(): Flow<Int> {
        return syncQueueManager.getPendingSyncCountFlow()
    }

    /**
     * Force sync offline changes (for manual sync)
     */
    suspend fun forceSyncOfflineChanges(): Boolean {
        val isConnected = networkConnectivityManager.isConnected().first()
        return if (isConnected) {
            syncOfflineChanges()
            true
        } else {
            false
        }
    }
}
