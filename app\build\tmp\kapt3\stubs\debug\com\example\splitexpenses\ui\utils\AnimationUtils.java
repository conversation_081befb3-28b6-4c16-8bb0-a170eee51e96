package com.example.splitexpenses.ui.utils;

/**
 * Animation utilities for optimizing performance during screen transitions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0007R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0007R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0007R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006\u0014"}, d2 = {"Lcom/example/splitexpenses/ui/utils/AnimationUtils;", "", "()V", "colorAnimationSpec", "Landroidx/compose/animation/core/AnimationSpec;", "Landroidx/compose/ui/graphics/Color;", "getColorAnimationSpec", "()Landroidx/compose/animation/core/AnimationSpec;", "emphasisAnimationSpec", "", "getEmphasisAnimationSpec", "navigationAnimationSpec", "getNavigationAnimationSpec", "quickAnimationSpec", "getQuickAnimationSpec", "sizeAnimationSpec", "Landroidx/compose/animation/core/FiniteAnimationSpec;", "Landroidx/compose/ui/unit/IntSize;", "getSizeAnimationSpec", "()Landroidx/compose/animation/core/FiniteAnimationSpec;", "app_debug"})
public final class AnimationUtils {
    
    /**
     * Standard animation spec for navigation transitions
     * Optimized for smooth performance across different devices
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.core.AnimationSpec<java.lang.Float> navigationAnimationSpec = null;
    
    /**
     * Fast animation spec for quick UI updates
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.core.AnimationSpec<java.lang.Float> quickAnimationSpec = null;
    
    /**
     * Slow animation spec for emphasis
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.core.AnimationSpec<java.lang.Float> emphasisAnimationSpec = null;
    
    /**
     * Animation spec for color transitions
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.core.AnimationSpec<androidx.compose.ui.graphics.Color> colorAnimationSpec = null;
    
    /**
     * Animation spec for size transitions
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.core.FiniteAnimationSpec<androidx.compose.ui.unit.IntSize> sizeAnimationSpec = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.splitexpenses.ui.utils.AnimationUtils INSTANCE = null;
    
    private AnimationUtils() {
        super();
    }
    
    /**
     * Standard animation spec for navigation transitions
     * Optimized for smooth performance across different devices
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.AnimationSpec<java.lang.Float> getNavigationAnimationSpec() {
        return null;
    }
    
    /**
     * Fast animation spec for quick UI updates
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.AnimationSpec<java.lang.Float> getQuickAnimationSpec() {
        return null;
    }
    
    /**
     * Slow animation spec for emphasis
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.AnimationSpec<java.lang.Float> getEmphasisAnimationSpec() {
        return null;
    }
    
    /**
     * Animation spec for color transitions
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.AnimationSpec<androidx.compose.ui.graphics.Color> getColorAnimationSpec() {
        return null;
    }
    
    /**
     * Animation spec for size transitions
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.FiniteAnimationSpec<androidx.compose.ui.unit.IntSize> getSizeAnimationSpec() {
        return null;
    }
}