package com.example.splitexpenses.data

import java.io.InputStream
import java.io.OutputStream
import java.util.UUID

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

import android.content.Context
import android.net.Uri
import android.util.Log

import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.ValueEventListener
import com.google.firebase.database.ktx.database
import com.google.firebase.ktx.Firebase

import com.example.splitexpenses.util.CsvImportError
import com.example.splitexpenses.util.CsvImportResult
import com.example.splitexpenses.util.CsvUtil

class Repository(val context: Context) {
    private val TAG = "Repository"
    val database = Firebase.database.reference
    val userPreferences = UserPreferences(context)
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    // Group data
    private val _currentGroup = MutableStateFlow<GroupData?>(null)
    val currentGroup: StateFlow<GroupData?> = _currentGroup

    private val _availableGroups = MutableStateFlow<List<GroupData>>(emptyList())
    val availableGroups: StateFlow<List<GroupData>> = _availableGroups

    // Loading states
    private val _isLoadingGroups = MutableStateFlow(false)
    val isLoadingGroups: StateFlow<Boolean> = _isLoadingGroups

    private val _isLoadingCurrentGroup = MutableStateFlow(false)
    val isLoadingCurrentGroup: StateFlow<Boolean> = _isLoadingCurrentGroup

    // Error states
    private val _groupsError = MutableStateFlow<String?>(null)
    val groupsError: StateFlow<String?> = _groupsError

    private val _currentGroupError = MutableStateFlow<String?>(null)
    val currentGroupError: StateFlow<String?> = _currentGroupError

    // StateFlow to notify when user loses access to current group
    private val _accessLost = MutableStateFlow(false)
    val accessLost: StateFlow<Boolean> = _accessLost

    // Firebase listeners
    private var availableGroupsListener: ValueEventListener? = null
    private var currentGroupListener: ValueEventListener? = null

    /**
     * Load available groups once (for backward compatibility)
     */
    suspend fun loadAvailableGroups() {
        try {
            _isLoadingGroups.value = true
            _groupsError.value = null

            val snapshot = database.child("groups").get().await()
            processAvailableGroupsSnapshot(snapshot)
        } catch (e: Exception) {
            handleError("load groups", e)
        } finally {
            _isLoadingGroups.value = false
        }
    }

    /**
     * Start listening for real-time updates to available groups
     */
    fun startListeningForAvailableGroups() {
        // Remove any existing listener
        stopListeningForAvailableGroups()

        _isLoadingGroups.value = true
        _groupsError.value = null

        // Create a new listener
        availableGroupsListener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                coroutineScope.launch {
                    processAvailableGroupsSnapshot(snapshot)
                    _isLoadingGroups.value = false
                }
            }

            override fun onCancelled(error: DatabaseError) {
                println("Error listening for groups: ${error.message}")
                _groupsError.value = "Failed to sync groups: ${error.message}"
                _isLoadingGroups.value = false
            }
        }

        // Attach the listener
        database.child("groups").addValueEventListener(availableGroupsListener!!)
    }

    /**
     * Stop listening for real-time updates to available groups
     */
    fun stopListeningForAvailableGroups() {
        availableGroupsListener?.let {
            database.child("groups").removeEventListener(it)
            availableGroupsListener = null
        }
    }

    /**
     * Process a snapshot of available groups
     */
    private fun processAvailableGroupsSnapshot(snapshot: DataSnapshot) {
        val groups = snapshot.children.mapNotNull { it.getValue(GroupData::class.java) }

        // Filter groups to only show those where the current user's UID is in the allowedUsers list
        // If allowedUsers is empty, show the group (for backward compatibility)
        val currentUserUid = userPreferences.getDeviceUid()
        val filteredGroups = groups.filter { group ->
            group.allowedUsers.isEmpty() || currentUserUid in group.allowedUsers
        }

        _availableGroups.value = filteredGroups
    }

    fun clearCurrentGroup() {
        stopListeningForCurrentGroup()
        _currentGroup.value = null
    }

    /**
     * Reset the access lost flag (call this when navigating back to group list)
     */
    fun resetAccessLost() {
        _accessLost.value = false
    }

    suspend fun createGroup(name: String, members: List<String>, currentUser: String): String {
        val groupId = UUID.randomUUID().toString()
        val currentUserUid = userPreferences.getDeviceUid()

        // Create initial memberUidMap with the current user
        val initialMemberUidMap = if (members.contains(currentUser)) {
            mapOf(currentUser to currentUserUid)
        } else {
            emptyMap()
        }

        // Create the group with the current user's UID in the allowedUsers list
        // and set the current user as the creator
        val group = GroupData(
            id = groupId,
            name = name,
            members = members,
            allowedUsers = listOf(currentUserUid),
            memberUidMap = initialMemberUidMap,
            creatorUid = currentUserUid,  // Set the creator UID to the current user's UID
            categories = getDefaultCategories()  // Initialize with default categories
        )

        try {
            database.child("groups").child(groupId).setValue(group).await()
            _currentGroup.value = group

            // Save the mapping between the user's name and UID for this group
            userPreferences.saveUserForGroup(groupId, currentUser)

            // Start listening for real-time updates
            startListeningForCurrentGroup(groupId)
        } catch (e: Exception) {
            handleError("create group", e, true)
            // Re-throw the exception to notify the caller
            throw e
        }

        return groupId
    }

    suspend fun joinGroup(groupId: String, currentUser: String) {
        val group = fetchGroupById(groupId)
        val currentUserUid = userPreferences.getDeviceUid()

        group?.let {
            // Check if the user is allowed to join this group
            if (it.allowedUsers.isEmpty() || currentUserUid in it.allowedUsers) {
                // Update the memberUidMap to include this user
                val updatedMemberUidMap = it.memberUidMap.toMutableMap()
                updatedMemberUidMap[currentUser] = currentUserUid

                val updatedGroup = it.copy(
                    memberUidMap = updatedMemberUidMap
                )

                updateGroup(groupId, updatedGroup)
                userPreferences.saveUserForGroup(groupId, currentUser)

                // Start listening for real-time updates to this group
                startListeningForCurrentGroup(groupId)
            }
        }
    }

    /**
     * Start listening for real-time updates to the current group
     */
    fun startListeningForCurrentGroup(groupId: String) {
        // Remove any existing listener
        stopListeningForCurrentGroup()

        _isLoadingCurrentGroup.value = true
        _currentGroupError.value = null

        // Create a new listener
        currentGroupListener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                coroutineScope.launch {
                    val group = snapshot.getValue(GroupData::class.java)
                    if (group != null) {
                        // Check if the current user still has access to this group
                        val currentUserUid = userPreferences.getDeviceUid()
                        val hasAccess = group.allowedUsers.isEmpty() || currentUserUid in group.allowedUsers

                        if (hasAccess) {
                            _currentGroup.value = group
                            _accessLost.value = false
                        } else {
                            // User has lost access to this group
                            println("Repository: User has lost access to group ${group.id}")
                            println("Repository: Current user UID: $currentUserUid")
                            println("Repository: Group allowedUsers: ${group.allowedUsers}")
                            _currentGroup.value = null
                            _accessLost.value = true
                            stopListeningForCurrentGroup()
                        }
                    }
                    _isLoadingCurrentGroup.value = false
                }
            }

            override fun onCancelled(error: DatabaseError) {
                println("Error listening for current group: ${error.message}")
                _currentGroupError.value = "Failed to sync group: ${error.message}"
                _isLoadingCurrentGroup.value = false
            }
        }

        // Attach the listener
        database.child("groups").child(groupId).addValueEventListener(currentGroupListener!!)
    }

    /**
     * Stop listening for real-time updates to the current group
     */
    fun stopListeningForCurrentGroup() {
        currentGroupListener?.let {
            val currentGroupId = _currentGroup.value?.id
            if (currentGroupId != null) {
                database.child("groups").child(currentGroupId).removeEventListener(it)
            }
            currentGroupListener = null
        }
    }

    fun getSavedUserForGroup(groupId: String): String? {
        return userPreferences.getSavedUserForGroup(groupId)
    }

    suspend fun addExpense(
        amount: Double,
        description: String,
        paidBy: String,
        splitBetween: List<String>,
        category: String,
        date: Long,
        isCategoryLocked: Boolean = false
    ) {
        val currentGroup = _currentGroup.value ?: return
        val expenseId = UUID.randomUUID().toString()
        val expense = Expense(
            id = expenseId,
            amount = amount,
            description = description,
            paidBy = paidBy,
            splitBetween = splitBetween,
            category = category,
            date = date,
            isCategoryLocked = isCategoryLocked
        )

        val updatedExpenses = currentGroup.expenses + expense
        // Use the more efficient updateGroupField method
        updateGroupField(currentGroup.id, "expenses", updatedExpenses)
    }

    suspend fun updateExpense(
        expenseId: String,
        amount: Double,
        description: String,
        paidBy: String,
        splitBetween: List<String>,
        category: String,
        date: Long,
        isCategoryLocked: Boolean = false
    ) {
        val currentGroup = _currentGroup.value ?: return
        val expenseIndex = currentGroup.expenses.indexOfFirst { it.id == expenseId }
        if (expenseIndex == -1) return

        val updatedExpense = Expense(
            id = expenseId,
            amount = amount,
            description = description,
            paidBy = paidBy,
            splitBetween = splitBetween,
            category = category,
            date = date,
            timestamp = currentGroup.expenses[expenseIndex].timestamp,
            isCategoryLocked = isCategoryLocked
        )

        val updatedExpenses = currentGroup.expenses.toMutableList()
        updatedExpenses[expenseIndex] = updatedExpense

        // Use the more efficient updateGroupField method
        updateGroupField(currentGroup.id, "expenses", updatedExpenses)
    }

    /**
     * Calculate financial data for each user in the current group
     * @param group The group to calculate finances for
     * @return A list of UserFinance objects
     */
    fun calculateFinances(group: GroupData): List<UserFinance> {
        val balances = mutableMapOf<String, Double>()
        val totalExpenses = mutableMapOf<String, Double>()

        // Initialize balances for all members
        group.members.forEach { member ->
            balances[member] = 0.0
            totalExpenses[member] = 0.0
        }

        // Calculate balances
        group.expenses.forEach { expense ->
            val amountPerPerson = expense.amount / expense.splitBetween.size

            // Add to payer's balance
            balances[expense.paidBy] = (balances[expense.paidBy] ?: 0.0) + expense.amount
            totalExpenses[expense.paidBy] = (totalExpenses[expense.paidBy] ?: 0.0) + expense.amount

            // Subtract from each person who owes money
            expense.splitBetween.forEach { member ->
                balances[member] = (balances[member] ?: 0.0) - amountPerPerson
            }
        }

        // Combine balances and totalExpenses for each user and map to UserFinance
        return balances.map { (userId, userBalance) ->
            val userExpense = totalExpenses[userId] ?: 0.0
            UserFinance(userId, userExpense, userBalance)
        }
    }

    /**
     * Calculate settlements for the current group
     * @param finances The list of UserFinance objects
     * @return A list of Triple objects (debtor, creditor, amount)
     */
    fun calculateSettlements(finances: List<UserFinance>): List<Triple<String, String, Double>> {
        val settlements = mutableListOf<Triple<String, String, Double>>()

        // Separate debtors and creditors
        val debtors = finances.filter { it.userBalance < 0 }
            .sortedBy { it.userBalance } // Sort by balance (ascending, most negative first)
        val creditors = finances.filter { it.userBalance > 0 }
            .sortedByDescending { it.userBalance } // Sort by balance (descending, most positive first)

        // Create a mutable copy of the debtors and creditors to work with
        val mutableDebtors = debtors.map { it.copy() }.toMutableList()
        val mutableCreditors = creditors.map { it.copy() }.toMutableList()

        // Calculate settlements
        while (mutableDebtors.isNotEmpty() && mutableCreditors.isNotEmpty()) {
            val debtor = mutableDebtors.first()
            val creditor = mutableCreditors.first()

            // Calculate the amount to settle (minimum of the absolute values)
            val amount = minOf(kotlin.math.abs(debtor.userBalance), creditor.userBalance)

            // Add the settlement
            settlements.add(Triple(debtor.userId, creditor.userId, amount))

            // Update balances (using mutable copies)
            val updatedDebtor = debtor.copy(userBalance = debtor.userBalance + amount)
            val updatedCreditor = creditor.copy(userBalance = creditor.userBalance - amount)

            // Replace the objects in the lists
            mutableDebtors[0] = updatedDebtor
            mutableCreditors[0] = updatedCreditor

            // Remove settled users
            if (kotlin.math.abs(updatedDebtor.userBalance) < 0.01) {
                mutableDebtors.removeAt(0)
            }

            if (kotlin.math.abs(updatedCreditor.userBalance) < 0.01) {
                mutableCreditors.removeAt(0)
            }
        }

        return settlements
    }

    /**
     * Calculate financial data for the current group
     * @return A list of UserFinance objects
     */
    fun calculateFinance(): List<UserFinance> {
        val group = _currentGroup.value ?: return emptyList()
        return calculateFinances(group)
    }

    suspend fun deleteExpense(expenseId: String) {
        try {
            println("Repository: Deleting expense with ID: $expenseId")

            val currentGroup = _currentGroup.value
            if (currentGroup == null) {
                println("Cannot delete expense: No current group")
                return
            }

            if (expenseId.isEmpty()) {
                println("Cannot delete expense: Empty expense ID")
                return
            }

            // Find the expense to make sure it exists
            val expenseToDelete = currentGroup.expenses.find { it.id == expenseId }
            if (expenseToDelete == null) {
                println("Cannot delete expense: Expense not found with ID $expenseId")
                return
            }

            // Filter out the expense to be deleted
            val updatedExpenses = currentGroup.expenses.filter { it.id != expenseId }

            // Use the more efficient updateGroupField method
            updateGroupField(currentGroup.id, "expenses", updatedExpenses)

            println("Expense deleted successfully: $expenseId")
        } catch (e: Exception) {
            println("Error in deleteExpense: ${e.message}")
            e.printStackTrace()
            handleError("delete expense $expenseId", e, true)
            throw e
        }
    }

    suspend fun deleteExpenses(expenseIds: Set<String>) {
        try {
            val currentGroup = _currentGroup.value ?: return

            // Make sure we have valid expense IDs
            if (expenseIds.isEmpty()) {
                println("No expense IDs provided for deletion")
                return
            }

            // Filter out the expenses to be deleted
            val updatedExpenses = currentGroup.expenses.filter { it.id !in expenseIds }

            // Use the more efficient updateGroupField method
            updateGroupField(currentGroup.id, "expenses", updatedExpenses)

            println("Deleted ${expenseIds.size} expenses successfully")
        } catch (e: Exception) {
            handleError("delete expenses", e, true)
            throw e
        }
    }

    suspend fun deleteGroup(groupId: String): Boolean {
        try {
            println("Repository: Deleting group with ID: $groupId")

            // Get the group data
            val group = fetchGroupById(groupId)
            if (group == null) {
                println("Repository: Group not found with ID: $groupId")
                return false
            }

            // Check if the current user is the creator
            val currentUserUid = userPreferences.getDeviceUid()
            if (group.creatorUid != currentUserUid) {
                println("Repository: Current user is not the creator of the group. Only the creator can delete the group.")
                return false
            }

            // Stop listening for updates to this group if it's the current group
            if (_currentGroup.value?.id == groupId) {
                println("Stopping listeners for current group")
                stopListeningForCurrentGroup()
            }

            // Delete the group from Firebase
            println("Removing group from Firebase")
            database.child("groups").child(groupId).removeValue().await()

            // Clear current group if it's the one we just deleted
            if (_currentGroup.value?.id == groupId) {
                println("Clearing current group state")
                _currentGroup.value = null
            }

            // Refresh the list of available groups
            println("Refreshing available groups")
            if (availableGroupsListener != null) {
                // If we have a real-time listener, it will update automatically
                println("Using real-time listener for group updates")
            } else {
                // Otherwise, manually refresh
                println("Manually refreshing available groups")
                loadAvailableGroups()
            }

            println("Group deleted successfully: $groupId")
            return true
        } catch (e: Exception) {
            println("Error in deleteGroup: ${e.message}")
            e.printStackTrace()
            handleError("delete group $groupId", e, groupId == _currentGroup.value?.id)
            throw e
        }
    }

    suspend fun updateGroupMembers(groupId: String, members: List<String>): Boolean {
        // Get the group data
        val group = fetchGroupById(groupId) ?: return false

        // Get the current user's UID
        val currentUserUid = userPreferences.getDeviceUid()

        // Check if the current user is the creator or if they're only updating their own name
        val currentUserName = userPreferences.getSavedUserForGroup(groupId)
        val isCreator = group.creatorUid == currentUserUid

        // If the user is not the creator, they can only update their own name
        if (!isCreator) {
            // Get the current members
            val currentMembers = group.members

            // Check if the user is trying to remove members other than themselves
            val removedMembers = currentMembers.filter { it !in members }
            if (removedMembers.isNotEmpty() && (removedMembers.size > 1 || (removedMembers.size == 1 && removedMembers[0] != currentUserName))) {
                println("Repository: Only the creator can remove members other than themselves")
                return false
            }
        }

        // Use the more efficient updateGroupField method
        updateGroupField(groupId, "members", members)
        return true
    }

    /**
     * Handle Firebase operation errors consistently
     * @param operation A description of the operation that failed
     * @param e The exception that was thrown
     * @param isCurrentGroup Whether this is related to the current group or all groups
     */
    private fun handleError(operation: String, e: Exception, isCurrentGroup: Boolean = false) {
        val errorMessage = "Failed to $operation: ${e.message}"
        println(errorMessage)

        if (isCurrentGroup) {
            _currentGroupError.value = errorMessage
        } else {
            _groupsError.value = errorMessage
        }
    }

    /**
     * Fetch a group by ID from Firebase
     * @param groupId The ID of the group to fetch
     * @return The group data or null if not found
     */
    private suspend fun fetchGroupById(groupId: String): GroupData? {
        return try {
            val snapshot = database.child("groups").child(groupId).get().await()
            snapshot.getValue(GroupData::class.java)
        } catch (e: Exception) {
            handleError("fetch group $groupId", e, groupId == _currentGroup.value?.id)
            null
        }
    }

    /**
     * Update a group in Firebase and update local state if needed
     * @param groupId The ID of the group to update
     * @param updatedGroup The updated group data
     */
    private suspend fun updateGroup(groupId: String, updatedGroup: GroupData) {
        try {
            database.child("groups").child(groupId).setValue(updatedGroup).await()

            // Update current group if it's the one we're modifying
            if (_currentGroup.value?.id == groupId) {
                _currentGroup.value = updatedGroup
            }

            // Refresh available groups if we're not using real-time listeners
            if (availableGroupsListener == null) {
                loadAvailableGroups()
            }
        } catch (e: Exception) {
            handleError("update group $groupId", e, groupId == _currentGroup.value?.id)
            throw e
        }
    }

    /**
     * Update a specific field in a group for better performance
     * @param groupId The ID of the group to update
     * @param field The field to update (e.g., "members", "allowedUsers")
     * @param value The new value for the field
     */
    private suspend fun updateGroupField(groupId: String, field: String, value: Any) {
        try {
            database.child("groups").child(groupId).child(field).setValue(value).await()

            // Update current group if it's the one we're modifying
            val currentGroup = _currentGroup.value
            if (currentGroup?.id == groupId) {
                // Create an updated group with the new field value
                // This is a bit complex because we need to handle different field types
                val updatedGroup = when (field) {
                    "members" -> {
                        @Suppress("UNCHECKED_CAST")
                        currentGroup.copy(members = value as List<String>)
                    }
                    "allowedUsers" -> {
                        @Suppress("UNCHECKED_CAST")
                        currentGroup.copy(allowedUsers = value as List<String>)
                    }
                    "expenses" -> {
                        @Suppress("UNCHECKED_CAST")
                        currentGroup.copy(expenses = value as List<Expense>)
                    }
                    "name" -> currentGroup.copy(name = value as String)
                    "memberUidMap" -> {
                        @Suppress("UNCHECKED_CAST")
                        currentGroup.copy(memberUidMap = value as Map<String, String>)
                    }
                    "categories" -> {
                        @Suppress("UNCHECKED_CAST")
                        currentGroup.copy(categories = value as List<Category>)
                    }
                    "memberAvatars" -> {
                        @Suppress("UNCHECKED_CAST")
                        currentGroup.copy(memberAvatars = value as Map<String, String>)
                    }
                    else -> currentGroup // No change for unknown fields
                }
                _currentGroup.value = updatedGroup
            }

            // Refresh available groups if we're not using real-time listeners
            if (availableGroupsListener == null && (field == "name" || field == "allowedUsers")) {
                loadAvailableGroups()
            }
        } catch (e: Exception) {
            handleError("update group field $field in $groupId", e, groupId == _currentGroup.value?.id)
            throw e
        }
    }

    /**
     * Adds a user to the allowedUsers list for a group by their member name
     * The system will automatically convert the member name to a UID
     */
    suspend fun addAllowedUserByName(groupId: String, memberName: String) {
        val group = fetchGroupById(groupId) ?: return

        // Get the UID for this member name, or generate a temporary one
        val userUid = userPreferences.getUidForUserInGroup(groupId, memberName)
            ?: UUID.randomUUID().toString()

        // Save the mapping for future reference
        userPreferences.saveUserForGroup(groupId, memberName)

        // Only add if not already in the list
        if (userUid !in group.allowedUsers) {
            val updatedAllowedUsers = group.allowedUsers + userUid
            // Use the more efficient updateGroupField method
            updateGroupField(groupId, "allowedUsers", updatedAllowedUsers)
        }
    }

    /**
     * Adds a user to the allowedUsers list for a group by their UID
     * This is kept for backward compatibility
     */
    suspend fun addAllowedUser(groupId: String, userUid: String) {
        val group = fetchGroupById(groupId) ?: return

        // Only add if not already in the list
        if (userUid !in group.allowedUsers) {
            val updatedAllowedUsers = group.allowedUsers + userUid
            // Use the more efficient updateGroupField method
            updateGroupField(groupId, "allowedUsers", updatedAllowedUsers)
        }
    }

    /**
     * Removes a user from the allowedUsers list for a group by their member name
     */
    suspend fun removeAllowedUserByName(groupId: String, memberName: String) {
        // Get the UID for this member name
        val userUid = userPreferences.getUidForUserInGroup(groupId, memberName) ?: return
        removeAllowedUser(groupId, userUid)
    }

    /**
     * Removes a member from a group and kicks them out (removes from allowedUsers)
     * This should only be called by the group creator
     * @param groupId The ID of the group
     * @param memberName The name of the member to remove
     * @return True if successful, false otherwise
     */
    suspend fun removeMemberAndKick(groupId: String, memberName: String): Boolean {
        try {
            // Check if the current user is the creator
            if (!isCurrentUserGroupCreator(groupId)) {
                println("Repository: Only the creator can remove members")
                return false
            }

            // Get the group data
            val group = fetchGroupById(groupId) ?: return false

            // Get the UID for this member name from the memberUidMap (more reliable than local storage)
            val memberUid = group.memberUidMap[memberName]
            println("Repository: removeMemberAndKick - Member: $memberName, UID: $memberUid")

            // Remove the member from the members list
            val updatedMembers = group.members.filter { it != memberName }
            println("Repository: removeMemberAndKick - Updating members from ${group.members} to $updatedMembers")
            updateGroupField(groupId, "members", updatedMembers)

            // Remove the member from the memberUidMap
            val updatedMemberUidMap = group.memberUidMap.toMutableMap()
            updatedMemberUidMap.remove(memberName)
            println("Repository: removeMemberAndKick - Updating memberUidMap, removed $memberName")
            updateGroupField(groupId, "memberUidMap", updatedMemberUidMap)

            // Remove the member from the allowedUsers list if we have their UID
            if (memberUid != null) {
                val updatedAllowedUsers = group.allowedUsers.filter { it != memberUid }
                println("Repository: removeMemberAndKick - Updating allowedUsers from ${group.allowedUsers} to $updatedAllowedUsers")
                updateGroupField(groupId, "allowedUsers", updatedAllowedUsers)
            } else {
                println("Repository: removeMemberAndKick - Warning: Could not find UID for member $memberName, cannot remove from allowedUsers")
            }

            return true
        } catch (e: Exception) {
            handleError("remove member and kick", e)
            return false
        }
    }

    /**
     * Removes a user from the allowedUsers list for a group by their UID
     */
    suspend fun removeAllowedUser(groupId: String, userUid: String) {
        val group = fetchGroupById(groupId) ?: return

        val updatedAllowedUsers = group.allowedUsers.filter { it != userUid }
        // Use the more efficient updateGroupField method
        updateGroupField(groupId, "allowedUsers", updatedAllowedUsers)
    }

    /**
     * Gets a list of member names who are allowed to access the group
     */
    fun getAllowedMemberNames(groupId: String): List<String> {
        val group = _currentGroup.value ?: return emptyList()
        if (group.id != groupId) return emptyList()

        return group.allowedUsers.mapNotNull { uid ->
            userPreferences.getUserNameForUidInGroup(groupId, uid) ?: uid
        }
    }

    /**
     * Gets information about all members in a group, with their assignment status
     * @param groupId The ID of the group
     * @return Pair of (all members, assigned members)
     */
    suspend fun getGroupMembersWithStatus(groupId: String): Pair<List<String>, List<String>> {
        // Get the group data
        val group = fetchGroupById(groupId) ?: return Pair(emptyList(), emptyList())

        // Get all member names
        val allMembers = group.members

        // Get the UIDs of all members who have already been assigned
        val assignedMemberNames = mutableListOf<String>()

        // First, find the creator's name
        val creatorUid = group.creatorUid
        if (creatorUid.isNotEmpty()) {
            // Try to find the creator's name from the memberUidMap
            val creatorName = group.memberUidMap.entries.find { it.value == creatorUid }?.key
            if (creatorName != null && creatorName in allMembers) {
                println("Repository: getGroupMembersWithStatus: Found creator name: $creatorName")
                assignedMemberNames.add(creatorName)
            } else {
                // If we can't find the creator's name in the memberUidMap, try to get it from local storage
                val name = userPreferences.getUserNameForUidInGroup(groupId, creatorUid)
                if (name != null && name in allMembers) {
                    println("Repository: getGroupMembersWithStatus: Found creator name from local storage: $name")
                    assignedMemberNames.add(name)
                } else {
                    println("Repository: getGroupMembersWithStatus: Could not find creator name for UID: $creatorUid")
                }
            }
        }

        // Then add all other assigned members
        for (uid in group.allowedUsers) {
            val name = userPreferences.getUserNameForUidInGroup(groupId, uid)
            if (name != null && name !in assignedMemberNames) {
                assignedMemberNames.add(name)
            }
        }

        // Also add all members that have entries in the memberUidMap
        for (memberName in group.memberUidMap.keys) {
            if (memberName in allMembers && memberName !in assignedMemberNames) {
                assignedMemberNames.add(memberName)
            }
        }

        println("Repository: getGroupMembersWithStatus: All members: $allMembers, Assigned members: $assignedMemberNames")
        return Pair(allMembers, assignedMemberNames)
    }

    /**
     * Gets a list of unassigned member names (members without an associated UID)
     */
    suspend fun getUnassignedMembers(groupId: String): List<String> {
        // Use the getGroupMembersWithStatus function to get consistent results
        val (allMembers, assignedMembers) = getGroupMembersWithStatus(groupId)

        // Return members who don't have an assigned UID yet
        return allMembers.filter { memberName -> memberName !in assignedMembers }
    }

    /**
     * Accept an invitation to join a group
     * @param groupId The ID of the group to join
     * @param memberName The name of the member to join as
     * @return True if successful, false otherwise
     */
    suspend fun acceptInvitation(groupId: String, memberName: String): Boolean {
        _isLoadingGroups.value = true
        _groupsError.value = null

        try {
            // Get the group data
            val group = fetchGroupById(groupId) ?: return false

            // Check if the member name is valid
            if (memberName !in group.members) return false

            // Get the current user's UID
            val currentUserUid = userPreferences.getDeviceUid()

            // Update the memberUidMap to include this user
            val updatedMemberUidMap = group.memberUidMap.toMutableMap()
            updatedMemberUidMap[memberName] = currentUserUid

            // Add the user to the allowedUsers list if not already there
            if (currentUserUid !in group.allowedUsers) {
                val updatedAllowedUsers = group.allowedUsers + currentUserUid
                val updatedGroup = group.copy(
                    allowedUsers = updatedAllowedUsers,
                    memberUidMap = updatedMemberUidMap
                )
                updateGroup(groupId, updatedGroup)
            } else {
                // Just update the memberUidMap
                val updatedGroup = group.copy(memberUidMap = updatedMemberUidMap)
                updateGroup(groupId, updatedGroup)
            }

            // Save the mapping between the member name and the UID
            userPreferences.saveUserForGroup(groupId, memberName)

            // Join the group - this will automatically start listening for real-time updates
            joinGroup(groupId, memberName)

            return true
        } catch (e: Exception) {
            handleError("accept invitation", e)
            return false
        } finally {
            _isLoadingGroups.value = false
        }
    }

    /**
     * Checks if the current user is the creator of the group
     * This method works for any group, not just the current one
     */
    fun isCurrentUserGroupCreator(groupId: String): Boolean {
        // First check if this is the current group (for efficiency)
        val currentGroup = _currentGroup.value
        if (currentGroup != null && currentGroup.id == groupId) {
            val currentUserUid = userPreferences.getDeviceUid()
            return currentGroup.creatorUid == currentUserUid
        }

        // If not the current group, check in the available groups
        val availableGroup = _availableGroups.value.find { it.id == groupId }
        if (availableGroup != null) {
            val currentUserUid = userPreferences.getDeviceUid()
            return availableGroup.creatorUid == currentUserUid
        }

        // If the group is not loaded yet, fetch it from Firebase
        return runBlocking {
            try {
                val group = fetchGroupById(groupId)
                if (group != null) {
                    val currentUserUid = userPreferences.getDeviceUid()
                    group.creatorUid == currentUserUid
                } else {
                    false
                }
            } catch (e: Exception) {
                println("Error checking if user is creator: ${e.message}")
                false
            }
        }
    }

    /**
     * Refresh the current group data from Firebase
     * This is kept for backward compatibility, but real-time updates are preferred
     */
    suspend fun refreshCurrentGroup() {
        val currentGroupId = _currentGroup.value?.id ?: return

        // If we're already listening for updates, just update the loading state
        if (currentGroupListener != null) {
            _isLoadingCurrentGroup.value = true
            // Wait a bit to show the loading indicator
            withContext(Dispatchers.IO) {
                kotlinx.coroutines.delay(300)
                _isLoadingCurrentGroup.value = false
            }
            return
        }

        try {
            _isLoadingCurrentGroup.value = true
            _currentGroupError.value = null

            val group = fetchGroupById(currentGroupId)
            if (group != null) {
                _currentGroup.value = group
            }
        } catch (e: Exception) {
            handleError("refresh current group", e, true)
        } finally {
            _isLoadingCurrentGroup.value = false
        }
    }

    /**
     * Update the categories for a group
     * @param groupId The ID of the group to update
     * @param categories The new list of categories
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateGroupCategories(groupId: String, categories: List<Category>): Boolean {
        try {
            // Get the group data
            val group = fetchGroupById(groupId) ?: return false

            // Update the categories field
            updateGroupField(groupId, "categories", categories)

            return true
        } catch (e: Exception) {
            handleError("update categories", e, groupId == _currentGroup.value?.id)
            return false
        }
    }

    /**
     * Update the group name
     * @param groupId The ID of the group to update
     * @param newName The new name for the group
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateGroupName(groupId: String, newName: String): Boolean {
        try {
            // Get the group data
            val group = fetchGroupById(groupId) ?: return false

            // Check if the current user is the creator
            val currentUserUid = userPreferences.getDeviceUid()
            if (group.creatorUid != currentUserUid) {
                println("Repository: Only the creator can update the group name")
                return false
            }

            // Update the name field
            updateGroupField(groupId, "name", newName)
            return true
        } catch (e: Exception) {
            handleError("update group name", e, groupId == _currentGroup.value?.id)
            return false
        }
    }

    /**
     * Update a member's avatar
     * @param groupId The ID of the group
     * @param memberName The name of the member
     * @param avatarEmoji The emoji to use as the avatar, or null to remove the avatar
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateMemberAvatar(groupId: String, memberName: String, avatarEmoji: String?): Boolean {
        try {
            // Get the group data
            val group = fetchGroupById(groupId) ?: return false

            // Check if the member exists in the group
            if (memberName !in group.members) {
                println("Repository: Member $memberName not found in group")
                return false
            }

            // Check if the current user is updating their own avatar
            val currentUserName = userPreferences.getSavedUserForGroup(groupId)
            if (currentUserName != memberName) {
                println("Repository: Users can only update their own avatar")
                return false
            }

            // Update the memberAvatars map
            val updatedMemberAvatars = group.memberAvatars.toMutableMap()

            if (avatarEmoji == null) {
                // Remove the avatar if null is passed
                updatedMemberAvatars.remove(memberName)
                println("Repository: Removing avatar for member $memberName")
            } else {
                // Otherwise update with the new emoji
                updatedMemberAvatars[memberName] = avatarEmoji
                println("Repository: Setting avatar for member $memberName to $avatarEmoji")
            }

            println("Repository: Updated memberAvatars map: $updatedMemberAvatars")

            // Update the field
            updateGroupField(groupId, "memberAvatars", updatedMemberAvatars)

            // Also update the current group in memory to ensure UI updates immediately
            _currentGroup.value = _currentGroup.value?.copy(memberAvatars = updatedMemberAvatars)
            return true
        } catch (e: Exception) {
            handleError("update member avatar", e, groupId == _currentGroup.value?.id)
            return false
        }
    }

    /**
     * Update a member's name while preserving their UID
     * @param groupId The ID of the group
     * @param oldMemberName The current name of the member
     * @param newMemberName The new name for the member
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateMemberName(groupId: String, oldMemberName: String, newMemberName: String): Boolean {
        try {
            // Get the group data
            val group = fetchGroupById(groupId) ?: return false

            // Check if the member exists in the group
            if (oldMemberName !in group.members) {
                println("Repository: Member $oldMemberName not found in group")
                return false
            }

            // Check if the new name is already taken
            if (newMemberName in group.members) {
                println("Repository: Name $newMemberName is already taken")
                return false
            }

            // Check if the current user is updating their own name
            val currentUserName = userPreferences.getSavedUserForGroup(groupId)
            if (currentUserName != oldMemberName) {
                println("Repository: Users can only update their own name")
                return false
            }

            // Get the current user's UID
            val currentUserUid = userPreferences.getDeviceUid()

            // Update the members list
            val updatedMembers = group.members.map { if (it == oldMemberName) newMemberName else it }

            // Update the memberUidMap
            val updatedMemberUidMap = group.memberUidMap.toMutableMap()
            val memberUid = updatedMemberUidMap.remove(oldMemberName)
            if (memberUid != null) {
                updatedMemberUidMap[newMemberName] = memberUid
            }

            // Update the memberAvatars map if the member has an avatar
            val updatedMemberAvatars = group.memberAvatars.toMutableMap()
            val memberAvatar = updatedMemberAvatars.remove(oldMemberName)
            if (memberAvatar != null) {
                updatedMemberAvatars[newMemberName] = memberAvatar
            }

            // Update all fields
            val updatedGroup = group.copy(
                members = updatedMembers,
                memberUidMap = updatedMemberUidMap,
                memberAvatars = updatedMemberAvatars
            )
            updateGroup(groupId, updatedGroup)

            // Update the user preferences
            userPreferences.saveUserForGroup(groupId, newMemberName)

            return true
        } catch (e: Exception) {
            handleError("update member name", e, groupId == _currentGroup.value?.id)
            return false
        }
    }

    /**
     * Get the avatar for a member
     * @param groupId The ID of the group
     * @param memberName The name of the member
     * @return The avatar emoji or null if not found
     */
    fun getMemberAvatar(groupId: String, memberName: String): String? {
        val group = _currentGroup.value
        if (group?.id != groupId) {
            println("Repository: getMemberAvatar: Group ID mismatch - current: ${group?.id}, requested: $groupId")
            return null
        }

        // Return the avatar from the memberAvatars map
        // If the member has no avatar entry, return null to display the default account_outline
        val avatar = group.memberAvatars[memberName]
        println("Repository: getMemberAvatar: Avatar for $memberName = $avatar, memberAvatars = ${group.memberAvatars}")
        return avatar
    }

    /**
     * Clean up all resources when the app is closed
     */
    fun cleanup() {
        stopListeningForAvailableGroups()
        stopListeningForCurrentGroup()
    }

    /**
     * Export the current group to a CSV file
     * @param outputStream The output stream to write the CSV data to
     * @return True if export was successful, false otherwise
     */
    fun exportGroupToCsv(outputStream: OutputStream): Boolean {
        val currentGroup = _currentGroup.value ?: return false
        return CsvUtil.exportGroupToCsv(currentGroup, outputStream)
    }

    /**
     * Import a group from a CSV file
     * @param inputStream The input stream to read the CSV data from
     * @param currentUser Optional current user name to use (will use from CSV if not provided)
     * @return CsvImportResult containing the import results, errors, and warnings
     */
    suspend fun importGroupFromCsv(inputStream: InputStream, currentUser: String? = null): CsvImportResult {
        Log.d(TAG, "Starting group import from CSV")

        try {
            // Parse the CSV data
            val importResult = CsvUtil.importGroupFromCsv(inputStream, currentUser)

            // If import failed, return the result with errors
            if (!importResult.success || importResult.group == null) {
                Log.e(TAG, "CSV import failed: ${importResult.errors.firstOrNull()?.message ?: "Unknown error"}")
                return importResult
            }

            // Create the group in Firebase
            val currentUserUid = userPreferences.getDeviceUid()

            // Set the allowedUsers list to include the current user
            // and set the current user as the creator
            val groupWithAllowedUsers = importResult.group.copy(
                allowedUsers = listOf(currentUserUid),
                creatorUid = currentUserUid,  // Set the creator UID to the current user's UID
                categories = importResult.group.categories.ifEmpty { getDefaultCategories() }  // Use default categories if none provided
            )

            try {
                // Save the group to Firebase
                Log.d(TAG, "Saving imported group to Firebase: ${groupWithAllowedUsers.id}")
                database.child("groups").child(groupWithAllowedUsers.id).setValue(groupWithAllowedUsers).await()

                // Save the mapping between the user's name and UID for this group
                // Use the provided currentUser parameter or default to the first member in the group
                val userToSave = currentUser ?: importResult.group.members.firstOrNull() ?: ""
                userPreferences.saveUserForGroup(groupWithAllowedUsers.id, userToSave)

                // Set as current group and start listening for updates
                _currentGroup.value = groupWithAllowedUsers
                startListeningForCurrentGroup(groupWithAllowedUsers.id)

                Log.d(TAG, "Group import completed successfully")
                return importResult
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save imported group to Firebase", e)
                handleError("save imported group to Firebase", e, true)

                // Return a modified result with the Firebase error
                return CsvImportResult(
                    success = false,
                    group = importResult.group,
                    errors = importResult.errors + CsvImportError(
                        message = "Failed to save group to database: ${e.message}",
                        exception = e
                    ),
                    warnings = importResult.warnings,
                    totalRowsProcessed = importResult.totalRowsProcessed,
                    successfulRows = importResult.successfulRows
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error during CSV import", e)
            handleError("import group from CSV", e, true)

            return CsvImportResult(
                success = false,
                errors = listOf(CsvImportError(
                    message = "Unexpected error during import: ${e.message}",
                    exception = e
                ))
            )
        }
    }
}