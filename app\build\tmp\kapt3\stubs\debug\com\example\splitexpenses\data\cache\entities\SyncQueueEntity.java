package com.example.splitexpenses.data.cache.entities;

/**
 * Room entity for storing sync queue items
 * This tracks changes made while offline that need to be synced when online
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u001a\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0087\b\u0018\u00002\u00020\u0001:\u0001+BO\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00c6\u0003J\t\u0010 \u001a\u00020\tH\u00c6\u0003J\t\u0010!\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\"\u001a\u00020\fH\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J[\u0010%\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u00072\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u00032\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010&\u001a\u00020\'2\b\u0010(\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010)\u001a\u00020\u0003H\u00d6\u0001J\t\u0010*\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0011R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0016R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001c\u00a8\u0006,"}, d2 = {"Lcom/example/splitexpenses/data/cache/entities/SyncQueueEntity;", "", "id", "", "entityType", "Lcom/example/splitexpenses/data/cache/entities/SyncEntityType;", "entityId", "", "operationType", "Lcom/example/splitexpenses/data/cache/entities/SyncOperationType;", "data", "timestamp", "", "retryCount", "lastError", "(ILcom/example/splitexpenses/data/cache/entities/SyncEntityType;Ljava/lang/String;Lcom/example/splitexpenses/data/cache/entities/SyncOperationType;Ljava/lang/String;JILjava/lang/String;)V", "getData", "()Ljava/lang/String;", "getEntityId", "getEntityType", "()Lcom/example/splitexpenses/data/cache/entities/SyncEntityType;", "getId", "()I", "getLastError", "getOperationType", "()Lcom/example/splitexpenses/data/cache/entities/SyncOperationType;", "getRetryCount", "getTimestamp", "()J", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "hashCode", "toString", "Converters", "app_debug"})
@androidx.room.Entity(tableName = "sync_queue")
@androidx.room.TypeConverters(value = {com.example.splitexpenses.data.cache.entities.SyncQueueEntity.Converters.class})
public final class SyncQueueEntity {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final int id = 0;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.cache.entities.SyncEntityType entityType = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String entityId = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.cache.entities.SyncOperationType operationType = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String data = null;
    private final long timestamp = 0L;
    private final int retryCount = 0;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String lastError = null;
    
    public SyncQueueEntity(int id, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncEntityType entityType, @org.jetbrains.annotations.NotNull()
    java.lang.String entityId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncOperationType operationType, @org.jetbrains.annotations.NotNull()
    java.lang.String data, long timestamp, int retryCount, @org.jetbrains.annotations.Nullable()
    java.lang.String lastError) {
        super();
    }
    
    public final int getId() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.cache.entities.SyncEntityType getEntityType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getEntityId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.cache.entities.SyncOperationType getOperationType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getData() {
        return null;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    public final int getRetryCount() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLastError() {
        return null;
    }
    
    public final int component1() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.cache.entities.SyncEntityType component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.cache.entities.SyncOperationType component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final int component7() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.cache.entities.SyncQueueEntity copy(int id, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncEntityType entityType, @org.jetbrains.annotations.NotNull()
    java.lang.String entityId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncOperationType operationType, @org.jetbrains.annotations.NotNull()
    java.lang.String data, long timestamp, int retryCount, @org.jetbrains.annotations.Nullable()
    java.lang.String lastError) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * Type converters for Room database
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\bH\u0007J\u0010\u0010\t\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0007J\u0010\u0010\n\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0004H\u0007\u00a8\u0006\u000b"}, d2 = {"Lcom/example/splitexpenses/data/cache/entities/SyncQueueEntity$Converters;", "", "()V", "fromSyncEntityType", "", "value", "Lcom/example/splitexpenses/data/cache/entities/SyncEntityType;", "fromSyncOperationType", "Lcom/example/splitexpenses/data/cache/entities/SyncOperationType;", "toSyncEntityType", "toSyncOperationType", "app_debug"})
    public static final class Converters {
        
        public Converters() {
            super();
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String fromSyncEntityType(@org.jetbrains.annotations.NotNull()
        com.example.splitexpenses.data.cache.entities.SyncEntityType value) {
            return null;
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final com.example.splitexpenses.data.cache.entities.SyncEntityType toSyncEntityType(@org.jetbrains.annotations.NotNull()
        java.lang.String value) {
            return null;
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String fromSyncOperationType(@org.jetbrains.annotations.NotNull()
        com.example.splitexpenses.data.cache.entities.SyncOperationType value) {
            return null;
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final com.example.splitexpenses.data.cache.entities.SyncOperationType toSyncOperationType(@org.jetbrains.annotations.NotNull()
        java.lang.String value) {
            return null;
        }
    }
}