# CSV Export Format Improvements

## Overview
The CSV export functionality has been significantly enhanced to ensure consistent row lengths, better compatibility with the import functionality, and improved data integrity. The improvements focus on creating a standardized, robust export format that works seamlessly with the enhanced delimiter detection and parsing logic.

## Key Improvements

### 1. Standardized Row Structure
- **Consistent field count**: All CSV rows now have exactly the expected number of columns
- **No missing columns**: Empty fields are explicitly included as empty strings rather than omitted
- **Uniform formatting**: All data follows consistent formatting rules across rows
- **Field validation**: Export validates field counts match header expectations before writing

### 2. Enhanced Data Formatting

#### Group Data
- **Name**: Always present, defaults to "Exported Group" if blank
- **Members**: Pipe-separated list (`Alice|Bob|Charlie`), empty string if no members
- **Member Avatars**: Semicolon-separated key-value pairs (`Alice=👩|Bob=👨`), sorted by key for consistency
- **Categories**: Semicolon-separated category definitions (`Food~🍔~restaurant^lunch`), empty string if no categories

#### Expense Data
- **Amount**: Always formatted as decimal with 2 decimal places using US locale (`25.50`)
- **Description**: Always present, defaults to "Expense" if blank, properly escaped
- **PaidBy**: Always present, defaults to "Unknown" if blank, properly escaped
- **SplitBetween**: Always present, pipe-separated list, defaults to paidBy if empty
- **Category**: Always present, defaults to "None" if blank
- **Date**: Always present in consistent ISO format (`yyyy-MM-dd`)
- **IsCategoryLocked**: Always present as boolean string (`true`/`false`)

### 3. Improved Character Escaping
- **Delimiter handling**: Properly escapes commas, semicolons, and other special characters
- **Quote handling**: Uses double-quote escaping for CSV compatibility
- **Newline handling**: Properly escapes newlines within field values
- **Unicode support**: Correctly handles emoji and international characters

### 4. Data Validation and Compatibility
- **Pre-export validation**: Validates data for potential compatibility issues
- **Reserved character detection**: Warns about characters that could cause parsing issues
- **Field length validation**: Ensures fields don't exceed reasonable limits
- **Data integrity checks**: Validates relationships between fields

### 5. Round-Trip Testing
- **Automated testing**: Built-in function to test export/import round-trip compatibility
- **Data comparison**: Comprehensive comparison of original vs imported data
- **Issue reporting**: Detailed reporting of any data integrity issues
- **Debug support**: Extensive logging for troubleshooting

## Export Format Specification

### File Structure
```
Line 1: Group Header    - name,members,memberAvatars,categories
Line 2: Group Data      - "Group Name","Alice|Bob","Alice=👩|Bob=👨","Food~🍔~restaurant"
Line 3: Expense Header  - amount,description,paidBy,splitBetween,category,date,isCategoryLocked
Line 4+: Expense Data   - 25.50,"Lunch","Alice","Alice|Bob","Food","2024-01-15",true
```

### Field Formats

#### Members List Format
```
Alice|Bob|Charlie
```
- Pipe-separated (`|`) list of member names
- No spaces around separators
- Empty string if no members

#### Member Avatars Format
```
Alice=👩;Bob=👨;Charlie=🧑
```
- Semicolon-separated (`;`) key-value pairs
- Equals sign (`=`) separates name from avatar
- Sorted alphabetically by member name for consistency
- Empty string if no avatars

#### Categories Format
```
Food~🍔~restaurant^lunch;Transport~🚗~taxi^gas
```
- Semicolon-separated (`;`) category definitions
- Each category: `name~emoji~keywords`
- Keywords separated by caret (`^`)
- Empty keywords section allowed: `Food~🍔~`
- Empty string if no categories

#### Amount Format
```
25.50
```
- Always formatted with 2 decimal places
- Uses US locale (period as decimal separator)
- No currency symbols or thousands separators

#### Date Format
```
2024-01-15
```
- ISO format: `yyyy-MM-dd`
- Always 4-digit year, 2-digit month and day
- Zero-padded as needed

### Character Escaping Rules

#### CSV Quoting
- Fields containing commas, quotes, or newlines are wrapped in double quotes
- Double quotes within fields are escaped by doubling: `"` becomes `""`
- Example: `"He said ""Hello"" to me"` represents: `He said "Hello" to me`

#### Reserved Characters
- **Pipe (`|`)**: Used for list separation, avoid in member names
- **Semicolon (`;`)**: Used for map/category separation
- **Tilde (`~`)**: Used for category field separation
- **Caret (`^`)**: Used for keyword separation
- **Equals (`=`)**: Used for key-value separation

## Validation Features

### Pre-Export Validation
The export function validates data before writing:

```kotlin
val validationIssues = validateExportCompatibility(group)
```

Checks for:
- Blank group names
- Empty member lists
- Reserved characters in names/values
- Negative amounts
- Blank required fields

### Round-Trip Testing
Test export/import compatibility:

```kotlin
val testResult = CsvUtil.testExportImportRoundTrip(group)
if (testResult.success) {
    // Export/import works correctly
} else {
    // Handle issues: testResult.error
}
```

## Benefits

1. **Reliability**: Consistent format reduces parsing errors
2. **Compatibility**: Works seamlessly with enhanced import functionality
3. **Data Integrity**: All data preserved during export/import cycles
4. **Debugging**: Extensive logging and validation for troubleshooting
5. **Standards Compliance**: Follows CSV RFC standards for escaping and formatting
6. **International Support**: Proper handling of Unicode characters and various locales

## Backward Compatibility

The improved export format maintains compatibility with:
- Existing import functionality (with enhanced robustness)
- Standard CSV readers and editors
- Previous versions of exported files (import handles both old and new formats)

## Example Export

```csv
name,members,memberAvatars,categories
"Family Expenses",Alice|Bob|Charlie,Alice=👩|Bob=👨|Charlie=🧑,Food~🍔~restaurant^lunch|Transport~🚗~taxi^gas
amount,description,paidBy,splitBetween,category,date,isCategoryLocked
25.50,"Lunch at restaurant",Alice,Alice|Bob,Food,2024-01-15,true
15.75,"Coffee break",Bob,Alice|Bob|Charlie,Food,2024-01-16,false
30.00,"Gas for trip",Charlie,Alice|Bob|Charlie,Transport,2024-01-17,true
```

This format ensures reliable parsing, complete data preservation, and excellent compatibility with various CSV tools and the enhanced import functionality.
