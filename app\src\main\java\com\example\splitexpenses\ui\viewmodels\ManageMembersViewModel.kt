package com.example.splitexpenses.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.repositories.GroupRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI state for the manage members screen
 */
data class ManageMembersUiState(
    val currentGroup: GroupData? = null,
    val newMemberName: String = "",
    val showError: Boolean = false,
    val errorMessage: String = "",
    val isLoading: Boolean = false,
    val currentUser: String = "",
    val isCurrentUserGroupCreator: Boolean = false
)

/**
 * ViewModel for the manage members screen
 */
@HiltViewModel
class ManageMembersViewModel @Inject constructor(
    private val groupRepository: GroupRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ManageMembersUiState())
    val uiState: StateFlow<ManageMembersUiState> = _uiState.asStateFlow()

    init {
        // Load current group and user information
        viewModelScope.launch {
            groupRepository.currentGroup.collectLatest { group ->
                if (group != null) {
                    val currentUser = groupRepository.getSavedUserForGroup(group.id) ?: ""
                    val isCreator = isCurrentUserGroupCreator(group, currentUser)

                    _uiState.update {
                        it.copy(
                            currentGroup = group,
                            currentUser = currentUser,
                            isCurrentUserGroupCreator = isCreator
                        )
                    }
                }
            }
        }
    }

    /**
     * Update the new member name
     */
    fun updateNewMemberName(name: String) {
        _uiState.update {
            it.copy(
                newMemberName = name,
                showError = false,
                errorMessage = ""
            )
        }
    }

    /**
     * Add a new member to the group
     */
    fun addMember() {
        val currentState = _uiState.value
        val memberName = currentState.newMemberName.trim()

        if (memberName.isBlank()) {
            _uiState.update {
                it.copy(
                    showError = true,
                    errorMessage = "Member name cannot be empty"
                )
            }
            return
        }

        val group = currentState.currentGroup ?: return

        if (group.members.contains(memberName)) {
            _uiState.update {
                it.copy(
                    showError = true,
                    errorMessage = "Member already exists"
                )
            }
            return
        }

        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }

                val updatedMembers = group.members + memberName
                groupRepository.updateGroupMembers(group.id, updatedMembers)

                _uiState.update {
                    it.copy(
                        newMemberName = "",
                        isLoading = false,
                        showError = false,
                        errorMessage = ""
                    )
                }
            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        showError = true,
                        errorMessage = "Failed to add member: ${e.message}"
                    )
                }
            }
        }
    }

    /**
     * Remove a member from the group
     */
    fun removeMember(memberName: String) {
        val currentState = _uiState.value
        val group = currentState.currentGroup ?: return

        // Don't allow removing the last member
        if (group.members.size <= 1) {
            _uiState.update {
                it.copy(
                    showError = true,
                    errorMessage = "Cannot remove the last member"
                )
            }
            return
        }

        // Don't allow removing the current user
        if (memberName == currentState.currentUser) {
            _uiState.update {
                it.copy(
                    showError = true,
                    errorMessage = "Cannot remove yourself from the group"
                )
            }
            return
        }

        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }

                // Use removeMemberAndKick which handles both removing from members list and allowedUsers
                val success = groupRepository.removeMemberAndKick(group.id, memberName)

                if (success) {
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            showError = false,
                            errorMessage = ""
                        )
                    }
                } else {
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            showError = true,
                            errorMessage = "Failed to remove member. Only the group creator can remove members."
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        showError = true,
                        errorMessage = "Failed to remove member: ${e.message}"
                    )
                }
            }
        }
    }

    /**
     * Get the avatar for a member
     */
    fun getMemberAvatar(memberName: String): String? {
        return _uiState.value.currentGroup?.memberAvatars?.get(memberName)
    }

    /**
     * Check if a member is assigned to a UID
     */
    fun isMemberAssigned(memberName: String): Boolean {
        return _uiState.value.currentGroup?.memberUidMap?.containsKey(memberName) == true
    }

    /**
     * Check if the current user is the group creator
     */
    private fun isCurrentUserGroupCreator(group: GroupData, currentUser: String): Boolean {
        val memberUid = group.memberUidMap[currentUser]
        return memberUid != null && memberUid == group.creatorUid
    }

    /**
     * Get the UID for a member in the current group
     */
    fun getMemberUid(memberName: String): String? {
        return _uiState.value.currentGroup?.memberUidMap?.get(memberName)
    }

    /**
     * Check if a member is the group creator
     */
    fun isMemberGroupCreator(memberName: String): Boolean {
        val group = _uiState.value.currentGroup ?: return false
        val memberUid = getMemberUid(memberName)
        return memberUid != null && memberUid == group.creatorUid
    }

    /**
     * Clear any error state
     */
    fun clearError() {
        _uiState.update {
            it.copy(
                showError = false,
                errorMessage = ""
            )
        }
    }
}
