{"logs": [{"outputFile": "com.example.splitexpenses.app-mergeDebugResources-73:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b780b5538f0f4dabb582f550579cb67\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "287", "startColumns": "4", "startOffsets": "18574", "endColumns": "42", "endOffsets": "18612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ce1fb0cb77ab1a413a3074f5def1029a\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "261,2135,3119,3122", "startColumns": "4,4,4,4", "startOffsets": "17200,140858,174280,174395", "endLines": "261,2141,3121,3124", "endColumns": "52,24,24,24", "endOffsets": "17248,141157,174390,174505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aa911471e545f6e40186656fffc1c810\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "265,289", "startColumns": "4,4", "startOffsets": "17416,18660", "endColumns": "41,59", "endOffsets": "17453,18715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a570daead3d9a0e09cee3c22b866f0e\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "299,368,369,370,371,372,373,374,375,376,377,380,381,382,383,384,385,386,387,388,389,390,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,1536,1546", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19252,25367,25455,25541,25622,25706,25775,25840,25923,26029,26115,26235,26289,26358,26419,26488,26577,26672,26746,26843,26936,27034,27183,27274,27362,27458,27556,27620,27688,27775,27869,27936,28008,28080,28181,28290,28366,28435,28483,28549,28613,28687,28744,28801,28873,28923,28977,29048,29119,29189,29258,29316,29392,29463,29537,29623,29673,29743,100242,100957", "endLines": "299,368,369,370,371,372,373,374,375,376,379,380,381,382,383,384,385,386,387,388,389,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,1545,1548", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19320,25450,25536,25617,25701,25770,25835,25918,26024,26110,26230,26284,26353,26414,26483,26572,26667,26741,26838,26931,27029,27178,27269,27357,27453,27551,27615,27683,27770,27864,27931,28003,28075,28176,28285,28361,28430,28478,28544,28608,28682,28739,28796,28868,28918,28972,29043,29114,29184,29253,29311,29387,29458,29532,29618,29668,29738,29803,100952,101105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fbd72a3f323a8e4b4a1a522b1d003ecb\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,23,24,25,26,27,28,29,30,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,88,89,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,179,180,181,182,183,184,185,186,187,203,204,205,206,207,208,209,210,246,247,248,249,256,263,264,267,286,294,295,296,297,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,434,445,446,447,448,449,450,458,459,463,467,471,476,482,489,493,497,502,506,510,514,518,522,526,532,536,542,546,552,556,561,565,568,572,578,582,588,592,598,601,605,609,613,617,621,622,623,624,627,630,633,636,640,641,642,643,644,647,649,651,653,658,659,663,669,673,674,676,688,689,693,699,703,704,705,709,736,740,741,745,773,945,971,1142,1168,1199,1207,1213,1229,1251,1256,1261,1271,1280,1289,1293,1300,1319,1326,1327,1336,1339,1342,1346,1350,1354,1357,1358,1363,1368,1378,1383,1390,1396,1397,1400,1404,1409,1411,1413,1416,1419,1421,1425,1428,1435,1438,1441,1445,1447,1451,1453,1455,1457,1461,1469,1477,1489,1495,1504,1507,1518,1521,1522,1527,1528,1572,1641,1711,1712,1722,1731,1732,1734,1738,1741,1744,1747,1750,1753,1756,1759,1763,1766,1769,1772,1776,1779,1783,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1809,1811,1812,1813,1814,1815,1816,1817,1818,1820,1821,1823,1824,1826,1828,1829,1831,1832,1833,1834,1835,1836,1838,1839,1840,1841,1842,1854,1856,1858,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1874,1875,1876,1877,1878,1879,1880,1882,1886,1908,1909,1910,1911,1912,1913,1917,1918,1919,1934,1936,1938,1940,1942,1944,1945,1946,1947,1949,1951,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1967,1968,1969,1970,1972,1974,1975,1977,1978,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1997,1998,1999,2000,2002,2003,2004,2005,2006,2008,2010,2012,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2031,2106,2109,2112,2115,2129,2142,2184,2187,2216,2243,2252,2316,2679,2689,2727,2755,2877,2901,2907,2926,2947,3071,3130,3136,3140,3146,3200,3232,3298,3318,3373,3385,3411", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,359,400,455,517,581,651,712,787,863,940,1178,1263,1345,1421,1539,1616,1694,1800,1906,1985,2065,2122,2982,3056,3131,3196,3262,3322,3383,3455,3528,3595,3663,3722,3781,3840,3899,3958,4012,4066,4119,4173,4227,4281,4467,4541,4620,4693,4767,4838,4910,4982,5196,5253,5311,5384,5458,5532,5607,5679,5752,5822,5983,6043,6146,6215,6284,6354,6428,6504,6568,6645,6721,6798,6863,6932,7009,7084,7153,7221,7298,7364,7425,7522,7587,7656,7755,7826,7885,7943,8000,8059,8123,8194,8266,8338,8410,8482,8549,8617,8685,8744,8807,8871,8961,9052,9112,9178,9245,9311,9381,9445,9498,9565,9626,9693,9806,9864,9927,9992,10057,10132,10205,10277,10321,10368,10414,10463,10524,10585,10646,10708,10772,10836,10900,10965,11028,11088,11149,11215,11274,11334,11396,11467,11527,12083,12169,12256,12346,12433,12521,12603,12686,12776,13845,13897,13955,14000,14066,14130,14187,14244,16421,16478,16526,16575,16987,17320,17367,17523,18542,18941,19005,19067,19127,19395,19469,19539,19617,19671,19741,19826,19874,19920,19981,20044,20110,20174,20245,20308,20373,20437,20498,20559,20611,20684,20758,20827,20902,20976,21050,21191,30108,30690,30768,30858,30946,31042,31132,31714,31803,32050,32331,32583,32868,33261,33738,33960,34182,34458,34685,34915,35145,35375,35605,35832,36251,36477,36902,37132,37560,37779,38062,38270,38401,38628,39054,39279,39706,39927,40352,40472,40748,41049,41373,41664,41978,42115,42246,42351,42593,42760,42964,43172,43443,43555,43667,43772,43889,44103,44249,44389,44475,44823,44911,45157,45575,45824,45906,46004,46661,46761,47013,47437,47692,47786,47875,48112,50136,50378,50480,50733,52889,63570,65086,75781,77309,79066,79692,80112,81373,82638,82894,83130,83677,84171,84776,84974,85554,86922,87297,87415,87953,88110,88306,88579,88835,89005,89146,89210,89575,89942,90618,90882,91220,91573,91667,91853,92159,92421,92546,92673,92912,93123,93242,93435,93612,94067,94248,94370,94629,94742,94929,95031,95138,95267,95542,96050,96546,97423,97717,98287,98436,99168,99340,99424,99760,99852,102394,107625,112996,113058,113636,114220,114311,114424,114653,114813,114965,115136,115302,115471,115638,115801,116044,116214,116387,116558,116832,117031,117236,117566,117650,117746,117842,117940,118040,118142,118244,118346,118448,118550,118650,118746,118858,118987,119110,119241,119372,119470,119584,119678,119818,119952,120048,120160,120260,120376,120472,120584,120684,120824,120960,121124,121254,121412,121562,121703,121847,121982,122094,122244,122372,122500,122636,122768,122898,123028,123140,124038,124184,124328,124466,124532,124622,124698,124802,124892,124994,125102,125210,125310,125390,125482,125580,125690,125742,125820,125926,126018,126122,126232,126354,126517,127795,127875,127975,128065,128175,128265,128506,128600,128706,129540,129640,129752,129866,129982,130098,130192,130306,130418,130520,130640,130762,130844,130948,131068,131194,131292,131386,131474,131586,131702,131824,131936,132111,132227,132313,132405,132517,132641,132708,132834,132902,133030,133174,133302,133371,133466,133581,133694,133793,133902,134013,134124,134225,134330,134430,134560,134651,134774,134868,134980,135066,135170,135266,135354,135472,135576,135680,135806,135894,136002,136102,136192,136302,136386,136488,136572,136626,136690,136796,136882,136992,137076,137335,139951,140069,140184,140264,140625,141162,142566,142644,143988,145349,145737,148580,158633,158971,160642,161999,166226,166977,167239,167754,168133,172411,174692,174921,175072,175287,176787,177637,180663,181407,183538,183878,185189", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,23,24,25,26,27,28,29,30,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,88,89,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,179,180,181,182,183,184,185,186,187,203,204,205,206,207,208,209,210,246,247,248,249,256,263,264,267,286,294,295,296,297,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,434,445,446,447,448,449,457,458,462,466,470,475,481,488,492,496,501,505,509,513,517,521,525,531,535,541,545,551,555,560,564,567,571,577,581,587,591,597,600,604,608,612,616,620,621,622,623,626,629,632,635,639,640,641,642,643,646,648,650,652,657,658,662,668,672,673,675,687,688,692,698,702,703,704,708,735,739,740,744,772,944,970,1141,1167,1198,1206,1212,1228,1250,1255,1260,1270,1279,1288,1292,1299,1318,1325,1326,1335,1338,1341,1345,1349,1353,1356,1357,1362,1367,1377,1382,1389,1395,1396,1399,1403,1408,1410,1412,1415,1418,1420,1424,1427,1434,1437,1440,1444,1446,1450,1452,1454,1456,1460,1468,1476,1488,1494,1503,1506,1517,1520,1521,1526,1527,1532,1640,1710,1711,1721,1730,1731,1733,1737,1740,1743,1746,1749,1752,1755,1758,1762,1765,1768,1771,1775,1778,1782,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1808,1810,1811,1812,1813,1814,1815,1816,1817,1819,1820,1822,1823,1825,1827,1828,1830,1831,1832,1833,1834,1835,1837,1838,1839,1840,1841,1842,1855,1857,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1873,1874,1875,1876,1877,1878,1879,1881,1885,1889,1908,1909,1910,1911,1912,1916,1917,1918,1919,1935,1937,1939,1941,1943,1944,1945,1946,1948,1950,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1966,1967,1968,1969,1971,1973,1974,1976,1977,1979,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1996,1997,1998,1999,2001,2002,2003,2004,2005,2007,2009,2011,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2105,2108,2111,2114,2128,2134,2151,2186,2215,2242,2251,2315,2678,2682,2716,2754,2772,2900,2906,2912,2946,3070,3090,3135,3139,3145,3180,3211,3297,3317,3372,3384,3410,3417", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,395,450,512,576,646,707,782,858,935,1013,1258,1340,1416,1492,1611,1689,1795,1901,1980,2060,2117,2175,3051,3126,3191,3257,3317,3378,3450,3523,3590,3658,3717,3776,3835,3894,3953,4007,4061,4114,4168,4222,4276,4330,4536,4615,4688,4762,4833,4905,4977,5050,5248,5306,5379,5453,5527,5602,5674,5747,5817,5888,6038,6099,6210,6279,6349,6423,6499,6563,6640,6716,6793,6858,6927,7004,7079,7148,7216,7293,7359,7420,7517,7582,7651,7750,7821,7880,7938,7995,8054,8118,8189,8261,8333,8405,8477,8544,8612,8680,8739,8802,8866,8956,9047,9107,9173,9240,9306,9376,9440,9493,9560,9621,9688,9801,9859,9922,9987,10052,10127,10200,10272,10316,10363,10409,10458,10519,10580,10641,10703,10767,10831,10895,10960,11023,11083,11144,11210,11269,11329,11391,11462,11522,11590,12164,12251,12341,12428,12516,12598,12681,12771,12862,13892,13950,13995,14061,14125,14182,14239,14293,16473,16521,16570,16621,17016,17362,17411,17564,18569,19000,19062,19122,19179,19464,19534,19612,19666,19736,19821,19869,19915,19976,20039,20105,20169,20240,20303,20368,20432,20493,20554,20606,20679,20753,20822,20897,20971,21045,21186,21256,30156,30763,30853,30941,31037,31127,31709,31798,32045,32326,32578,32863,33256,33733,33955,34177,34453,34680,34910,35140,35370,35600,35827,36246,36472,36897,37127,37555,37774,38057,38265,38396,38623,39049,39274,39701,39922,40347,40467,40743,41044,41368,41659,41973,42110,42241,42346,42588,42755,42959,43167,43438,43550,43662,43767,43884,44098,44244,44384,44470,44818,44906,45152,45570,45819,45901,45999,46656,46756,47008,47432,47687,47781,47870,48107,50131,50373,50475,50728,52884,63565,65081,75776,77304,79061,79687,80107,81368,82633,82889,83125,83672,84166,84771,84969,85549,86917,87292,87410,87948,88105,88301,88574,88830,89000,89141,89205,89570,89937,90613,90877,91215,91568,91662,91848,92154,92416,92541,92668,92907,93118,93237,93430,93607,94062,94243,94365,94624,94737,94924,95026,95133,95262,95537,96045,96541,97418,97712,98282,98431,99163,99335,99419,99755,99847,100125,107620,112991,113053,113631,114215,114306,114419,114648,114808,114960,115131,115297,115466,115633,115796,116039,116209,116382,116553,116827,117026,117231,117561,117645,117741,117837,117935,118035,118137,118239,118341,118443,118545,118645,118741,118853,118982,119105,119236,119367,119465,119579,119673,119813,119947,120043,120155,120255,120371,120467,120579,120679,120819,120955,121119,121249,121407,121557,121698,121842,121977,122089,122239,122367,122495,122631,122763,122893,123023,123135,123275,124179,124323,124461,124527,124617,124693,124797,124887,124989,125097,125205,125305,125385,125477,125575,125685,125737,125815,125921,126013,126117,126227,126349,126512,126669,127870,127970,128060,128170,128260,128501,128595,128701,128793,129635,129747,129861,129977,130093,130187,130301,130413,130515,130635,130757,130839,130943,131063,131189,131287,131381,131469,131581,131697,131819,131931,132106,132222,132308,132400,132512,132636,132703,132829,132897,133025,133169,133297,133366,133461,133576,133689,133788,133897,134008,134119,134220,134325,134425,134555,134646,134769,134863,134975,135061,135165,135261,135349,135467,135571,135675,135801,135889,135997,136097,136187,136297,136381,136483,136567,136621,136685,136791,136877,136987,137071,137191,139946,140064,140179,140259,140620,140853,141674,142639,143983,145344,145732,148575,158628,158763,160336,161994,162566,166972,167234,167434,168128,172406,173012,174916,175067,175282,176365,177094,180658,181402,183533,183873,185184,185387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e41e00b20117d1aab37721f81a9cd4d9\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "291", "startColumns": "4", "startOffsets": "18774", "endColumns": "49", "endOffsets": "18819"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SplitExpenses\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "50", "endOffsets": "62"}, "to": {"startLines": "329", "startColumns": "4", "startOffsets": "21344", "endColumns": "50", "endOffsets": "21390"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SplitExpenses\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "23,3", "startColumns": "4,4", "startOffsets": "1304,110", "endLines": "38,20", "endColumns": "12,12", "endOffsets": "2277,1257"}, "to": {"startLines": "1556,1890", "startColumns": "4,4", "startOffsets": "101416,126674", "endLines": "1571,1907", "endColumns": "12,12", "endOffsets": "102389,127790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5657f6063dd1869549247e75dad7ed17\\transformed\\core-1.6.1\\res\\values\\values.xml", "from": {"startLines": "4,11", "startColumns": "0,0", "startOffsets": "142,510", "endLines": "10,17", "endColumns": "8,8", "endOffsets": "509,875"}, "to": {"startLines": "1920,1927", "startColumns": "4,4", "startOffsets": "128798,129170", "endLines": "1926,1933", "endColumns": "8,8", "endOffsets": "129165,129535"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SplitExpenses\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,201,283,387,496,616,727", "endColumns": "145,81,103,108,119,110,79", "endOffsets": "196,278,382,491,611,722,802"}, "to": {"startLines": "360,361,362,363,364,365,431", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "24583,24729,24811,24915,25024,25144,29924", "endColumns": "145,81,103,108,119,110,79", "endOffsets": "24724,24806,24910,25019,25139,25250,29999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\32ffe7f18302515d9d73a141b3dfe2c2\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "258,262", "startColumns": "4,4", "startOffsets": "17076,17253", "endColumns": "53,66", "endOffsets": "17125,17315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\faac399e6b2ef4972910475a8683353b\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "5,16,17,31,32,63,64,172,173,174,175,176,177,178,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,211,212,213,259,260,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,300,330,331,332,333,334,335,336,439,1843,1844,1848,1849,1853,2029,2030,2683,2717,2773,2808,2838,2871", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1018,1090,2180,2245,4335,4404,11595,11665,11733,11805,11875,11936,12010,12867,12928,12989,13051,13115,13177,13238,13306,13406,13466,13532,13605,13674,13731,13783,14298,14370,14446,17130,17165,17569,17624,17687,17742,17800,17856,17914,17975,18038,18095,18146,18204,18254,18315,18372,18438,18472,18507,19325,21395,21462,21534,21603,21672,21746,21818,30335,123280,123397,123598,123708,123909,137196,137268,158768,160341,162571,164377,165377,166059", "endLines": "5,16,17,31,32,63,64,172,173,174,175,176,177,178,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,211,212,213,259,260,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,300,330,331,332,333,334,335,336,439,1843,1847,1848,1852,1853,2029,2030,2688,2726,2807,2828,2870,2876", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1085,1173,2240,2306,4399,4462,11660,11728,11800,11870,11931,12005,12078,12923,12984,13046,13110,13172,13233,13301,13401,13461,13527,13600,13669,13726,13778,13840,14365,14441,14506,17160,17195,17619,17682,17737,17795,17851,17909,17970,18033,18090,18141,18199,18249,18310,18367,18433,18467,18502,18537,19390,21457,21529,21598,21667,21741,21813,21901,30401,123392,123593,123703,123904,124033,137263,137330,158966,160637,164372,165053,166054,166221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c08b202a85ee381a8046024641512a8e\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "288", "startColumns": "4", "startOffsets": "18617", "endColumns": "42", "endOffsets": "18655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b75d175235add0f8427f8a1abbb87845\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "328", "startColumns": "4", "startOffsets": "21261", "endColumns": "82", "endOffsets": "21339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da89fa1c76c2ef4b0cb85de664d475f6\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3091,3104,3110,3116,3125", "startColumns": "4,4,4,4,4", "startOffsets": "173017,173656,173900,174147,174510", "endLines": "3103,3109,3115,3118,3129", "endColumns": "24,24,24,24,24", "endOffsets": "173651,173895,174142,174275,174687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e2c2b067070e6ef49086890aefaa08b3\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "253,266,292,2829,2834", "startColumns": "4,4,4,4,4", "startOffsets": "16813,17458,18824,165058,165228", "endLines": "253,266,292,2833,2837", "endColumns": "56,64,63,24,24", "endOffsets": "16865,17518,18883,165223,165372"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SplitExpenses\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "22,73,74,75,86,87,90", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1497,5055,5102,5149,5893,5938,6104", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1534,5097,5144,5191,5933,5978,6141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3b83d3de83f199e14dbe26a60deaf5ce\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "254", "startColumns": "4", "startOffsets": "16870", "endColumns": "65", "endOffsets": "16931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c5c293b9bccb3452a24d1d22534197\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "290", "startColumns": "4", "startOffsets": "18720", "endColumns": "53", "endOffsets": "18769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e2673b804717d9b59abfbd1af2f09ef\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "33,34,35,36,37,38,39,40,339,340,341,342,343,344,345,346,348,349,350,351,352,353,354,355,356,2913,3181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2311,2401,2481,2571,2661,2741,2822,2902,22025,22130,22311,22436,22543,22723,22846,22962,23232,23420,23525,23706,23831,24006,24154,24217,24279,167439,176370", "endLines": "33,34,35,36,37,38,39,40,339,340,341,342,343,344,345,346,348,349,350,351,352,353,354,355,356,2925,3199", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2396,2476,2566,2656,2736,2817,2897,2977,22125,22306,22431,22538,22718,22841,22957,23060,23415,23520,23701,23826,24001,24149,24212,24274,24353,167749,176782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\af30d809214462b80fbfe822607332fe\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,250,251,252,255,257,293,337,338,357,358,359,366,367,429,430,432,433,435,436,437,438,440,441,442,1533,1549,1552", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14511,14570,14629,14689,14749,14809,14869,14929,14989,15049,15109,15169,15229,15288,15348,15408,15468,15528,15588,15648,15708,15768,15828,15888,15947,16007,16067,16126,16185,16244,16303,16362,16626,16700,16758,16936,17021,18888,21906,21971,24358,24424,24525,25255,25307,29808,29870,30004,30054,30161,30207,30253,30295,30406,30453,30489,100130,101110,101221", "endLines": "214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,250,251,252,255,257,293,337,338,357,358,359,366,367,429,430,432,433,435,436,437,438,440,441,442,1535,1551,1555", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "14565,14624,14684,14744,14804,14864,14924,14984,15044,15104,15164,15224,15283,15343,15403,15463,15523,15583,15643,15703,15763,15823,15883,15942,16002,16062,16121,16180,16239,16298,16357,16416,16695,16753,16808,16982,17071,18936,21966,22020,24419,24520,24578,25302,25362,29865,29919,30049,30103,30202,30248,30290,30330,30448,30484,30574,100237,101216,101411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\035a4e7aa700a3b95d1ef9282dca08ff\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "443,444", "startColumns": "4,4", "startOffsets": "30579,30635", "endColumns": "55,54", "endOffsets": "30630,30685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d43e0e4658725c97c9b3b7b9218894ad\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "298,347", "startColumns": "4,4", "startOffsets": "19184,23065", "endColumns": "67,166", "endOffsets": "19247,23227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7dc3a7185889b10bd023552a3b0513dc\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2152,2168,2174,3212,3228", "startColumns": "4,4,4,4,4", "startOffsets": "141679,142104,142282,177099,177510", "endLines": "2167,2173,2183,3227,3231", "endColumns": "24,24,24,24,24", "endOffsets": "142099,142277,142561,177505,177632"}}]}]}