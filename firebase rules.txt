{
  "rules": {
    "groups": {
      // Allow reading all groups - we'll filter client-side
      // This is necessary for the initial loading of available groups
      ".read": true,

      "$groupId": {
        // Allow writing to a group if:
        // 1. The group is being created (data doesn't exist)
        // 2. The allowedUsers list is empty (backward compatibility)
        // 3. We're not using Firebase Auth in this app, so we can't check auth.uid
        ".write": true,

        // Basic validation for group structure
        ".validate": "
          // Ensure required fields exist
          newData.hasChildren(['id', 'name']) &&

          // Validate id field
          newData.child('id').isString() &&

          // Validate name field
          newData.child('name').isString()"
      }
    },

    // Default rule - deny access to other paths
    ".read": false,
    ".write": false
  }
}
