package com.example.splitexpenses.ui.viewmodels;

/**
 * Data class representing the current filter state for expenses
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b!\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0087\b\u0018\u00002\u00020\u0001B}\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b\u0012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u0012\b\b\u0002\u0010\u000e\u001a\u00020\f\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\u0002\u0010\u0012J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010&\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017J\t\u0010\'\u001a\u00020\u0005H\u00c6\u0003J\t\u0010(\u001a\u00020\u0005H\u00c6\u0003J\u0010\u0010)\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0014J\u0010\u0010*\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0014J\u000f\u0010+\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u00c6\u0003J\u000f\u0010,\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u00c6\u0003J\t\u0010-\u001a\u00020\fH\u00c6\u0003J\u0010\u0010.\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017J\u0086\u0001\u0010/\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\b\b\u0002\u0010\u000e\u001a\u00020\f2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00102\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0010H\u00c6\u0001\u00a2\u0006\u0002\u00100J\u0013\u00101\u001a\u0002022\b\u00103\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u00104\u001a\u00020\u0005J\u0006\u00105\u001a\u000202J\t\u00106\u001a\u00020\u0005H\u00d6\u0001J\t\u00107\u001a\u00020\fH\u00d6\u0001R\u0015\u0010\t\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u0013\u0010\u0014R\u0015\u0010\u0011\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0016\u0010\u0017R\u0015\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0019\u0010\u0017R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u000e\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\"R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b$\u0010\u0014\u00a8\u00068"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;", "", "periodType", "Lcom/example/splitexpenses/ui/viewmodels/PeriodType;", "selectedMonth", "", "selectedYear", "startDate", "", "endDate", "selectedCategories", "", "", "selectedMembers", "searchText", "minAmount", "", "maxAmount", "(Lcom/example/splitexpenses/ui/viewmodels/PeriodType;IILjava/lang/Long;Ljava/lang/Long;Ljava/util/Set;Ljava/util/Set;Ljava/lang/String;Ljava/lang/Float;Ljava/lang/Float;)V", "getEndDate", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getMaxAmount", "()Ljava/lang/Float;", "Ljava/lang/Float;", "getMinAmount", "getPeriodType", "()Lcom/example/splitexpenses/ui/viewmodels/PeriodType;", "getSearchText", "()Ljava/lang/String;", "getSelectedCategories", "()Ljava/util/Set;", "getSelectedMembers", "getSelectedMonth", "()I", "getSelectedYear", "getStartDate", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Lcom/example/splitexpenses/ui/viewmodels/PeriodType;IILjava/lang/Long;Ljava/lang/Long;Ljava/util/Set;Ljava/util/Set;Ljava/lang/String;Ljava/lang/Float;Ljava/lang/Float;)Lcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;", "equals", "", "other", "getActiveFilterCount", "hasActiveFilters", "hashCode", "toString", "app_debug"})
public final class ExpenseFilterState {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.ui.viewmodels.PeriodType periodType = null;
    private final int selectedMonth = 0;
    private final int selectedYear = 0;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long startDate = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long endDate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> selectedCategories = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> selectedMembers = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String searchText = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Float minAmount = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Float maxAmount = null;
    
    public ExpenseFilterState(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.PeriodType periodType, int selectedMonth, int selectedYear, @org.jetbrains.annotations.Nullable()
    java.lang.Long startDate, @org.jetbrains.annotations.Nullable()
    java.lang.Long endDate, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedCategories, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedMembers, @org.jetbrains.annotations.NotNull()
    java.lang.String searchText, @org.jetbrains.annotations.Nullable()
    java.lang.Float minAmount, @org.jetbrains.annotations.Nullable()
    java.lang.Float maxAmount) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.ui.viewmodels.PeriodType getPeriodType() {
        return null;
    }
    
    public final int getSelectedMonth() {
        return 0;
    }
    
    public final int getSelectedYear() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getStartDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getEndDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> getSelectedCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> getSelectedMembers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSearchText() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float getMinAmount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float getMaxAmount() {
        return null;
    }
    
    /**
     * Check if any filters are active
     */
    public final boolean hasActiveFilters() {
        return false;
    }
    
    /**
     * Get the number of active filters
     */
    public final int getActiveFilterCount() {
        return 0;
    }
    
    public ExpenseFilterState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.ui.viewmodels.PeriodType component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float component10() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.ui.viewmodels.ExpenseFilterState copy(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.PeriodType periodType, int selectedMonth, int selectedYear, @org.jetbrains.annotations.Nullable()
    java.lang.Long startDate, @org.jetbrains.annotations.Nullable()
    java.lang.Long endDate, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedCategories, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedMembers, @org.jetbrains.annotations.NotNull()
    java.lang.String searchText, @org.jetbrains.annotations.Nullable()
    java.lang.Float minAmount, @org.jetbrains.annotations.Nullable()
    java.lang.Float maxAmount) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}