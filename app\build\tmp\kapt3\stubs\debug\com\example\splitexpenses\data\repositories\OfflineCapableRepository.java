package com.example.splitexpenses.data.repositories;

/**
 * Repository that handles both online and offline operations
 * Automatically switches between Firebase and local cache based on connectivity
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\"\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\r\b\u0007\u0018\u00002\u00020\u0001B+\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u001e\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J\u001e\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\f2\u0006\u0010\u0016\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u0017J$\u0010\u0018\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\f2\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\f0\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0016\u0010\u001c\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u000e\u0010\u001e\u001a\u00020\u001fH\u0086@\u00a2\u0006\u0002\u0010 J\u0014\u0010!\u001a\b\u0012\u0004\u0012\u00020#0\"H\u0086@\u00a2\u0006\u0002\u0010 J\u0018\u0010$\u001a\u0004\u0018\u00010#2\u0006\u0010\u0011\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\'0&J\u0012\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\"0&J\u0016\u0010)\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010#0&2\u0006\u0010\u0011\u001a\u00020\fJ\u000e\u0010*\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010 J\u0016\u0010+\u001a\u00020\u00102\u0006\u0010,\u001a\u00020#H\u0086@\u00a2\u0006\u0002\u0010-J\u000e\u0010.\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010 J\u001e\u0010/\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J&\u00100\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\f2\u0006\u00101\u001a\u00020\f2\u0006\u00102\u001a\u00020\u0001H\u0086@\u00a2\u0006\u0002\u00103R\u000e\u0010\u000b\u001a\u00020\fX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00064"}, d2 = {"Lcom/example/splitexpenses/data/repositories/OfflineCapableRepository;", "", "remoteDataSource", "Lcom/example/splitexpenses/data/source/DataSource;", "offlineDataSource", "Lcom/example/splitexpenses/data/source/OfflineDataSource;", "networkConnectivityManager", "Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;", "syncQueueManager", "Lcom/example/splitexpenses/data/sync/SyncQueueManager;", "(Lcom/example/splitexpenses/data/source/DataSource;Lcom/example/splitexpenses/data/source/OfflineDataSource;Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;Lcom/example/splitexpenses/data/sync/SyncQueueManager;)V", "TAG", "", "coroutineScope", "Lkotlinx/coroutines/CoroutineScope;", "addExpense", "", "groupId", "expense", "Lcom/example/splitexpenses/data/Expense;", "(Ljava/lang/String;Lcom/example/splitexpenses/data/Expense;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpense", "expenseId", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenses", "expenseIds", "", "(Ljava/lang/String;Ljava/util/Set;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGroup", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "forceSyncOfflineChanges", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAvailableGroups", "", "Lcom/example/splitexpenses/data/GroupData;", "getGroup", "getPendingSyncCountFlow", "Lkotlinx/coroutines/flow/Flow;", "", "observeAvailableGroups", "observeGroup", "refreshDataFromRemote", "saveGroup", "group", "(Lcom/example/splitexpenses/data/GroupData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "syncOfflineChanges", "updateExpense", "updateGroupField", "field", "value", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class OfflineCapableRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.source.DataSource remoteDataSource = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.source.OfflineDataSource offlineDataSource = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.connectivity.NetworkConnectivityManager networkConnectivityManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.sync.SyncQueueManager syncQueueManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "OfflineCapableRepository";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope coroutineScope = null;
    
    @javax.inject.Inject()
    public OfflineCapableRepository(@javax.inject.Named(value = "firebase")
    @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.source.DataSource remoteDataSource, @javax.inject.Named(value = "offline")
    @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.source.OfflineDataSource offlineDataSource, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.connectivity.NetworkConnectivityManager networkConnectivityManager, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.sync.SyncQueueManager syncQueueManager) {
        super();
    }
    
    /**
     * Get a group - tries remote first if online, falls back to cache
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.data.GroupData> $completion) {
        return null;
    }
    
    /**
     * Get available groups - tries remote first if online, falls back to cache
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAvailableGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.GroupData>> $completion) {
        return null;
    }
    
    /**
     * Observe a group - combines remote and local data
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.example.splitexpenses.data.GroupData> observeGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return null;
    }
    
    /**
     * Observe available groups - combines remote and local data
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.splitexpenses.data.GroupData>> observeAvailableGroups() {
        return null;
    }
    
    /**
     * Save a group - saves to remote if online, queues for sync if offline
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveGroup(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Update a group field - updates remote if online, queues for sync if offline
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupField(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String field, @org.jetbrains.annotations.NotNull()
    java.lang.Object value, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Delete a group - deletes from remote if online, queues for sync if offline
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Add an expense - adds to remote if online, queues for sync if offline
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Update an expense - updates remote if online, queues for sync if offline
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Expense expense, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Delete an expense - deletes from remote if online, queues for sync if offline
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Delete multiple expenses - deletes from remote if online, queues for sync if offline
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpenses(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> expenseIds, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Sync offline changes to remote
     */
    private final java.lang.Object syncOfflineChanges(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Force refresh data from remote when coming back online
     */
    private final java.lang.Object refreshDataFromRemote(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Get pending sync count
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Integer> getPendingSyncCountFlow() {
        return null;
    }
    
    /**
     * Force sync offline changes (for manual sync)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object forceSyncOfflineChanges(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}