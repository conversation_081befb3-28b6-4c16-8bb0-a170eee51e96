package com.example.splitexpenses.ui;

import com.example.splitexpenses.data.repositories.GroupRepository;
import com.example.splitexpenses.data.source.LocalDataSource;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<GroupRepository> groupRepositoryProvider;

  private final Provider<LocalDataSource> localDataSourceProvider;

  public MainActivity_MembersInjector(Provider<GroupRepository> groupRepositoryProvider,
      Provider<LocalDataSource> localDataSourceProvider) {
    this.groupRepositoryProvider = groupRepositoryProvider;
    this.localDataSourceProvider = localDataSourceProvider;
  }

  public static MembersInjector<MainActivity> create(
      Provider<GroupRepository> groupRepositoryProvider,
      Provider<LocalDataSource> localDataSourceProvider) {
    return new MainActivity_MembersInjector(groupRepositoryProvider, localDataSourceProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectGroupRepository(instance, groupRepositoryProvider.get());
    injectLocalDataSource(instance, localDataSourceProvider.get());
  }

  @InjectedFieldSignature("com.example.splitexpenses.ui.MainActivity.groupRepository")
  public static void injectGroupRepository(MainActivity instance, GroupRepository groupRepository) {
    instance.groupRepository = groupRepository;
  }

  @InjectedFieldSignature("com.example.splitexpenses.ui.MainActivity.localDataSource")
  public static void injectLocalDataSource(MainActivity instance, LocalDataSource localDataSource) {
    instance.localDataSource = localDataSource;
  }
}
