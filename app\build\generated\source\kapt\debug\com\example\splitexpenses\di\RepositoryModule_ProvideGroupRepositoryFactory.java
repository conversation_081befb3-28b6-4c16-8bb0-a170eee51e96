package com.example.splitexpenses.di;

import com.example.splitexpenses.data.repositories.GroupRepository;
import com.example.splitexpenses.data.repositories.OfflineCapableRepository;
import com.example.splitexpenses.data.source.LocalDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideGroupRepositoryFactory implements Factory<GroupRepository> {
  private final Provider<OfflineCapableRepository> offlineCapableRepositoryProvider;

  private final Provider<LocalDataSource> localDataSourceProvider;

  public RepositoryModule_ProvideGroupRepositoryFactory(
      Provider<OfflineCapableRepository> offlineCapableRepositoryProvider,
      Provider<LocalDataSource> localDataSourceProvider) {
    this.offlineCapableRepositoryProvider = offlineCapableRepositoryProvider;
    this.localDataSourceProvider = localDataSourceProvider;
  }

  @Override
  public GroupRepository get() {
    return provideGroupRepository(offlineCapableRepositoryProvider.get(), localDataSourceProvider.get());
  }

  public static RepositoryModule_ProvideGroupRepositoryFactory create(
      Provider<OfflineCapableRepository> offlineCapableRepositoryProvider,
      Provider<LocalDataSource> localDataSourceProvider) {
    return new RepositoryModule_ProvideGroupRepositoryFactory(offlineCapableRepositoryProvider, localDataSourceProvider);
  }

  public static GroupRepository provideGroupRepository(
      OfflineCapableRepository offlineCapableRepository, LocalDataSource localDataSource) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideGroupRepository(offlineCapableRepository, localDataSource));
  }
}
