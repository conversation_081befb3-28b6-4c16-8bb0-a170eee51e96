package com.example.splitexpenses.data

import java.util.UUID

import android.content.Context
import android.content.SharedPreferences

class UserPreferences(context: Context) {
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    /**
     * Gets the device's unique identifier.
     * If no UID exists yet, generates a new one and stores it.
     */
    fun getDeviceUid(): String {
        var uid = sharedPreferences.getString(KEY_DEVICE_UID, null)

        if (uid == null) {
            uid = UUID.randomUUID().toString()
            sharedPreferences.edit().putString(KEY_DEVICE_UID, uid).apply()
        }

        return uid
    }

    /**
     * Saves the user name for a specific group
     */
    fun saveUserForGroup(groupId: String, userName: String) {
        val uid = getDeviceUid()

        // Combine all edits into a single transaction for better performance
        sharedPreferences.edit()
            .putString("user_$groupId", userName)
            .putString("name_to_uid_${groupId}_$userName", uid)
            .putString("uid_to_name_${groupId}_$uid", userName)
            .apply()
    }

    /**
     * Gets the user name for a specific group
     */
    fun getSavedUserForGroup(groupId: String): String? {
        return sharedPreferences.getString("user_$groupId", null)
    }

    /**
     * Gets the UID associated with a user name in a specific group
     */
    fun getUidForUserInGroup(groupId: String, userName: String): String? {
        return sharedPreferences.getString("name_to_uid_${groupId}_$userName", null)
    }

    /**
     * Saves the UID for a user in a specific group
     */
    fun saveUidForUserInGroup(groupId: String, userName: String, uid: String) {
        sharedPreferences.edit()
            .putString("name_to_uid_${groupId}_$userName", uid)
            .putString("uid_to_name_${groupId}_$uid", userName)
            .apply()
    }

    /**
     * Gets the user name associated with a UID in a specific group
     */
    fun getUserNameForUidInGroup(groupId: String, uid: String): String? {
        return sharedPreferences.getString("uid_to_name_${groupId}_$uid", null)
    }

    companion object {
        private const val PREFS_NAME = "SplitExpensesPrefs"
        private const val KEY_DEVICE_UID = "device_uid"
    }
}