package com.example.splitexpenses.data.source

import android.util.Log
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.cache.SplitExpensesDatabase
import com.example.splitexpenses.data.cache.entities.CategoryEntity
import com.example.splitexpenses.data.cache.entities.ExpenseEntity
import com.example.splitexpenses.data.cache.entities.GroupEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Data source for offline operations using Room database
 */
@Singleton
class OfflineDataSource @Inject constructor(
    private val database: SplitExpensesDatabase
) : DataSource {

    private val TAG = "OfflineDataSource"

    override suspend fun getGroup(groupId: String): GroupData? {
        return try {
            val groupEntity = database.groupDao().getGroupById(groupId)
            val expenses = database.expenseDao().getExpensesForGroup(groupId)

            groupEntity?.toGroupData(expenses.map { it.toExpense() })
        } catch (e: Exception) {
            Log.e(TAG, "Error getting group $groupId from cache", e)
            null
        }
    }

    override suspend fun getAvailableGroups(): List<GroupData> {
        return try {
            val groups = database.groupDao().getAllGroups()
            groups.map { groupEntity ->
                val expenses = database.expenseDao().getExpensesForGroup(groupEntity.id)
                groupEntity.toGroupData(expenses.map { it.toExpense() })
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting available groups from cache", e)
            emptyList()
        }
    }

    override fun observeGroup(groupId: String): Flow<GroupData?> {
        return combine(
            database.groupDao().getGroupByIdFlow(groupId),
            database.expenseDao().getExpensesForGroupFlow(groupId)
        ) { groupEntity, expenseEntities ->
            groupEntity?.toGroupData(expenseEntities.map { it.toExpense() })
        }
    }

    override fun observeAvailableGroups(): Flow<List<GroupData>> {
        return database.groupDao().getAllGroupsFlow().map { groupEntities ->
            groupEntities.map { groupEntity ->
                val expenses = database.expenseDao().getExpensesForGroup(groupEntity.id)
                groupEntity.toGroupData(expenses.map { it.toExpense() })
            }
        }
    }

    override suspend fun saveGroup(group: GroupData) {
        try {
            // Save group entity
            val groupEntity = GroupEntity.fromGroupData(group, isSynced = false)
            database.groupDao().insertGroup(groupEntity)

            // Save expenses
            val expenseEntities = group.expenses.map { expense ->
                ExpenseEntity.fromExpense(expense, group.id, isSynced = false)
            }
            database.expenseDao().insertExpenses(expenseEntities)

            // Save categories
            val categoryEntities = group.categories.map { category ->
                CategoryEntity.fromCategory(category, group.id, isSynced = false)
            }
            database.categoryDao().insertCategories(categoryEntities)

            Log.d(TAG, "Saved group ${group.id} to cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving group ${group.id} to cache", e)
            throw e
        }
    }

    override suspend fun updateGroupField(groupId: String, field: String, value: Any) {
        try {
            val groupEntity = database.groupDao().getGroupById(groupId)
            if (groupEntity != null) {
                when (field) {
                    "expenses" -> {
                        @Suppress("UNCHECKED_CAST")
                        val expenses = value as List<Expense>

                        // Clear existing expenses for this group
                        database.expenseDao().deleteExpensesForGroup(groupId)

                        // Insert new expenses
                        val expenseEntities = expenses.map { expense ->
                            ExpenseEntity.fromExpense(expense, groupId, isSynced = false)
                        }
                        database.expenseDao().insertExpenses(expenseEntities)
                    }
                    "categories" -> {
                        @Suppress("UNCHECKED_CAST")
                        val categories = value as List<Category>

                        // Clear existing categories for this group
                        database.categoryDao().deleteCategoriesForGroup(groupId)

                        // Insert new categories
                        val categoryEntities = categories.map { category ->
                            CategoryEntity.fromCategory(category, groupId, isSynced = false)
                        }
                        database.categoryDao().insertCategories(categoryEntities)
                    }
                    else -> {
                        // For other fields, update the group entity
                        val updatedGroup = when (field) {
                            "name" -> groupEntity.copy(name = value as String, isSynced = false)
                            "members" -> {
                                @Suppress("UNCHECKED_CAST")
                                groupEntity.copy(members = value as List<String>, isSynced = false)
                            }
                            "memberAvatars" -> {
                                @Suppress("UNCHECKED_CAST")
                                groupEntity.copy(memberAvatars = value as Map<String, String>, isSynced = false)
                            }
                            else -> groupEntity.copy(isSynced = false)
                        }
                        database.groupDao().updateGroup(updatedGroup)
                    }
                }

                Log.d(TAG, "Updated group $groupId field $field in cache")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating group $groupId field $field in cache", e)
            throw e
        }
    }

    override suspend fun deleteGroup(groupId: String) {
        try {
            database.groupDao().deleteGroupById(groupId)
            database.expenseDao().deleteExpensesForGroup(groupId)
            database.categoryDao().deleteCategoriesForGroup(groupId)

            Log.d(TAG, "Deleted group $groupId from cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting group $groupId from cache", e)
            throw e
        }
    }

    override suspend fun addExpense(groupId: String, expense: Expense) {
        try {
            val expenseEntity = ExpenseEntity.fromExpense(expense, groupId, isSynced = false)
            database.expenseDao().insertExpense(expenseEntity)

            Log.d(TAG, "Added expense ${expense.id} to cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error adding expense ${expense.id} to cache", e)
            throw e
        }
    }

    override suspend fun updateExpense(groupId: String, expense: Expense) {
        try {
            val expenseEntity = ExpenseEntity.fromExpense(expense, groupId, isSynced = false)
            database.expenseDao().updateExpense(expenseEntity)

            Log.d(TAG, "Updated expense ${expense.id} in cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating expense ${expense.id} in cache", e)
            throw e
        }
    }

    override suspend fun deleteExpense(groupId: String, expenseId: String) {
        try {
            database.expenseDao().deleteExpenseById(expenseId)

            Log.d(TAG, "Deleted expense $expenseId from cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting expense $expenseId from cache", e)
            throw e
        }
    }

    override suspend fun deleteExpenses(groupId: String, expenseIds: Set<String>) {
        try {
            expenseIds.forEach { expenseId ->
                database.expenseDao().deleteExpenseById(expenseId)
            }

            Log.d(TAG, "Deleted ${expenseIds.size} expenses from cache")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting expenses from cache", e)
            throw e
        }
    }

    override fun cleanup() {
        // Room database cleanup is handled automatically
        Log.d(TAG, "OfflineDataSource cleanup completed")
    }

    /**
     * Cache data from remote source
     */
    suspend fun cacheGroup(group: GroupData) {
        try {
            val groupEntity = GroupEntity.fromGroupData(group, isSynced = true)
            database.groupDao().insertGroup(groupEntity)

            // Cache expenses
            val expenseEntities = group.expenses.map { expense ->
                ExpenseEntity.fromExpense(expense, group.id, isSynced = true)
            }
            // Clear existing expenses first
            database.expenseDao().deleteExpensesForGroup(group.id)
            database.expenseDao().insertExpenses(expenseEntities)

            // Cache categories
            val categoryEntities = group.categories.map { category ->
                CategoryEntity.fromCategory(category, group.id, isSynced = true)
            }
            // Clear existing categories first
            database.categoryDao().deleteCategoriesForGroup(group.id)
            database.categoryDao().insertCategories(categoryEntities)

            Log.d(TAG, "Cached group ${group.id} from remote")
        } catch (e: Exception) {
            Log.e(TAG, "Error caching group ${group.id}", e)
            // Don't rethrow the exception to prevent crashes
            // The app can continue to work without caching
        }
    }

    /**
     * Cache multiple groups from remote source
     */
    suspend fun cacheGroups(groups: List<GroupData>) {
        try {
            groups.forEach { group ->
                cacheGroup(group)
            }
            Log.d(TAG, "Cached ${groups.size} groups from remote")
        } catch (e: Exception) {
            Log.e(TAG, "Error caching groups", e)
            // Don't rethrow the exception to prevent crashes
            // The app can continue to work without caching
        }
    }

    /**
     * Get unsynced groups
     */
    suspend fun getUnsyncedGroups(): List<GroupEntity> {
        return database.groupDao().getUnsyncedGroups()
    }

    /**
     * Get unsynced expenses
     */
    suspend fun getUnsyncedExpenses(): List<ExpenseEntity> {
        return database.expenseDao().getUnsyncedExpenses()
    }

    /**
     * Mark group as synced
     */
    suspend fun markGroupAsSynced(groupId: String) {
        database.groupDao().markGroupAsSynced(groupId)
    }

    /**
     * Mark expense as synced
     */
    suspend fun markExpenseAsSynced(expenseId: String) {
        database.expenseDao().markExpenseAsSynced(expenseId)
    }
}
