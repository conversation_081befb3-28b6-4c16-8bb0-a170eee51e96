#androidx.activity.ComponentActivityandroid.app.Applicationandroidx.room.RoomDatabasekotlin.Enum0com.example.splitexpenses.data.source.DataSource/com.example.splitexpenses.ui.viewmodels.UiState5com.example.splitexpenses.ui.viewmodels.BaseViewModelandroidx.lifecycle.ViewModel=com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           