package com.example.splitexpenses.di;

import android.content.Context;
import com.example.splitexpenses.data.cache.SplitExpensesDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideSplitExpensesDatabaseFactory implements Factory<SplitExpensesDatabase> {
  private final Provider<Context> contextProvider;

  public DataModule_ProvideSplitExpensesDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SplitExpensesDatabase get() {
    return provideSplitExpensesDatabase(contextProvider.get());
  }

  public static DataModule_ProvideSplitExpensesDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DataModule_ProvideSplitExpensesDatabaseFactory(contextProvider);
  }

  public static SplitExpensesDatabase provideSplitExpensesDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideSplitExpensesDatabase(context));
  }
}
