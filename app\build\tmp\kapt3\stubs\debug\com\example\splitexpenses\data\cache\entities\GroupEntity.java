package com.example.splitexpenses.data.cache.entities;

/**
 * Room entity for storing group data locally
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u001d\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0087\b\u0018\u0000 52\u00020\u0001:\u000256B\u0083\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\b\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006\u0012\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\b\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\u0002\u0010\u0012J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0011H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006H\u00c6\u0003J\u0015\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\bH\u00c6\u0003J\u000f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010(\u001a\b\u0012\u0004\u0012\u00020\f0\u0006H\u00c6\u0003J\u0015\u0010)\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\bH\u00c6\u0003J\t\u0010*\u001a\u00020\u000fH\u00c6\u0003J\u0097\u0001\u0010+\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u0014\b\u0002\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\b\b\u0002\u0010\n\u001a\u00020\u00032\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00062\u0014\b\u0002\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\b2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u00c6\u0001J\u0013\u0010,\u001a\u00020\u00112\b\u0010-\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010.\u001a\u00020/H\u00d6\u0001J\u0016\u00100\u001a\u0002012\u000e\b\u0002\u00102\u001a\b\u0012\u0004\u0012\u0002030\u0006J\t\u00104\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0017R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0019R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u001d\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u001d\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001dR\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0017\u00a8\u00067"}, d2 = {"Lcom/example/splitexpenses/data/cache/entities/GroupEntity;", "", "id", "", "name", "members", "", "memberUidMap", "", "allowedUsers", "creatorUid", "categories", "Lcom/example/splitexpenses/data/Category;", "memberAvatars", "lastModified", "", "isSynced", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/Map;Ljava/util/List;Ljava/lang/String;Ljava/util/List;Ljava/util/Map;JZ)V", "getAllowedUsers", "()Ljava/util/List;", "getCategories", "getCreatorUid", "()Ljava/lang/String;", "getId", "()Z", "getLastModified", "()J", "getMemberAvatars", "()Ljava/util/Map;", "getMemberUidMap", "getMembers", "getName", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toGroupData", "Lcom/example/splitexpenses/data/GroupData;", "expenses", "Lcom/example/splitexpenses/data/Expense;", "toString", "Companion", "Converters", "app_debug"})
@androidx.room.Entity(tableName = "groups")
@androidx.room.TypeConverters(value = {com.example.splitexpenses.data.cache.entities.GroupEntity.Converters.class})
public final class GroupEntity {
    @androidx.room.PrimaryKey()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String name = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> members = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.String> memberUidMap = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> allowedUsers = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String creatorUid = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.splitexpenses.data.Category> categories = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.String> memberAvatars = null;
    private final long lastModified = 0L;
    private final boolean isSynced = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.splitexpenses.data.cache.entities.GroupEntity.Companion Companion = null;
    
    public GroupEntity(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> members, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> memberUidMap, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> allowedUsers, @org.jetbrains.annotations.NotNull()
    java.lang.String creatorUid, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Category> categories, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> memberAvatars, long lastModified, boolean isSynced) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getMembers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> getMemberUidMap() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAllowedUsers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCreatorUid() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.data.Category> getCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> getMemberAvatars() {
        return null;
    }
    
    public final long getLastModified() {
        return 0L;
    }
    
    public final boolean isSynced() {
        return false;
    }
    
    /**
     * Convert GroupEntity to GroupData
     * Note: Expenses are stored separately and need to be fetched from ExpenseEntity
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.GroupData toGroupData(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Expense> expenses) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.data.Category> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> component8() {
        return null;
    }
    
    public final long component9() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.cache.entities.GroupEntity copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> members, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> memberUidMap, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> allowedUsers, @org.jetbrains.annotations.NotNull()
    java.lang.String creatorUid, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Category> categories, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> memberAvatars, long lastModified, boolean isSynced) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b\u00a8\u0006\t"}, d2 = {"Lcom/example/splitexpenses/data/cache/entities/GroupEntity$Companion;", "", "()V", "fromGroupData", "Lcom/example/splitexpenses/data/cache/entities/GroupEntity;", "groupData", "Lcom/example/splitexpenses/data/GroupData;", "isSynced", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Convert GroupData to GroupEntity
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.splitexpenses.data.cache.entities.GroupEntity fromGroupData(@org.jetbrains.annotations.NotNull()
        com.example.splitexpenses.data.GroupData groupData, boolean isSynced) {
            return null;
        }
    }
    
    /**
     * Type converters for Room database
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0007J\u0016\u0010\n\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\bH\u0007J\u001c\u0010\u000b\u001a\u00020\u00062\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\fH\u0007J\u0016\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\u0007\u001a\u00020\u0006H\u0007J\u0016\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00060\b2\u0006\u0010\u0007\u001a\u00020\u0006H\u0007J\u001c\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\f2\u0006\u0010\u0007\u001a\u00020\u0006H\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/example/splitexpenses/data/cache/entities/GroupEntity$Converters;", "", "()V", "gson", "Lcom/google/gson/Gson;", "fromCategoryList", "", "value", "", "Lcom/example/splitexpenses/data/Category;", "fromStringList", "fromStringMap", "", "toCategoryList", "toStringList", "toStringMap", "app_debug"})
    public static final class Converters {
        @org.jetbrains.annotations.NotNull()
        private final com.google.gson.Gson gson = null;
        
        public Converters() {
            super();
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String fromStringList(@org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> value) {
            return null;
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> toStringList(@org.jetbrains.annotations.NotNull()
        java.lang.String value) {
            return null;
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String fromStringMap(@org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, java.lang.String> value) {
            return null;
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, java.lang.String> toStringMap(@org.jetbrains.annotations.NotNull()
        java.lang.String value) {
            return null;
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String fromCategoryList(@org.jetbrains.annotations.NotNull()
        java.util.List<com.example.splitexpenses.data.Category> value) {
            return null;
        }
        
        @androidx.room.TypeConverter()
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.splitexpenses.data.Category> toCategoryList(@org.jetbrains.annotations.NotNull()
        java.lang.String value) {
            return null;
        }
    }
}