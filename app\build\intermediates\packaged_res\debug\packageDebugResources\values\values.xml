<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">SplitExpenses</string>
    <string name="firebase_database_url" translatable="false">https://splitexpenses-2d71b-default-rtdb.europe-west1.firebasedatabase.app</string>
    <string name="gcm_defaultSenderId" translatable="false">175517668233</string>
    <string name="google_api_key" translatable="false">AIzaSyBk38_5UM5yOgBHGm5d8XkdCPAkhg8vzUU</string>
    <string name="google_app_id" translatable="false">1:175517668233:android:cc60d95b13df51c728c758</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBk38_5UM5yOgBHGm5d8XkdCPAkhg8vzUU</string>
    <string name="google_storage_bucket" translatable="false">splitexpenses-2d71b.firebasestorage.app</string>
    <string name="project_id" translatable="false">splitexpenses-2d71b</string>
    <style name="NoAnimationStyle">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>
    <style name="Theme.SplitExpenses" parent="android:Theme.Material.Light.NoActionBar">
        
        <item name="android:windowAnimationStyle">@style/NoAnimationStyle</item>
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>
</resources>