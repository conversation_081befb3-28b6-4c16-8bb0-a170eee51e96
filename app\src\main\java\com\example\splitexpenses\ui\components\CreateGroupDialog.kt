package com.example.splitexpenses.ui.components


import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.example.splitexpenses.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateGroupDialog(
    onDismiss: () -> Unit,
    onCreate: (String, List<String>, String, (Boolean, String?) -> Unit) -> Unit,
    onImportClick: () -> Unit
) {
    var groupName by remember { mutableStateOf("") }
    var creatorName by remember { mutableStateOf("") }
    var memberFields by remember { mutableStateOf(listOf("")) }
    var showError by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = {
            Text(
                text = "Create New Group",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                // Group name field
                OutlinedTextField(
                    value = groupName,
                    onValueChange = {
                        groupName = it
                        showError = false
                    },
                    label = { Text("Group Name") },
                    modifier = Modifier.fillMaxWidth(),
                    isError = showError && groupName.isBlank(),
                    singleLine = true,
                    colors = TextFieldDefaults.colors(
                        unfocusedContainerColor = Color.Transparent,
                        focusedContainerColor = Color.Transparent,
                        unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Creator name field
                OutlinedTextField(
                    value = creatorName,
                    onValueChange = {
                        creatorName = it
                        showError = false
                    },
                    label = { Text("Your Name") },
                    modifier = Modifier.fillMaxWidth(),
                    isError = showError && creatorName.isBlank(),
                    singleLine = true,
                    colors = TextFieldDefaults.colors(
                        unfocusedContainerColor = Color.Transparent,
                        focusedContainerColor = Color.Transparent,
                        unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Member fields section
                Text(
                    text = "Group Members",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Member fields
                Column(
                    modifier = Modifier.heightIn(0.dp, 200.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .verticalScroll(rememberScrollState())
                    ) {
                        memberFields.forEachIndexed { index, member ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 2.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                OutlinedTextField(
                                    value = member,
                                    onValueChange = { newValue ->
                                        memberFields = memberFields.toMutableList().apply {
                                            this[index] = newValue
                                        }
                                        showError = false
                                    },
                                    label = { Text("Member ${index + 1}") },
                                    modifier = Modifier.weight(1f),
                                    isError = showError && member.isBlank(),
                                    singleLine = true,
                                    colors = TextFieldDefaults.colors(
                                        unfocusedContainerColor = Color.Transparent,
                                        focusedContainerColor = Color.Transparent,
                                        unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                                    )
                                )

                                if (memberFields.size > 1) {
                                    IconButton(
                                        onClick = {
                                            memberFields = memberFields.toMutableList().apply {
                                                removeAt(index)
                                            }
                                        }
                                    ) {
                                        Icon(
                                            Icons.Default.Delete,
                                            contentDescription = "Remove member",
                                            tint = MaterialTheme.colorScheme.error
                                        )
                                    }
                                }
                            }
                        }
                    }
                }


                // Add member button
                TextButton(
                    onClick = {
                        memberFields = memberFields + ""
                    },
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Icon(
                        Icons.Default.Add,
                        contentDescription = "Add member",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Add Member")
                }

                // Error message
                if (errorMessage != null) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = errorMessage!!,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // Loading indicator
                if (isLoading) {
                    Spacer(modifier = Modifier.height(16.dp))
                    LinearProgressIndicator(
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        },
        confirmButton = {
            Row {
                TextButton(
                    onClick = onImportClick,
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.resource_import),
                        contentDescription = "Import",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Import from CSV")
                }

                Button(
                    onClick = {
                        if (groupName.isBlank() || creatorName.isBlank()) {
                            showError = true
                            return@Button
                        }

                        // Reset error state
                        errorMessage = null
                        isLoading = true

                        // Filter out empty member fields
                        val validMembers = memberFields.filter { it.isNotBlank() }

                        // Make sure we have at least one valid member (the creator)
                        if (validMembers.isEmpty()) {
                            // Add the creator as a member if no other members
                            onCreate(
                                groupName,
                                listOf(creatorName),
                                creatorName
                            ) { success, error ->
                                isLoading = false
                                if (!success) {
                                    errorMessage = error ?: "Failed to create group"
                                }
                            }
                        } else {
                            // Make sure creator is included in members list
                            val finalMembers = if (validMembers.contains(creatorName)) {
                                validMembers
                            } else {
                                validMembers + creatorName
                            }
                            onCreate(groupName, finalMembers, creatorName) { success, error ->
                                isLoading = false
                                if (!success) {
                                    errorMessage = error ?: "Failed to create group"
                                }
                            }
                        }
                    },
                    enabled = !isLoading && groupName.isNotBlank() && creatorName.isNotBlank()
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text("Create")
                    }
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}