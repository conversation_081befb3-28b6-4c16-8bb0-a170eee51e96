package com.example.splitexpenses.data.source

import android.util.Log
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.getDefaultCategories
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.GenericTypeIndicator
import com.google.firebase.database.ValueEventListener
import com.google.firebase.database.ktx.database
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await

/**
 * Firebase implementation of the DataSource interface
 */
class FirebaseDataSource : DataSource {
    private val TAG = "FirebaseDataSource"
    private val database: DatabaseReference = Firebase.database.reference

    // Keep track of active listeners to clean them up later
    private val activeListeners = mutableMapOf<String, ValueEventListener>()

    /**
     * Safely deserialize a GroupData object from a DataSnapshot
     * This handles the case where categories might be stored as an ArrayList instead of a Map
     */
    private fun safelyDeserializeGroupData(snapshot: DataSnapshot): GroupData? {
        try {
            return snapshot.getValue(GroupData::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error deserializing GroupData: ${e.message}")

            // If we get here, try to manually construct the GroupData object
            try {
                val id = snapshot.child("id").getValue(String::class.java) ?: ""
                val name = snapshot.child("name").getValue(String::class.java) ?: ""

                // Get members list
                val membersType = object : GenericTypeIndicator<List<String>>() {}
                val members = snapshot.child("members").getValue(membersType) ?: emptyList()

                // Get expenses list
                val expensesType = object : GenericTypeIndicator<List<Expense>>() {}
                val expenses = snapshot.child("expenses").getValue(expensesType) ?: emptyList()

                // Get memberUidMap
                val memberUidMapType = object : GenericTypeIndicator<Map<String, String>>() {}
                val memberUidMap = snapshot.child("memberUidMap").getValue(memberUidMapType) ?: emptyMap()

                // Get allowedUsers list
                val allowedUsersType = object : GenericTypeIndicator<List<String>>() {}
                val allowedUsers = snapshot.child("allowedUsers").getValue(allowedUsersType) ?: emptyList()

                // Get creatorUid
                val creatorUid = snapshot.child("creatorUid").getValue(String::class.java) ?: ""

                // For categories, use default if there's an issue
                val categories = try {
                    val categoriesType = object : GenericTypeIndicator<List<Category>>() {}
                    snapshot.child("categories").getValue(categoriesType) ?: getDefaultCategories()
                } catch (e: Exception) {
                    Log.w(TAG, "Error deserializing categories, using defaults: ${e.message}")
                    getDefaultCategories()
                }

                // Get memberAvatars map
                val memberAvatarsType = object : GenericTypeIndicator<Map<String, String>>() {}
                val memberAvatars = try {
                    val avatarsSnapshot = snapshot.child("memberAvatars")
                    if (avatarsSnapshot.exists()) {
                        val result = avatarsSnapshot.getValue(memberAvatarsType)
                        Log.d(TAG, "Deserialized memberAvatars: $result")
                        result ?: emptyMap()
                    } else {
                        Log.d(TAG, "memberAvatars node doesn't exist, using empty map")
                        emptyMap<String, String>()
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Error deserializing memberAvatars, using empty map: ${e.message}")
                    emptyMap<String, String>()
                }

                return GroupData(
                    id = id,
                    name = name,
                    members = members,
                    expenses = expenses,
                    memberUidMap = memberUidMap,
                    allowedUsers = allowedUsers,
                    creatorUid = creatorUid,
                    categories = categories,
                    memberAvatars = memberAvatars
                )
            } catch (e2: Exception) {
                Log.e(TAG, "Failed to manually deserialize GroupData: ${e2.message}")
                return null
            }
        }
    }

    override suspend fun getGroup(groupId: String): GroupData? {
        return try {
            val snapshot = database.child("groups").child(groupId).get().await()
            val group = safelyDeserializeGroupData(snapshot)

            // Sort expenses by date (most recent first) if the group exists
            group?.let {
                // Create a sorted copy of the expenses list
                val sortedExpenses = it.expenses.sortedByDescending { expense -> expense.date }
                // Return a copy of the group with sorted expenses
                it.copy(expenses = sortedExpenses)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching group $groupId", e)
            null
        }
    }

    override suspend fun getAvailableGroups(): List<GroupData> {
        return try {
            val snapshot = database.child("groups").get().await()
            val groups = mutableListOf<GroupData>()

            for (childSnapshot in snapshot.children) {
                val group = safelyDeserializeGroupData(childSnapshot)
                if (group != null) {
                    // Sort expenses by date (most recent first)
                    val sortedExpenses = group.expenses.sortedByDescending { expense -> expense.date }
                    // Add a copy of the group with sorted expenses
                    groups.add(group.copy(expenses = sortedExpenses))
                }
            }

            groups
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching available groups", e)
            emptyList()
        }
    }

    override fun observeGroup(groupId: String): Flow<GroupData?> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val group = safelyDeserializeGroupData(snapshot)

                // Sort expenses by date (most recent first) if the group exists
                val sortedGroup = group?.let {
                    // Create a sorted copy of the expenses list
                    val sortedExpenses = it.expenses.sortedByDescending { expense -> expense.date }
                    // Return a copy of the group with sorted expenses
                    it.copy(expenses = sortedExpenses)
                }

                trySend(sortedGroup)
            }

            override fun onCancelled(error: DatabaseError) {
                Log.e(TAG, "Error observing group $groupId: ${error.message}")
                trySend(null)
            }
        }

        // Store the listener for cleanup
        val listenerKey = "group_$groupId"
        activeListeners[listenerKey] = listener

        // Attach the listener
        database.child("groups").child(groupId).addValueEventListener(listener)

        // Remove the listener when the flow is cancelled
        awaitClose {
            database.child("groups").child(groupId).removeEventListener(listener)
            activeListeners.remove(listenerKey)
        }
    }

    override fun observeAvailableGroups(): Flow<List<GroupData>> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val groups = mutableListOf<GroupData>()

                for (childSnapshot in snapshot.children) {
                    try {
                        val group = safelyDeserializeGroupData(childSnapshot)
                        if (group != null) {
                            // Sort expenses by date (most recent first)
                            val sortedExpenses = group.expenses.sortedByDescending { expense -> expense.date }
                            // Add a copy of the group with sorted expenses
                            groups.add(group.copy(expenses = sortedExpenses))
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing group in observeAvailableGroups: ${e.message}")
                        // Continue with the next group instead of failing completely
                    }
                }

                trySend(groups)
            }

            override fun onCancelled(error: DatabaseError) {
                Log.e(TAG, "Error observing available groups: ${error.message}")
                trySend(emptyList())
            }
        }

        // Store the listener for cleanup
        val listenerKey = "available_groups"
        activeListeners[listenerKey] = listener

        // Attach the listener
        database.child("groups").addValueEventListener(listener)

        // Remove the listener when the flow is cancelled
        awaitClose {
            database.child("groups").removeEventListener(listener)
            activeListeners.remove(listenerKey)
        }
    }

    override suspend fun saveGroup(group: GroupData) {
        try {
            database.child("groups").child(group.id).setValue(group).await()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving group ${group.id}", e)
            throw e
        }
    }

    override suspend fun updateGroupField(groupId: String, field: String, value: Any) {
        try {
            database.child("groups").child(groupId).child(field).setValue(value).await()
        } catch (e: Exception) {
            Log.e(TAG, "Error updating field $field in group $groupId", e)
            throw e
        }
    }

    override suspend fun deleteGroup(groupId: String) {
        try {
            database.child("groups").child(groupId).removeValue().await()
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting group $groupId", e)
            throw e
        }
    }

    override suspend fun addExpense(groupId: String, expense: Expense) {
        try {
            val group = getGroup(groupId) ?: throw IllegalStateException("Group not found")
            // Add the new expense and sort by date (most recent first)
            val updatedExpenses = (group.expenses + expense).sortedByDescending { it.date }
            updateGroupField(groupId, "expenses", updatedExpenses)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding expense to group $groupId", e)
            throw e
        }
    }

    override suspend fun updateExpense(groupId: String, expense: Expense) {
        try {
            val group = getGroup(groupId) ?: throw IllegalStateException("Group not found")
            val expenseIndex = group.expenses.indexOfFirst { it.id == expense.id }

            if (expenseIndex == -1) {
                throw IllegalStateException("Expense not found in group")
            }

            // Replace the expense and re-sort by date (most recent first)
            val updatedExpenses = group.expenses.toMutableList()
            updatedExpenses[expenseIndex] = expense
            val sortedExpenses = updatedExpenses.sortedByDescending { it.date }

            updateGroupField(groupId, "expenses", sortedExpenses)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating expense in group $groupId", e)
            throw e
        }
    }

    override suspend fun deleteExpense(groupId: String, expenseId: String) {
        deleteExpenses(groupId, setOf(expenseId))
    }

    override suspend fun deleteExpenses(groupId: String, expenseIds: Set<String>) {
        try {
            val group = getGroup(groupId) ?: throw IllegalStateException("Group not found")
            // Filter out deleted expenses and ensure they remain sorted by date (most recent first)
            val updatedExpenses = group.expenses
                .filter { it.id !in expenseIds }
                .sortedByDescending { it.date }
            updateGroupField(groupId, "expenses", updatedExpenses)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting expenses from group $groupId", e)
            throw e
        }
    }

    override fun cleanup() {
        // Remove all active listeners
        activeListeners.forEach { (key, listener) ->
            if (key.startsWith("group_")) {
                val groupId = key.removePrefix("group_")
                database.child("groups").child(groupId).removeEventListener(listener)
            } else if (key == "available_groups") {
                database.child("groups").removeEventListener(listener)
            }
        }

        activeListeners.clear()
    }
}
