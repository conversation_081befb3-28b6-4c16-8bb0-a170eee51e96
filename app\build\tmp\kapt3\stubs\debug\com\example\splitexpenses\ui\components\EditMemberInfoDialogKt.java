package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000.\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\u001ah\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00012\b\u0010\t\u001a\u0004\u0018\u00010\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b24\u0010\f\u001a0\u0012\u0004\u0012\u00020\u0001\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0012\u0004\u0012\u00020\u00070\u000e\u0012\u0004\u0012\u00020\u00070\r2\b\b\u0002\u0010\u0010\u001a\u00020\u000fH\u0007\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0086T\u00a2\u0006\u0002\n\u0000\"\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0005\u00a8\u0006\u0011"}, d2 = {"NO_AVATAR", "", "commonEmojis", "", "getCommonEmojis", "()Ljava/util/List;", "EditMemberInfoDialog", "", "currentName", "currentAvatar", "onDismiss", "Lkotlin/Function0;", "onSave", "Lkotlin/Function3;", "Lkotlin/Function2;", "", "isOffline", "app_debug"})
public final class EditMemberInfoDialogKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> commonEmojis = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String NO_AVATAR = "NO_AVATAR";
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<java.lang.String> getCommonEmojis() {
        return null;
    }
    
    /**
     * Dialog for editing a member's information
     * @param currentName The current name of the member
     * @param currentAvatar The current avatar of the member (can be null)
     * @param onDismiss Callback for when the dialog is dismissed
     * @param onSave Callback for when the information is saved, with a callback for success/failure
     */
    @androidx.compose.runtime.Composable()
    public static final void EditMemberInfoDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String currentName, @org.jetbrains.annotations.Nullable()
    java.lang.String currentAvatar, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit>, kotlin.Unit> onSave, boolean isOffline) {
    }
}