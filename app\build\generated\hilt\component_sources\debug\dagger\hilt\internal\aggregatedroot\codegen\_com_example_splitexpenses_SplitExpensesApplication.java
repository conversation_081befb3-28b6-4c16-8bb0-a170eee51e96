package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.splitexpenses.SplitExpensesApplication",
    rootPackage = "com.example.splitexpenses",
    originatingRoot = "com.example.splitexpenses.SplitExpensesApplication",
    originatingRootPackage = "com.example.splitexpenses",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "SplitExpensesApplication",
    originatingRootSimpleNames = "SplitExpensesApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_splitexpenses_SplitExpensesApplication {
}
