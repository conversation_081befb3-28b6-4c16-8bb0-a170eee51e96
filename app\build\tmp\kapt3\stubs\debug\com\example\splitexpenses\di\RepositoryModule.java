package com.example.splitexpenses.di;

/**
 * Hilt module that provides repository dependencies
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0007J\u0018\u0010\t\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0007\u00a8\u0006\f"}, d2 = {"Lcom/example/splitexpenses/di/RepositoryModule;", "", "()V", "provideExpenseRepository", "Lcom/example/splitexpenses/data/repositories/ExpenseRepository;", "offlineCapableRepository", "Lcom/example/splitexpenses/data/repositories/OfflineCapableRepository;", "groupRepository", "Lcom/example/splitexpenses/data/repositories/GroupRepository;", "provideGroupRepository", "localDataSource", "Lcom/example/splitexpenses/data/source/LocalDataSource;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class RepositoryModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.splitexpenses.di.RepositoryModule INSTANCE = null;
    
    private RepositoryModule() {
        super();
    }
    
    /**
     * Provides the group repository
     * @param offlineCapableRepository The offline-capable repository
     * @param localDataSource The data source for local operations
     * @return The group repository implementation
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.repositories.GroupRepository provideGroupRepository(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.OfflineCapableRepository offlineCapableRepository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.source.LocalDataSource localDataSource) {
        return null;
    }
    
    /**
     * Provides the expense repository
     * @param offlineCapableRepository The offline-capable repository
     * @param groupRepository The group repository
     * @return The expense repository implementation
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.repositories.ExpenseRepository provideExpenseRepository(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.OfflineCapableRepository offlineCapableRepository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.GroupRepository groupRepository) {
        return null;
    }
}