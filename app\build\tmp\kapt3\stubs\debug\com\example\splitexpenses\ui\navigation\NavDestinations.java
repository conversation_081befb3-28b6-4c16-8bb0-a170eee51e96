package com.example.splitexpenses.ui.navigation;

/**
 * Navigation destinations for the app
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\r\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/example/splitexpenses/ui/navigation/NavDestinations;", "", "()V", "BALANCE_DETAILS_ROUTE", "", "EXPENSE_DETAILS_ROUTE", "EXPENSE_DETAILS_ROUTE_WITH_PARAMS", "EXPENSE_EDIT_ROUTE", "EXPENSE_EDIT_ROUTE_WITH_PARAMS", "EXPENSE_ID_ARG", "EXPENSE_LIST_ROUTE", "EXPENSE_LIST_ROUTE_WITH_PARAMS", "GROUP_ID_ARG", "GROUP_LIST_ROUTE", "MANAGE_CATEGORIES_ROUTE", "MANAGE_MEMBERS_ROUTE", "STATISTICS_ROUTE", "app_debug"})
public final class NavDestinations {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GROUP_LIST_ROUTE = "group_list";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXPENSE_LIST_ROUTE = "expense_list";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXPENSE_DETAILS_ROUTE = "expense_details";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXPENSE_EDIT_ROUTE = "expense_edit";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String BALANCE_DETAILS_ROUTE = "balance_details";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String STATISTICS_ROUTE = "statistics";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MANAGE_CATEGORIES_ROUTE = "manage_categories";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MANAGE_MEMBERS_ROUTE = "manage_members";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXPENSE_DETAILS_ROUTE_WITH_PARAMS = "expense_details/{expenseId}";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXPENSE_EDIT_ROUTE_WITH_PARAMS = "expense_edit?expenseId={expenseId}";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXPENSE_LIST_ROUTE_WITH_PARAMS = "expense_list/{groupId}";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXPENSE_ID_ARG = "expenseId";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GROUP_ID_ARG = "groupId";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.splitexpenses.ui.navigation.NavDestinations INSTANCE = null;
    
    private NavDestinations() {
        super();
    }
}