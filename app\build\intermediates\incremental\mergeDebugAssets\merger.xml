<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="androidx.benchmark:benchmark-macro:1.3.4" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\assets"><file name="trace_processor_shell_aarch64" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\assets\trace_processor_shell_aarch64"/><file name="trace_processor_shell_arm" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\assets\trace_processor_shell_arm"/><file name="trace_processor_shell_x86" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\assets\trace_processor_shell_x86"/><file name="trace_processor_shell_x86_64" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\assets\trace_processor_shell_x86_64"/></source></dataSet><dataSet config="androidx.benchmark:benchmark-common:1.3.4" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\assets"><file name="tracebox_aarch64" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\assets\tracebox_aarch64"/><file name="tracebox_arm" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\assets\tracebox_arm"/><file name="tracebox_x86" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\assets\tracebox_x86"/><file name="tracebox_x86_64" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\assets\tracebox_x86_64"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\assets"><file name="emoji.json" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\assets\emoji.json"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>