{"logs": [{"outputFile": "com.example.splitexpenses.app-mergeDebugResources-73:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fbd72a3f323a8e4b4a1a522b1d003ecb\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,13134", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,13211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a570daead3d9a0e09cee3c22b866f0e\\transformed\\material3-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,407,524,627,725,840,977,1094,1249,1334,1434,1526,1627,1747,1869,1974,2118,2253,2390,2562,2694,2820,2945,3073,3166,3266,3394,3536,3635,3737,3846,3986,4127,4237,4339,4417,4512,4609,4717,4803,4889,4995,5075,5160,5268,5370,5474,5572,5660,5766,5872,5974,6096,6176,6283", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,107,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "168,287,402,519,622,720,835,972,1089,1244,1329,1429,1521,1622,1742,1864,1969,2113,2248,2385,2557,2689,2815,2940,3068,3161,3261,3389,3531,3630,3732,3841,3981,4122,4232,4334,4412,4507,4604,4712,4798,4884,4990,5070,5155,5263,5365,5469,5567,5655,5761,5867,5969,6091,6171,6278,6378"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6454,6572,6691,6806,6923,7026,7124,7239,7376,7493,7648,7733,7833,7925,8026,8146,8268,8373,8517,8652,8789,8961,9093,9219,9344,9472,9565,9665,9793,9935,10034,10136,10245,10385,10526,10636,10738,10816,10911,11008,11116,11202,11288,11394,11474,11559,11667,11769,11873,11971,12059,12165,12271,12373,12495,12575,12682", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,107,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "6567,6686,6801,6918,7021,7119,7234,7371,7488,7643,7728,7828,7920,8021,8141,8263,8368,8512,8647,8784,8956,9088,9214,9339,9467,9560,9660,9788,9930,10029,10131,10240,10380,10521,10631,10733,10811,10906,11003,11111,11197,11283,11389,11469,11554,11662,11764,11868,11966,12054,12160,12266,12368,12490,12570,12677,12777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\035a4e7aa700a3b95d1ef9282dca08ff\\transformed\\foundation-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "13898,13989", "endColumns": "90,91", "endOffsets": "13984,14076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d43e0e4658725c97c9b3b7b9218894ad\\transformed\\play-services-basement-18.3.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4755", "endColumns": "131", "endOffsets": "4882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\af30d809214462b80fbfe822607332fe\\transformed\\ui-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,492,582,664,753,846,929,1017,1105,1181,1262,1338,1413,1492,1562", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,80,75,74,78,69,123", "endOffsets": "195,277,382,487,577,659,748,841,924,1012,1100,1176,1257,1333,1408,1487,1557,1681"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3521,3616,5983,6088,6193,6283,6365,12782,12875,12958,13046,13216,13292,13373,13449,13625,13704,13774", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,80,75,74,78,69,123", "endOffsets": "3611,3693,6083,6188,6278,6360,6449,12870,12953,13041,13129,13287,13368,13444,13519,13699,13769,13893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\faac399e6b2ef4972910475a8683353b\\transformed\\core-1.16.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2791,2889,2993,3092,3195,3301,3408,13524", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "2884,2988,3087,3190,3296,3403,3516,13620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e2673b804717d9b59abfbd1af2f09ef\\transformed\\play-services-base-18.1.0\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3698,3809,3985,4120,4224,4392,4521,4645,4887,5047,5157,5322,5453,5611,5768,5829,5898", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "3804,3980,4115,4219,4387,4516,4640,4750,5042,5152,5317,5448,5606,5763,5824,5893,5978"}}]}]}