package com.example.splitexpenses.data.source;

import com.example.splitexpenses.data.cache.SplitExpensesDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OfflineDataSource_Factory implements Factory<OfflineDataSource> {
  private final Provider<SplitExpensesDatabase> databaseProvider;

  public OfflineDataSource_Factory(Provider<SplitExpensesDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public OfflineDataSource get() {
    return newInstance(databaseProvider.get());
  }

  public static OfflineDataSource_Factory create(Provider<SplitExpensesDatabase> databaseProvider) {
    return new OfflineDataSource_Factory(databaseProvider);
  }

  public static OfflineDataSource newInstance(SplitExpensesDatabase database) {
    return new OfflineDataSource(database);
  }
}
