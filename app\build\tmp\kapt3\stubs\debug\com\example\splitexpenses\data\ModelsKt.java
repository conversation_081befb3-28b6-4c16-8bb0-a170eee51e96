package com.example.splitexpenses.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0016\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001c\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u001a\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a8\u0006\u0007"}, d2 = {"detectCategory", "", "description", "categories", "", "Lcom/example/splitexpenses/data/Category;", "getDefaultCategories", "app_debug"})
public final class ModelsKt {
    
    /**
     * Get the default categories for a new group
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.example.splitexpenses.data.Category> getDefaultCategories() {
        return null;
    }
    
    /**
     * Detect a category based on a description
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String detectCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Category> categories) {
        return null;
    }
}