package com.example.splitexpenses.ui.viewmodels

import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.UserFinance
import com.example.splitexpenses.data.repositories.ExpenseRepository
import com.example.splitexpenses.data.repositories.GroupRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import kotlin.math.absoluteValue

/**
 * UI state for the balance details screen
 */
data class BalanceDetailsUiState(
    val finances: List<UserFinance> = emptyList(),
    val settlements: List<Triple<String, String, Double>> = emptyList(),
    val totalSpent: Double = 0.0,
    val selectedUserFilters: Set<String> = emptySet(),
    override val isLoading: Boolean = false,
    override val error: String? = null
) : UiState

/**
 * ViewModel for the balance details screen
 */
@HiltViewModel
class BalanceDetailsViewModel @Inject constructor(
    private val expenseRepository: ExpenseRepository,
    private val groupRepository: GroupRepository
) : BaseViewModel<BalanceDetailsUiState>() {

    // Expose the current group as a StateFlow
    val currentGroup: StateFlow<GroupData?> = groupRepository.currentGroup

    init {
        // Calculate finances when the ViewModel is created
        calculateFinances()
    }

    override fun createInitialState(): BalanceDetailsUiState {
        return BalanceDetailsUiState()
    }

    /**
     * Calculate finances for the current group
     */
    fun calculateFinances() {
        updateState { it.copy(isLoading = true, error = null) }

        try {
            val finances = expenseRepository.calculateFinances()
            val settlements = expenseRepository.calculateSettlements()
            val totalSpent = finances.sumOf { it.userExpense }

            // Get the current user's name to set as default filter
            val currentUserName = getCurrentUserName()
            val defaultFilter = if (currentUserName != null) setOf(currentUserName) else emptySet()

            updateState { state ->
                state.copy(
                    finances = finances,
                    settlements = settlements,
                    totalSpent = totalSpent,
                    selectedUserFilters = defaultFilter,
                    isLoading = false
                )
            }
        } catch (e: Exception) {
            updateState { it.copy(isLoading = false, error = e.message) }
        }
    }

    /**
     * Get the current user's name for the current group
     * @return The current user's name or null if not found
     */
    private fun getCurrentUserName(): String? {
        val currentGroupValue = currentGroup.value
        return if (currentGroupValue != null) {
            groupRepository.getSavedUserForGroup(currentGroupValue.id)
        } else {
            null
        }
    }

    /**
     * Toggle a user filter on or off
     * @param userId The user ID to toggle
     */
    fun toggleUserFilter(userId: String) {
        updateState { state ->
            val currentFilters = state.selectedUserFilters.toMutableSet()
            if (userId in currentFilters) {
                currentFilters.remove(userId)
            } else {
                currentFilters.add(userId)
            }
            state.copy(selectedUserFilters = currentFilters)
        }
    }

    /**
     * Clear all user filters
     */
    fun clearUserFilters() {
        updateState { state ->
            state.copy(selectedUserFilters = emptySet())
        }
    }

    /**
     * Get finances filtered by selected users
     * @return Filtered list of UserFinance objects
     */
    fun getFilteredFinances(): List<UserFinance> {
        val currentState = uiState.value
        val selectedFilters = currentState.selectedUserFilters

        return if (selectedFilters.isEmpty()) {
            currentState.finances
        } else {
            currentState.finances.filter { finance ->
                finance.userId in selectedFilters
            }
        }
    }

    /**
     * Get settlements filtered by selected users
     * @return Filtered list of settlements
     */
    fun getFilteredSettlements(): List<Triple<String, String, Double>> {
        val currentState = uiState.value
        val selectedFilters = currentState.selectedUserFilters

        return if (selectedFilters.isEmpty()) {
            currentState.settlements
        } else {
            currentState.settlements.filter { (debtor, creditor, _) ->
                debtor in selectedFilters || creditor in selectedFilters
            }
        }
    }

    /**
     * Get the absolute value of a user's balance
     * @param userFinance The user finance object
     * @return The absolute value of the user's balance
     */
    fun getAbsoluteBalance(userFinance: UserFinance): Double {
        return userFinance.userBalance.absoluteValue
    }

    /**
     * Get the avatar for a member
     * @param memberName The name of the member
     * @return The avatar emoji or null if not found
     */
    fun getMemberAvatar(memberName: String): String? {
        val currentGroupValue = currentGroup.value
        return if (currentGroupValue != null) {
            groupRepository.getMemberAvatar(currentGroupValue.id, memberName)
        } else {
            null
        }
    }

    /**
     * Check if a user is a creditor (has a positive balance)
     * @param userFinance The user finance object
     * @return True if the user is a creditor, false otherwise
     */
    fun isCreditor(userFinance: UserFinance): Boolean {
        return userFinance.userBalance > 0
    }

    /**
     * Check if a user is a debtor (has a negative balance)
     * @param userFinance The user finance object
     * @return True if the user is a debtor, false otherwise
     */
    fun isDebtor(userFinance: UserFinance): Boolean {
        return userFinance.userBalance < 0
    }

}
