package com.example.splitexpenses.ui.viewmodels;

import com.example.splitexpenses.data.repositories.GroupRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ManageMembersViewModel_Factory implements Factory<ManageMembersViewModel> {
  private final Provider<GroupRepository> groupRepositoryProvider;

  public ManageMembersViewModel_Factory(Provider<GroupRepository> groupRepositoryProvider) {
    this.groupRepositoryProvider = groupRepositoryProvider;
  }

  @Override
  public ManageMembersViewModel get() {
    return newInstance(groupRepositoryProvider.get());
  }

  public static ManageMembersViewModel_Factory create(
      Provider<GroupRepository> groupRepositoryProvider) {
    return new ManageMembersViewModel_Factory(groupRepositoryProvider);
  }

  public static ManageMembersViewModel newInstance(GroupRepository groupRepository) {
    return new ManageMembersViewModel(groupRepository);
  }
}
