{"logs": [{"outputFile": "com.example.splitexpenses.app-mergeDebugResources-73:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\faac399e6b2ef4972910475a8683353b\\transformed\\core-1.16.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2799,2897,2999,3100,3199,3304,3411,13337", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "2892,2994,3095,3194,3299,3406,3525,13433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d43e0e4658725c97c9b3b7b9218894ad\\transformed\\play-services-basement-18.3.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4700", "endColumns": "142", "endOffsets": "4838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\035a4e7aa700a3b95d1ef9282dca08ff\\transformed\\foundation-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "13705,13791", "endColumns": "85,88", "endOffsets": "13786,13875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\af30d809214462b80fbfe822607332fe\\transformed\\ui-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,993,1079,1151,1229,1305,1380,1459,1527", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,988,1074,1146,1224,1300,1375,1454,1522,1642"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3530,3623,5958,6052,6154,6246,6324,12613,12704,12785,12867,13036,13108,13186,13262,13438,13517,13585", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "3618,3701,6047,6149,6241,6319,6411,12699,12780,12862,12948,13103,13181,13257,13332,13512,13580,13700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fbd72a3f323a8e4b4a1a522b1d003ecb\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,12953", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,13031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a570daead3d9a0e09cee3c22b866f0e\\transformed\\material3-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,409,527,628,723,835,969,1085,1224,1309,1409,1502,1599,1715,1837,1942,2075,2205,2347,2510,2638,2755,2879,3000,3091,3188,3308,3423,3521,3624,3732,3864,4005,4115,4214,4298,4392,4487,4599,4691,4777,4890,4970,5056,5157,5260,5357,5458,5546,5652,5751,5854,5973,6053,6157", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "168,285,404,522,623,718,830,964,1080,1219,1304,1404,1497,1594,1710,1832,1937,2070,2200,2342,2505,2633,2750,2874,2995,3086,3183,3303,3418,3516,3619,3727,3859,4000,4110,4209,4293,4387,4482,4594,4686,4772,4885,4965,5051,5152,5255,5352,5453,5541,5647,5746,5849,5968,6048,6152,6247"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6416,6534,6651,6770,6888,6989,7084,7196,7330,7446,7585,7670,7770,7863,7960,8076,8198,8303,8436,8566,8708,8871,8999,9116,9240,9361,9452,9549,9669,9784,9882,9985,10093,10225,10366,10476,10575,10659,10753,10848,10960,11052,11138,11251,11331,11417,11518,11621,11718,11819,11907,12013,12112,12215,12334,12414,12518", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "6529,6646,6765,6883,6984,7079,7191,7325,7441,7580,7665,7765,7858,7955,8071,8193,8298,8431,8561,8703,8866,8994,9111,9235,9356,9447,9544,9664,9779,9877,9980,10088,10220,10361,10471,10570,10654,10748,10843,10955,11047,11133,11246,11326,11412,11513,11616,11713,11814,11902,12008,12107,12210,12329,12409,12513,12608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e2673b804717d9b59abfbd1af2f09ef\\transformed\\play-services-base-18.1.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3706,3812,3972,4097,4207,4360,4487,4599,4843,5018,5129,5293,5421,5582,5737,5805,5872", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "3807,3967,4092,4202,4355,4482,4594,4695,5013,5124,5288,5416,5577,5732,5800,5867,5953"}}]}]}