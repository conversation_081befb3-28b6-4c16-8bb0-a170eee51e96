package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\u001a2\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007H\u0007\u00a8\u0006\n"}, d2 = {"ExportDialog", "", "groupName", "", "onDismiss", "Lkotlin/Function0;", "onExport", "Lkotlin/Function1;", "Ljava/io/OutputStream;", "", "app_debug"})
public final class ExportDialogKt {
    
    /**
     * Dialog for exporting group data to CSV
     */
    @androidx.compose.runtime.Composable()
    public static final void ExportDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String groupName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.io.OutputStream, java.lang.Boolean> onExport) {
    }
}