package com.example.splitexpenses.data

data class GroupData(
    val id: String = "",
    val name: String = "",
    val members: List<String> = emptyList(),
    val expenses: List<Expense> = emptyList(),
    // Map of member names to UIDs for tracking which members are assigned to which devices
    val memberUidMap: Map<String, String> = emptyMap(),
    val allowedUsers: List<String> = emptyList(),
    // UID of the group creator - only the creator can delete the group or remove members
    val creatorUid: String = "",
    // Group-specific categories
    val categories: List<Category> = getDefaultCategories(),
    // Map of member names to their avatar emojis
    val memberAvatars: Map<String, String> = emptyMap()
)

data class Expense(
    val id: String = "",
    val amount: Double = 0.0,
    val description: String = "",
    val paidBy: String = "",
    val splitBetween: List<String> = emptyList(),
    val category: String = "",
    val date: Long = System.currentTimeMillis(),
    val timestamp: Long = System.currentTimeMillis(),
    val isCategoryLocked: Boolean = false
)



data class Category(
    val name: String = "",
    val emoji: String = "",
    val keywords: List<String> = emptyList()
) {
    // No-argument constructor required by Firebase
    constructor() : this("", "", emptyList())
}

/**
 * Get the default categories for a new group
 */
fun getDefaultCategories(): List<Category> {
    return listOf(
        Category("None", "💰", emptyList()),
        Category("Food", "🍔", listOf("restaurant", "cafe", "coffee", "lunch", "dinner", "breakfast", "food", "eat", "meal")),
        Category("Groceries", "🛒", listOf("supermarket", "grocery", "market", "food", "shop", "store")),
        Category("Transport", "🚗", listOf("taxi", "uber", "lyft", "bus", "train", "metro", "subway", "transport", "travel", "car", "fuel", "gas", "parking")),
        Category("Entertainment", "🎬", listOf("movie", "cinema", "theater", "concert", "show", "ticket", "entertainment", "fun")),
        Category("Shopping", "🛍️", listOf("shop", "store", "mall", "clothes", "shoes", "fashion", "shopping")),
        Category("Utilities", "💡", listOf("electricity", "water", "gas", "internet", "phone", "utility", "bill")),
        Category("Housing", "🏠", listOf("rent", "mortgage", "house", "apartment", "home", "housing")),
        Category("Health", "🏥", listOf("doctor", "hospital", "pharmacy", "medicine", "health", "medical")),
        Category("Travel", "✈️", listOf("flight", "hotel", "vacation", "trip", "travel", "holiday")),
        Category("Education", "📚", listOf("school", "university", "course", "book", "education", "study")),
        Category("Personal", "👤", listOf("personal", "self", "individual")),
        Category("Other", "🔄", emptyList())
    )
}

/**
 * Detect a category based on a description
 */
fun detectCategory(description: String, categories: List<Category>): String {
    val normalizedDescription = description.lowercase().trim()
    return categories.find { category ->
        category.keywords.any { keyword ->
            normalizedDescription.contains(keyword.lowercase())
        }
    }?.name ?: "None"
}

