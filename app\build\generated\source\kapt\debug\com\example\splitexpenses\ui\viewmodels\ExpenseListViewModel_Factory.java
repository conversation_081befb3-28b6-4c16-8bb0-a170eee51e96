package com.example.splitexpenses.ui.viewmodels;

import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager;
import com.example.splitexpenses.data.repositories.ExpenseRepository;
import com.example.splitexpenses.data.repositories.GroupRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExpenseListViewModel_Factory implements Factory<ExpenseListViewModel> {
  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<GroupRepository> groupRepositoryProvider;

  private final Provider<NetworkConnectivityManager> connectivityManagerProvider;

  public ExpenseListViewModel_Factory(Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<GroupRepository> groupRepositoryProvider,
      Provider<NetworkConnectivityManager> connectivityManagerProvider) {
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.groupRepositoryProvider = groupRepositoryProvider;
    this.connectivityManagerProvider = connectivityManagerProvider;
  }

  @Override
  public ExpenseListViewModel get() {
    return newInstance(expenseRepositoryProvider.get(), groupRepositoryProvider.get(), connectivityManagerProvider.get());
  }

  public static ExpenseListViewModel_Factory create(
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<GroupRepository> groupRepositoryProvider,
      Provider<NetworkConnectivityManager> connectivityManagerProvider) {
    return new ExpenseListViewModel_Factory(expenseRepositoryProvider, groupRepositoryProvider, connectivityManagerProvider);
  }

  public static ExpenseListViewModel newInstance(ExpenseRepository expenseRepository,
      GroupRepository groupRepository, NetworkConnectivityManager connectivityManager) {
    return new ExpenseListViewModel(expenseRepository, groupRepository, connectivityManager);
  }
}
