package com.example.splitexpenses.data.repositories;

/**
 * Repository for managing expenses with offline capability
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\"\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006JN\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00112\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00110\u00142\u0006\u0010\u0015\u001a\u00020\u00112\u0006\u0010\u0016\u001a\u00020\u00172\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0014J\u001e\u0010\u001d\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u000f0\u001e0\u0014J\u0016\u0010\u001f\u001a\u00020\r2\u0006\u0010 \u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010!J\u001c\u0010\"\u001a\u00020\r2\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00110$H\u0086@\u00a2\u0006\u0002\u0010%JV\u0010&\u001a\u00020\r2\u0006\u0010 \u001a\u00020\u00112\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00112\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00110\u00142\u0006\u0010\u0015\u001a\u00020\u00112\u0006\u0010\u0016\u001a\u00020\u00172\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\'R\u0019\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/example/splitexpenses/data/repositories/ExpenseRepository;", "", "offlineCapableRepository", "Lcom/example/splitexpenses/data/repositories/OfflineCapableRepository;", "groupRepository", "Lcom/example/splitexpenses/data/repositories/GroupRepository;", "(Lcom/example/splitexpenses/data/repositories/OfflineCapableRepository;Lcom/example/splitexpenses/data/repositories/GroupRepository;)V", "currentGroup", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/example/splitexpenses/data/GroupData;", "getCurrentGroup", "()Lkotlinx/coroutines/flow/StateFlow;", "addExpense", "", "amount", "", "description", "", "paidBy", "splitBetween", "", "category", "date", "", "isCategoryLocked", "", "(DLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "calculateFinances", "Lcom/example/splitexpenses/data/UserFinance;", "calculateSettlements", "Lkotlin/Triple;", "deleteExpense", "expenseId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenses", "expenseIds", "", "(Ljava/util/Set;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateExpense", "(Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class ExpenseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.OfflineCapableRepository offlineCapableRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.GroupRepository groupRepository = null;
    
    /**
     * Get the current group
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> currentGroup = null;
    
    @javax.inject.Inject()
    public ExpenseRepository(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.OfflineCapableRepository offlineCapableRepository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.GroupRepository groupRepository) {
        super();
    }
    
    /**
     * Get the current group
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> getCurrentGroup() {
        return null;
    }
    
    /**
     * Add a new expense to the current group
     * @param amount The amount of the expense
     * @param description The description of the expense
     * @param paidBy The name of the person who paid
     * @param splitBetween The names of the people to split the expense between
     * @param category The category of the expense
     * @param date The date of the expense
     * @param isCategoryLocked Whether the category is locked and should not be auto-detected
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addExpense(double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String paidBy, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> splitBetween, @org.jetbrains.annotations.NotNull()
    java.lang.String category, long date, boolean isCategoryLocked, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Update an existing expense
     * @param expenseId The ID of the expense to update
     * @param amount The new amount
     * @param description The new description
     * @param paidBy The new payer
     * @param splitBetween The new split
     * @param category The new category
     * @param date The new date
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String paidBy, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> splitBetween, @org.jetbrains.annotations.NotNull()
    java.lang.String category, long date, boolean isCategoryLocked, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Delete an expense
     * @param expenseId The ID of the expense to delete
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Delete multiple expenses
     * @param expenseIds The IDs of the expenses to delete
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpenses(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> expenseIds, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Calculate the financial balances for the current group
     * @return A list of UserFinance objects representing each member's financial state
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.data.UserFinance> calculateFinances() {
        return null;
    }
    
    /**
     * Calculate optimal settlements between members to resolve debts
     * @return A list of Triple objects representing (debtor, creditor, amount)
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<kotlin.Triple<java.lang.String, java.lang.String, java.lang.Double>> calculateSettlements() {
        return null;
    }
}