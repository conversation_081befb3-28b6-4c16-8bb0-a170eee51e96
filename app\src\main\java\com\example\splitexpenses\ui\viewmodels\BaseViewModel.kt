package com.example.splitexpenses.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Base ViewModel class that provides common functionality for all ViewModels
 */
abstract class BaseViewModel<S : UiState> : ViewModel() {
    
    private val _uiState = MutableStateFlow<S>(createInitialState())
    val uiState: StateFlow<S> = _uiState.asStateFlow()
    
    /**
     * Create the initial UI state for this ViewModel
     */
    abstract fun createInitialState(): S
    
    /**
     * Update the UI state
     */
    protected fun updateState(update: (S) -> S) {
        _uiState.value = update(_uiState.value)
    }
    
    /**
     * Launch a coroutine in the ViewModel scope with error handling
     */
    protected fun launchWithErrorHandling(
        onError: (Throwable) -> Unit = { handleError(it) },
        block: suspend () -> Unit
    ) {
        viewModelScope.launch {
            try {
                block()
            } catch (e: Throwable) {
                onError(e)
            }
        }
    }
    
    /**
     * Default error handling implementation
     */
    protected open fun handleError(error: Throwable) {
        // Default implementation - can be overridden by subclasses
        println("Error in ViewModel: ${error.message}")
    }
}

/**
 * Base interface for UI states
 */
interface UiState {
    val isLoading: Boolean
    val error: String?
}

/**
 * Base UI state implementation
 */
data class BaseUiState(
    override val isLoading: Boolean = false,
    override val error: String? = null
) : UiState
