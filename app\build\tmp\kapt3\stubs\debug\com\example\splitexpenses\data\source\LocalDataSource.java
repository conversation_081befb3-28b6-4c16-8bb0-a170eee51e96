package com.example.splitexpenses.data.source;

/**
 * Local data source for user preferences and device-specific data
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0007\u001a\u00020\u0003J\u0006\u0010\b\u001a\u00020\tJ\u0010\u0010\n\u001a\u0004\u0018\u00010\t2\u0006\u0010\u000b\u001a\u00020\tJ\u0018\u0010\f\u001a\u0004\u0018\u00010\t2\u0006\u0010\u000b\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\tJ\u0018\u0010\u000e\u001a\u0004\u0018\u00010\t2\u0006\u0010\u000b\u001a\u00020\t2\u0006\u0010\u000f\u001a\u00020\tJ\u001e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u000b\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\t2\u0006\u0010\u000f\u001a\u00020\tJ\u0016\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u000b\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/example/splitexpenses/data/source/LocalDataSource;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "userPreferences", "Lcom/example/splitexpenses/data/UserPreferences;", "getContext", "getDeviceUid", "", "getSavedUserForGroup", "groupId", "getUidForUserInGroup", "userName", "getUserNameForUidInGroup", "uid", "saveUidForUserInGroup", "", "saveUserForGroup", "app_debug"})
public final class LocalDataSource {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.UserPreferences userPreferences = null;
    
    public LocalDataSource(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Get the application context
     * @return The application context
     */
    @org.jetbrains.annotations.NotNull()
    public final android.content.Context getContext() {
        return null;
    }
    
    /**
     * Get the device's unique identifier
     * @return The device UID
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDeviceUid() {
        return null;
    }
    
    /**
     * Get the saved user name for a specific group
     * @param groupId The ID of the group
     * @return The user name or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSavedUserForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return null;
    }
    
    /**
     * Save the mapping between a user name and the group
     * @param groupId The ID of the group
     * @param userName The user name to save
     */
    public final void saveUserForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String userName) {
    }
    
    /**
     * Get the UID for a user in a specific group
     * @param groupId The ID of the group
     * @param userName The user name
     * @return The UID or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUidForUserInGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String userName) {
        return null;
    }
    
    /**
     * Save the mapping between a user name and UID for a specific group
     * @param groupId The ID of the group
     * @param userName The user name
     * @param uid The UID to save
     */
    public final void saveUidForUserInGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String userName, @org.jetbrains.annotations.NotNull()
    java.lang.String uid) {
    }
    
    /**
     * Get the user name for a UID in a specific group
     * @param groupId The ID of the group
     * @param uid The UID
     * @return The user name or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUserNameForUidInGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String uid) {
        return null;
    }
}