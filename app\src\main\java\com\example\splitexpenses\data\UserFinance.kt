package com.example.splitexpenses.data

/**
 * Data class representing a user's financial information in a group
 * @param userId The user's ID
 * @param userExpense The total amount spent by the user
 * @param userBalance The user's balance (positive means they are owed money, negative means they owe money)
 */
data class UserFinance(
    val userId: String = "",
    val userExpense: Double = 0.0, // Total amount spent by user
    val userBalance: Double = 0.0  // Positive means they are owed money, negative means they owe money
)
