package com.example.splitexpenses.ui.viewmodels;

/**
 * ViewModel for the manage members screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\f\u001a\u00020\rJ\u0006\u0010\u000e\u001a\u00020\rJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0011\u001a\u00020\u0010J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0011\u001a\u00020\u0010J\u0018\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0010H\u0002J\u000e\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u0010J\u000e\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u0010J\u000e\u0010\u001a\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u0010J\u000e\u0010\u001b\u001a\u00020\r2\u0006\u0010\u001c\u001a\u00020\u0010R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u001d"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/ManageMembersViewModel;", "Landroidx/lifecycle/ViewModel;", "groupRepository", "Lcom/example/splitexpenses/data/repositories/GroupRepository;", "(Lcom/example/splitexpenses/data/repositories/GroupRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/splitexpenses/ui/viewmodels/ManageMembersUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "addMember", "", "clearError", "getMemberAvatar", "", "memberName", "getMemberUid", "isCurrentUserGroupCreator", "", "group", "Lcom/example/splitexpenses/data/GroupData;", "currentUser", "isMemberAssigned", "isMemberGroupCreator", "removeMember", "updateNewMemberName", "name", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ManageMembersViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.GroupRepository groupRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.splitexpenses.ui.viewmodels.ManageMembersUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.ui.viewmodels.ManageMembersUiState> uiState = null;
    
    @javax.inject.Inject()
    public ManageMembersViewModel(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.GroupRepository groupRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.ui.viewmodels.ManageMembersUiState> getUiState() {
        return null;
    }
    
    /**
     * Update the new member name
     */
    public final void updateNewMemberName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
    }
    
    /**
     * Add a new member to the group
     */
    public final void addMember() {
    }
    
    /**
     * Remove a member from the group
     */
    public final void removeMember(@org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
    }
    
    /**
     * Get the avatar for a member
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMemberAvatar(@org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
        return null;
    }
    
    /**
     * Check if a member is assigned to a UID
     */
    public final boolean isMemberAssigned(@org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
        return false;
    }
    
    /**
     * Check if the current user is the group creator
     */
    private final boolean isCurrentUserGroupCreator(com.example.splitexpenses.data.GroupData group, java.lang.String currentUser) {
        return false;
    }
    
    /**
     * Get the UID for a member in the current group
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMemberUid(@org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
        return null;
    }
    
    /**
     * Check if a member is the group creator
     */
    public final boolean isMemberGroupCreator(@org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
        return false;
    }
    
    /**
     * Clear any error state
     */
    public final void clearError() {
    }
}