# Offline Data Caching Implementation

## Overview

This document describes the comprehensive offline data caching system implemented for the SplitExpenses app. The system allows users to access and create content while offline, with automatic synchronization when connectivity returns.

## Architecture

### Core Components

1. **Room Database** (`SplitExpensesDatabase`)
   - Local SQLite database for caching data
   - Entities: `GroupEntity`, `ExpenseEntity`, `CategoryEntity`, `SyncQueueEntity`
   - DAOs: `GroupDao`, `ExpenseDao`, `CategoryDao`, `SyncQueueDao`

2. **OfflineDataSource**
   - Handles all local database operations
   - Implements the same `DataSource` interface as `FirebaseDataSource`
   - Manages data conversion between entities and domain models

3. **OfflineCapableRepository**
   - Central coordinator for online/offline operations
   - Automatically switches between remote and local data sources
   - Handles caching and sync queue management

4. **SyncQueueManager**
   - Manages offline changes that need to be synced
   - Processes sync queue when connectivity returns
   - Handles retry logic and error handling

5. **NetworkConnectivityManager**
   - Monitors network connectivity status
   - Provides reactive connectivity updates via Flow

## Key Features

### ✅ **Read Operations**
- Always available regardless of connectivity
- Data served from cache when offline
- Automatic cache updates when online

### ✅ **Create Operations**
- New content can be created offline
- Changes stored locally and queued for sync
- Automatic sync when connectivity returns

### ❌ **Edit Operations**
- Blocked when offline for existing content
- Prevents data conflicts and sync issues
- Clear UI feedback when operations are blocked

### ❌ **Delete Operations**
- Blocked when offline
- Prevents accidental data loss
- UI elements disabled when offline

## Data Flow

### Online Mode
```
UI → Repository → OfflineCapableRepository → FirebaseDataSource → Firebase
                                         ↓
                                    OfflineDataSource → Room DB (cache)
```

### Offline Mode
```
UI → Repository → OfflineCapableRepository → OfflineDataSource → Room DB
                                         ↓
                                    SyncQueueManager → Sync Queue
```

### Sync Process
```
NetworkConnectivityManager → OfflineCapableRepository → SyncQueueManager
                                                     ↓
                                               FirebaseDataSource → Firebase
```

## Database Schema

### GroupEntity
- Stores group data with sync status
- Includes `isSynced` flag and `lastModified` timestamp
- Expenses stored separately in `ExpenseEntity`

### ExpenseEntity
- Stores individual expenses with group reference
- Includes sync status and modification tracking
- Linked to groups via `groupId` foreign key

### CategoryEntity
- Stores categories per group
- Composite key: `groupId + categoryName`
- Sync status tracking

### SyncQueueEntity
- Tracks pending changes for sync
- Includes entity type, operation type, and retry count
- JSON data storage for entity information

## Sync Strategy

### Automatic Sync
- Triggered when device comes online
- Processes all pending changes in chronological order
- Handles failures with retry logic (max 3 retries)

### Conflict Resolution
- Last-write-wins strategy for simplicity
- Offline changes always take precedence during sync
- No complex merge logic to avoid data corruption

### Error Handling
- Failed sync items remain in queue for retry
- Error messages stored for debugging
- Exponential backoff for retries (future enhancement)

## UI Integration

### Offline Status Indicator
- Shows when device is offline
- Displays pending sync count
- Provides sync progress feedback

### Disabled UI Elements
- Edit buttons grayed out when offline
- Delete operations blocked
- Clear visual feedback for blocked actions

### Sync Feedback
- Progress indicators during sync
- Success/failure notifications
- Pending changes counter

## Usage Examples

### Repository Integration
```kotlin
@Inject
class GroupRepository(
    private val offlineCapableRepository: OfflineCapableRepository,
    private val localDataSource: LocalDataSource
) {
    suspend fun createGroup(name: String, members: List<String>): String {
        val group = GroupData(...)
        offlineCapableRepository.saveGroup(group) // Handles online/offline automatically
        return group.id
    }
}
```

### UI Integration
```kotlin
@Composable
fun GroupListScreen() {
    val isOffline by networkConnectivityManager.isConnected()
        .map { !it }
        .collectAsState(initial = false)
    
    val pendingSyncCount by groupRepository.getPendingSyncCountFlow()
        .collectAsState(initial = 0)
    
    OfflineStatusIndicator(
        isOffline = isOffline,
        pendingSyncCount = pendingSyncCount
    )
}
```

## Benefits

1. **Improved User Experience**
   - App remains functional offline
   - No data loss when connectivity is poor
   - Seamless transition between online/offline

2. **Data Consistency**
   - Automatic caching ensures data availability
   - Sync queue prevents data loss
   - Clear separation of read/write operations

3. **Performance**
   - Faster data access from local cache
   - Reduced network requests
   - Better app responsiveness

4. **Reliability**
   - Graceful handling of network issues
   - Automatic retry mechanisms
   - Data persistence across app restarts

## Future Enhancements

1. **Advanced Conflict Resolution**
   - Three-way merge for complex conflicts
   - User-driven conflict resolution UI
   - Timestamp-based merge strategies

2. **Selective Sync**
   - Sync only specific data types
   - Priority-based sync ordering
   - Bandwidth-aware sync strategies

3. **Background Sync**
   - Sync during app background state
   - WorkManager integration
   - Periodic sync scheduling

4. **Data Compression**
   - Compress sync payloads
   - Delta sync for large datasets
   - Efficient data transfer protocols

## Testing

The offline functionality includes comprehensive unit tests covering:
- Online/offline mode switching
- Sync queue operations
- Error handling scenarios
- Data consistency checks

See `OfflineCapableRepositoryTest.kt` for test examples.
