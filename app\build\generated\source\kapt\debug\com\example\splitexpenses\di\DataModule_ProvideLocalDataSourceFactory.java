package com.example.splitexpenses.di;

import android.content.Context;
import com.example.splitexpenses.data.source.LocalDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideLocalDataSourceFactory implements Factory<LocalDataSource> {
  private final Provider<Context> contextProvider;

  public DataModule_ProvideLocalDataSourceFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LocalDataSource get() {
    return provideLocalDataSource(contextProvider.get());
  }

  public static DataModule_ProvideLocalDataSourceFactory create(Provider<Context> contextProvider) {
    return new DataModule_ProvideLocalDataSourceFactory(contextProvider);
  }

  public static LocalDataSource provideLocalDataSource(Context context) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideLocalDataSource(context));
  }
}
