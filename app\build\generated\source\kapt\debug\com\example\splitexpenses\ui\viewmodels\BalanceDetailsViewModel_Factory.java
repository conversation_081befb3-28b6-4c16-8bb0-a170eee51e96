package com.example.splitexpenses.ui.viewmodels;

import com.example.splitexpenses.data.repositories.ExpenseRepository;
import com.example.splitexpenses.data.repositories.GroupRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BalanceDetailsViewModel_Factory implements Factory<BalanceDetailsViewModel> {
  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<GroupRepository> groupRepositoryProvider;

  public BalanceDetailsViewModel_Factory(Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<GroupRepository> groupRepositoryProvider) {
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.groupRepositoryProvider = groupRepositoryProvider;
  }

  @Override
  public BalanceDetailsViewModel get() {
    return newInstance(expenseRepositoryProvider.get(), groupRepositoryProvider.get());
  }

  public static BalanceDetailsViewModel_Factory create(
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<GroupRepository> groupRepositoryProvider) {
    return new BalanceDetailsViewModel_Factory(expenseRepositoryProvider, groupRepositoryProvider);
  }

  public static BalanceDetailsViewModel newInstance(ExpenseRepository expenseRepository,
      GroupRepository groupRepository) {
    return new BalanceDetailsViewModel(expenseRepository, groupRepository);
  }
}
