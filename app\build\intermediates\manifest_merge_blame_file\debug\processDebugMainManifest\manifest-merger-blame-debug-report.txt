1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.splitexpenses"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions for file operations -->
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:6:5-7:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:7:9-35
15    <uses-permission
15-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:8:5-9:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:8:22-78
17        android:maxSdkVersion="29" />
17-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:9:9-35
18
19    <!-- For Android 13+ (API 33+), we use the new granular media permissions -->
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:12:5-76
20-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:12:22-73
21
22    <!-- Network permissions for connectivity monitoring -->
23    <uses-permission android:name="android.permission.INTERNET" />
23-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:15:5-67
23-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:15:22-64
24    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
24-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:16:5-79
24-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:16:22-76
25    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
25-->[androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:23:5-25:53
25-->[androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:24:9-61
26    <uses-permission android:name="android.permission.REORDER_TASKS" />
26-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:24:5-72
26-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:24:22-69
27
28    <permission
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
29        android:name="com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
32-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
33
34    <application
34-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:18:5-66:19
35        android:name="com.example.splitexpenses.SplitExpensesApplication"
35-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:19:9-49
36        android:allowBackup="true"
36-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:20:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:21:9-65
39        android:debuggable="true"
40        android:extractNativeLibs="false"
41        android:fullBackupContent="@xml/backup_rules"
41-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:22:9-54
42        android:icon="@mipmap/ic_launcher"
42-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:23:9-43
43        android:label="@string/app_name"
43-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:24:9-41
44        android:networkSecurityConfig="@xml/network_security_config"
44-->[androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:42:18-78
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:25:9-54
46        android:supportsRtl="true"
46-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:26:9-35
47        android:testOnly="true"
48        android:theme="@style/Theme.SplitExpenses" >
48-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:27:9-51
49        <activity
49-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:29:9-65:20
50            android:name="com.example.splitexpenses.ui.MainActivity"
50-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:30:13-44
51            android:exported="true"
51-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:31:13-36
52            android:label="@string/app_name"
52-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:32:13-45
53            android:theme="@style/Theme.SplitExpenses" >
53-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:33:13-55
54
55            <!-- Main launcher intent filter -->
56            <intent-filter>
56-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:35:13-38:29
57                <action android:name="android.intent.action.MAIN" />
57-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:36:17-69
57-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:36:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
59-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
60            </intent-filter>
61
62            <!-- Deep link intent filter for group invitations (old format) -->
63            <intent-filter>
63-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:41:13-51:29
64                <action android:name="android.intent.action.VIEW" />
64-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:17-69
64-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:25-66
65
66                <category android:name="android.intent.category.DEFAULT" />
66-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:17-76
66-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:27-73
67                <category android:name="android.intent.category.BROWSABLE" />
67-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:17-78
67-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:27-75
68
69                <!-- URI scheme for deep links (old format) -->
70                <data
70-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:47:17-50:49
71                    android:host="join"
71-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:49:21-40
72                    android:pathPattern="/.*"
72-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:50:21-46
73                    android:scheme="splitexpenses" />
73-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:48:21-51
74            </intent-filter>
75
76            <!-- Deep link intent filter for group invitations (new format) -->
77            <intent-filter>
77-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:54:13-64:29
78                <action android:name="android.intent.action.VIEW" />
78-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:17-69
78-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:25-66
79
80                <category android:name="android.intent.category.DEFAULT" />
80-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:17-76
80-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:27-73
81                <category android:name="android.intent.category.BROWSABLE" />
81-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:17-78
81-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:27-75
82
83                <!-- URI scheme for deep links (new format) -->
84                <data
84-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:47:17-50:49
85                    android:host="splitexpenses.example.com"
85-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:49:21-40
86                    android:pathPrefix="/join"
86-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:63:21-47
87                    android:scheme="https" />
87-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:48:21-51
88            </intent-filter>
89        </activity>
90
91        <service
91-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
92            android:name="com.google.firebase.components.ComponentDiscoveryService"
92-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:27:13-84
93            android:directBootAware="true"
93-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
94            android:exported="false" >
94-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:28:13-37
95            <meta-data
95-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
96                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
96-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
98            <meta-data
98-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
99                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
99-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
101            <meta-data
101-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
102                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
102-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
104            <meta-data
104-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
105                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
105-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
107        </service>
108
109        <activity
109-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
110            android:name="com.google.android.gms.common.api.GoogleApiActivity"
110-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
111            android:exported="false"
111-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
112            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
112-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
113
114        <provider
114-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
115            android:name="com.google.firebase.provider.FirebaseInitProvider"
115-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
116            android:authorities="com.example.splitexpenses.firebaseinitprovider"
116-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
117            android:directBootAware="true"
117-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
118            android:exported="false"
118-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
119            android:initOrder="100" />
119-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
120
121        <service
121-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
122            android:name="androidx.room.MultiInstanceInvalidationService"
122-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
123            android:directBootAware="true"
123-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
124            android:exported="false" /> <!-- Activity used to block background content while benchmarks are running -->
124-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
125        <activity
125-->[androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:32:9-36:20
126            android:name="androidx.benchmark.IsolationActivity"
126-->[androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:33:13-64
127            android:exported="true"
127-->[androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:34:13-36
128            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" >
128-->[androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:35:13-77
129        </activity>
130        <activity
130-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
131            android:name="androidx.activity.ComponentActivity"
131-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
132            android:exported="true" />
132-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
133        <activity
133-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
134            android:name="androidx.compose.ui.tooling.PreviewActivity"
134-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
135            android:exported="true" />
135-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
136        <activity
136-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:27:9-34:20
137            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
137-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:28:13-99
138            android:exported="true"
138-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:29:13-36
139            android:theme="@style/WhiteBackgroundTheme" >
139-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:30:13-56
140            <intent-filter android:priority="-100" >
140-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
140-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
141                <category android:name="android.intent.category.LAUNCHER" />
141-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
141-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
142            </intent-filter>
143        </activity>
144        <activity
144-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:35:9-42:20
145            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
145-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:36:13-95
146            android:exported="true"
146-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:37:13-36
147            android:theme="@style/WhiteBackgroundTheme" >
147-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:38:13-56
148            <intent-filter android:priority="-100" >
148-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
148-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
149                <category android:name="android.intent.category.LAUNCHER" />
149-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
149-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
150            </intent-filter>
151        </activity>
152        <activity
152-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:43:9-50:20
153            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
153-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:44:13-103
154            android:exported="true"
154-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:45:13-36
155            android:theme="@style/WhiteBackgroundDialogTheme" >
155-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:46:13-62
156            <intent-filter android:priority="-100" >
156-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
156-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
157                <category android:name="android.intent.category.LAUNCHER" />
157-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
157-->C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
158            </intent-filter>
159        </activity>
160
161        <provider
161-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
162            android:name="androidx.startup.InitializationProvider"
162-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:25:13-67
163            android:authorities="com.example.splitexpenses.androidx-startup"
163-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:26:13-68
164            android:exported="false" >
164-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:27:13-37
165            <meta-data
165-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
166                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
166-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
167                android:value="androidx.startup" />
167-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
168            <meta-data
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.emoji2.text.EmojiCompatInitializer"
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
170                android:value="androidx.startup" />
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
171            <meta-data
171-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:44:13-46:52
172                android:name="androidx.tracing.perfetto.StartupTracingInitializer"
172-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:45:17-83
173                android:value="androidx.startup" />
173-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:46:17-49
174            <meta-data
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
175                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
176                android:value="androidx.startup" />
176-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
177        </provider>
178
179        <meta-data
179-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
180            android:name="com.google.android.gms.version"
180-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
181            android:value="@integer/google_play_services_version" />
181-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
182
183        <receiver
183-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:24:9-37:20
184            android:name="androidx.tracing.perfetto.TracingReceiver"
184-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:25:13-69
185            android:directBootAware="false"
185-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:26:13-44
186            android:enabled="true"
186-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:27:13-35
187            android:exported="true"
187-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:28:13-36
188            android:permission="android.permission.DUMP" >
188-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:29:13-57
189
190            <!-- Note: DUMP above highly limits who can call the receiver; Shell has DUMP perm. -->
191            <intent-filter>
191-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:32:13-36:29
192                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING" />
192-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:33:17-90
192-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:33:25-87
193                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START" />
193-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:34:17-101
193-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:34:25-98
194                <action android:name="androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START" />
194-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:35:17-102
194-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:35:25-99
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:49:9-55:20
198            android:name="androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate"
198-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:50:13-92
199            android:directBootAware="false"
199-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:51:13-44
200            android:enabled="false"
200-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:52:13-36
201            android:exported="false" >
201-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:53:13-37
202        </receiver>
203        <receiver
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
204            android:name="androidx.profileinstaller.ProfileInstallReceiver"
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
205            android:directBootAware="false"
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
206            android:enabled="true"
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
207            android:exported="true"
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
208            android:permission="android.permission.DUMP" >
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
209            <intent-filter>
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
210                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
211            </intent-filter>
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
213                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
214            </intent-filter>
215            <intent-filter>
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
216                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
217            </intent-filter>
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
219                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
220            </intent-filter>
221        </receiver>
222    </application>
223
224</manifest>
