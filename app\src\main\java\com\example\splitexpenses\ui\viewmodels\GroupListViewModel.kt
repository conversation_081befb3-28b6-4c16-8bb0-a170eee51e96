package com.example.splitexpenses.ui.viewmodels

import androidx.lifecycle.viewModelScope
import java.io.InputStream

import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn

import dagger.hilt.android.lifecycle.HiltViewModel

import javax.inject.Inject

import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.repositories.GroupRepository
import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager
import com.example.splitexpenses.util.CsvImportResult

/**
 * UI state for the group list screen
 */
data class GroupListUiState(
    val groups: List<GroupData> = emptyList(),
    val selectedGroups: Set<String> = emptySet(),
    val isMultiSelectMode: Boolean = false,
    override val isLoading: Boolean = false,
    override val error: String? = null
) : UiState

/**
 * ViewModel for the group list screen
 */
@HiltViewModel
class GroupListViewModel @Inject constructor(
    private val groupRepository: GroupRepository,
    connectivityManager: NetworkConnectivityManager
) : OfflineAwareViewModel<GroupListUiState>(connectivityManager) {

    // Expose the available groups as a StateFlow
    val availableGroups: StateFlow<List<GroupData>> = groupRepository.availableGroups
    val isLoadingGroups: StateFlow<Boolean> = groupRepository.isLoadingGroups
    val groupsError: StateFlow<String?> = groupRepository.groupsError

    // Expose the pending sync count
    val pendingSyncCount: StateFlow<Int> = groupRepository.getPendingSyncCountFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = 0
        )

    init {
        // Start listening for available groups
        groupRepository.startListeningForAvailableGroups()
    }

    override fun createInitialState(): GroupListUiState {
        return GroupListUiState()
    }

    /**
     * Join a group
     * @param groupId The ID of the group to join
     * @param memberName The name of the member joining the group
     */
    fun joinGroup(groupId: String, memberName: String) {
        launchWithErrorHandling {
            groupRepository.joinGroup(groupId, memberName)
        }
    }

    /**
     * Create a new group
     * @param name The name of the group
     * @param members The initial members of the group
     * @param currentUser The current user's name
     * @return The ID of the newly created group
     */
    suspend fun createGroup(name: String, members: List<String>, currentUser: String): String {
        return groupRepository.createGroup(name, members, currentUser)
    }

    /**
     * Delete selected groups
     * @param groupIds The IDs of the groups to delete
     */
    fun deleteGroups(groupIds: Set<String>) {
        executeEditOperationIfConnected(
            operation = {
                groupIds.forEach { groupId ->
                    groupRepository.deleteGroup(groupId)
                }

                // Update UI state
                updateState { state ->
                    state.copy(
                        selectedGroups = emptySet(),
                        isMultiSelectMode = false
                    )
                }
            }
        )
    }

    /**
     * Toggle multi-select mode
     * @param enabled Whether multi-select mode should be enabled
     */
    fun setMultiSelectMode(enabled: Boolean) {
        updateState { state ->
            state.copy(
                isMultiSelectMode = enabled,
                selectedGroups = if (!enabled) emptySet() else state.selectedGroups
            )
        }
    }

    /**
     * Toggle selection of a group
     * @param groupId The ID of the group to toggle
     * @param selected Whether the group should be selected
     */
    fun toggleGroupSelection(groupId: String, selected: Boolean) {
        updateState { state ->
            val newSelectedGroups = state.selectedGroups.toMutableSet()

            if (selected) {
                newSelectedGroups.add(groupId)
            } else {
                newSelectedGroups.remove(groupId)
            }

            // Automatically disable multi-select mode when no items are selected
            val shouldDisableMultiSelect = newSelectedGroups.isEmpty() && state.isMultiSelectMode

            state.copy(
                selectedGroups = newSelectedGroups,
                isMultiSelectMode = if (shouldDisableMultiSelect) false else state.isMultiSelectMode
            )
        }
    }

    /**
     * Get the saved user name for a group
     * @param groupId The ID of the group
     * @return The user name or null if not found
     */
    fun getSavedUserForGroup(groupId: String): String? {
        return groupRepository.getSavedUserForGroup(groupId)
    }

    /**
     * Import a group from CSV
     * @param inputStream The input stream to read from
     * @param currentUser The current user's name (optional)
     * @return The result of the import operation
     */
    suspend fun importGroupFromCsv(inputStream: InputStream, currentUser: String? = null): CsvImportResult {
        return groupRepository.importGroupFromCsv(inputStream, currentUser)
    }

    /**
     * Get unassigned members for a group
     * @param groupId The ID of the group
     * @return List of unassigned member names
     */
    suspend fun getUnassignedMembers(groupId: String): List<String> {
        return groupRepository.getUnassignedMembers(groupId)
    }

    /**
     * Get all members in a group with their assignment status
     * @param groupId The ID of the group
     * @return Pair of (all members, assigned members)
     */
    suspend fun getGroupMembersWithStatus(groupId: String): Pair<List<String>, List<String>> {
        return groupRepository.getGroupMembersWithStatus(groupId)
    }

    /**
     * Check if the current user is the creator of a group
     * @param groupId The ID of the group
     * @return True if the current user is the creator, false otherwise
     */
    fun isCurrentUserGroupCreator(groupId: String): Boolean {
        return groupRepository.isCurrentUserGroupCreator(groupId)
    }

    override fun onCleared() {
        super.onCleared()
        // No need to clean up here as the repository is managed by the AppModule
    }
}
