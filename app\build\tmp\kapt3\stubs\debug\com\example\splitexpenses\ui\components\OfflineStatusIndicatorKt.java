package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\u001a\u001a\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a$\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u00a8\u0006\t"}, d2 = {"OfflineBadge", "", "isOffline", "", "modifier", "Landroidx/compose/ui/Modifier;", "OfflineStatusIndicator", "pendingSyncCount", "", "app_debug"})
public final class OfflineStatusIndicatorKt {
    
    /**
     * Composable that shows an offline status indicator when the device is offline
     * and sync status when there are pending changes
     */
    @androidx.compose.runtime.Composable()
    public static final void OfflineStatusIndicator(boolean isOffline, int pendingSyncCount, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Composable that shows a simple offline badge
     */
    @androidx.compose.runtime.Composable()
    public static final void OfflineBadge(boolean isOffline, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}