package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\u001a&\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001a \u0010\u0007\u001a\u00020\u00012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a\u000e\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00040\rH\u0007\u00a8\u0006\u000e"}, d2 = {"EmojiPickerFromJson", "", "onEmojiSelected", "Lkotlin/Function1;", "", "modifier", "Landroidx/compose/ui/Modifier;", "ManageCategoriesScreen", "onBackClick", "Lkotlin/Function0;", "viewModel", "Lcom/example/splitexpenses/ui/viewmodels/CategoriesViewModel;", "loadEmojiListFromAssets", "", "app_debug"})
public final class ManageCategoriesScreenKt {
    
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<java.lang.String> loadEmojiListFromAssets() {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EmojiPickerFromJson(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onEmojiSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ManageCategoriesScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.CategoriesViewModel viewModel) {
    }
}