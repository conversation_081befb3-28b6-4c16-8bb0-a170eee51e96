{"logs": [{"outputFile": "com.example.splitexpenses.test.app-mergeDebugAndroidTestResources-39:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\faac399e6b2ef4972910475a8683353b\\transformed\\core-1.16.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,407,511,614,712,2094", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "198,300,402,506,609,707,821,2190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\af30d809214462b80fbfe822607332fe\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,282,372,469,557,638,731,819,905,988,1073,1148,1226,1300,1373,1448,1514", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,77,73,72,74,65,116", "endOffsets": "194,277,367,464,552,633,726,814,900,983,1068,1143,1221,1295,1368,1443,1509,1626"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "826,920,1003,1093,1190,1278,1359,1452,1540,1626,1709,1794,1869,1947,2021,2195,2270,2336", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,77,73,72,74,65,116", "endOffsets": "915,998,1088,1185,1273,1354,1447,1535,1621,1704,1789,1864,1942,2016,2089,2265,2331,2448"}}]}]}