package com.example.splitexpenses.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.app.ShareCompat

/**
 * Utility class for handling group invitation links
 */
object InvitationLinkUtil {

    // Using a more compatible approach with http scheme
    // This will still be handled by our app due to the intent filter
    private const val SCHEME = "https"
    private const val HOST = "splitexpenses.example.com"
    private const val PATH = "join"

    /**
     * Generate an invitation link for a group
     * @param groupId The ID of the group
     * @return A URI string for the invitation link
     */
    fun generateInvitationLink(groupId: String): String {
        return "$SCHEME://$HOST/$PATH?groupId=$groupId"
    }

    /**
     * Extract the group ID from an invitation link
     * @param uri The URI from the deep link
     * @return The group ID or null if the URI is invalid
     */
    fun extractGroupId(uri: Uri?): String? {
        if (uri == null) return null

        // Check if this is a valid invitation link
        if (uri.scheme != SCHEME || uri.host != HOST) {
            // Try the old format as fallback
            if (uri.scheme == "splitexpenses" && uri.host == "join") {
                val path = uri.path ?: return null
                if (path.isEmpty() || path == "/") return null
                return path.substring(1)
            }
            return null
        }

        // Check if the path is correct
        if (uri.path != "/$PATH") return null

        // Extract the group ID from the query parameter
        return uri.getQueryParameter("groupId")
    }

    /**
     * Share an invitation link using the Android share sheet
     * @param context The context to use for sharing
     * @param groupId The ID of the group to share
     * @param groupName The name of the group (for the share message)
     */
    fun shareInvitationLink(context: Context, groupId: String, groupName: String) {
        println("InvitationLinkUtil: shareInvitationLink called with groupId=$groupId, groupName=$groupName")
        try {
            val link = generateInvitationLink(groupId)
            println("InvitationLinkUtil: Generated link: $link")

//            val shareText = "Join my expense group \"$groupName\" in SplitExpenses: $link\n\n" +
//                    "If the link doesn't work, you can manually join using this group ID: $groupId"
            val shareText = groupId

            println("InvitationLinkUtil: Share text created")

            println("InvitationLinkUtil: Creating share intent with context: $context")
            val shareIntent = ShareCompat.IntentBuilder(context)
                .setText(shareText)
                .setType("text/plain")
                .createChooserIntent()
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            println("InvitationLinkUtil: Share intent created")

            println("InvitationLinkUtil: Starting activity with intent")
            context.startActivity(shareIntent)
            println("InvitationLinkUtil: Activity started successfully")
        } catch (e: Exception) {
            println("InvitationLinkUtil: Error in shareInvitationLink: ${e.message}")
            e.printStackTrace()
        }
    }
}
