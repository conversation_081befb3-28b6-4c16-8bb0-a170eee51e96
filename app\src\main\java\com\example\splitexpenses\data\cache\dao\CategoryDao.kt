package com.example.splitexpenses.data.cache.dao

import androidx.room.*
import com.example.splitexpenses.data.cache.entities.CategoryEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Category entities
 */
@Dao
interface CategoryDao {

    /**
     * Get all categories for a specific group as Flow
     */
    @Query("SELECT * FROM categories WHERE groupId = :groupId ORDER BY name ASC")
    fun getCategoriesForGroupFlow(groupId: String): Flow<List<CategoryEntity>>

    /**
     * Get all categories for a specific group
     */
    @Query("SELECT * FROM categories WHERE groupId = :groupId ORDER BY name ASC")
    suspend fun getCategoriesForGroup(groupId: String): List<CategoryEntity>

    /**
     * Get a specific category by ID
     */
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    suspend fun getCategoryById(categoryId: String): CategoryEntity?

    /**
     * Get categories that are not synced
     */
    @Query("SELECT * FROM categories WHERE isSynced = 0")
    suspend fun getUnsyncedCategories(): List<CategoryEntity>

    /**
     * Get unsynced categories for a specific group
     */
    @Query("SELECT * FROM categories WHERE groupId = :groupId AND isSynced = 0")
    suspend fun getUnsyncedCategoriesForGroup(groupId: String): List<CategoryEntity>

    /**
     * Insert or replace a category
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategory(category: CategoryEntity)

    /**
     * Insert or replace multiple categories
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategories(categories: List<CategoryEntity>)

    /**
     * Update a category
     */
    @Update
    suspend fun updateCategory(category: CategoryEntity)

    /**
     * Delete a category
     */
    @Delete
    suspend fun deleteCategory(category: CategoryEntity)

    /**
     * Delete a category by ID
     */
    @Query("DELETE FROM categories WHERE id = :categoryId")
    suspend fun deleteCategoryById(categoryId: String)

    /**
     * Delete all categories for a group
     */
    @Query("DELETE FROM categories WHERE groupId = :groupId")
    suspend fun deleteCategoriesForGroup(groupId: String)

    /**
     * Mark a category as synced
     */
    @Query("UPDATE categories SET isSynced = 1 WHERE id = :categoryId")
    suspend fun markCategoryAsSynced(categoryId: String)

    /**
     * Mark a category as unsynced
     */
    @Query("UPDATE categories SET isSynced = 0, lastModified = :timestamp WHERE id = :categoryId")
    suspend fun markCategoryAsUnsynced(categoryId: String, timestamp: Long = System.currentTimeMillis())

    /**
     * Clear all categories
     */
    @Query("DELETE FROM categories")
    suspend fun clearAllCategories()
}
