package com.example.splitexpenses.util

import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import kotlin.math.absoluteValue

import android.util.Log

import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.getDefaultCategories

/**
 * Utility class for handling CSV export and import operations
 */
object CsvUtil {
    // Tag for logging
    private const val TAG = "CsvUtil"

    // CSV format constants
    private const val CSV_DELIMITER_COMMA = ","
    private const val CSV_DELIMITER_SEMICOLON = ";"
    private const val CSV_QUOTE = "\""
    private const val CSV_ESCAPE = "\\"
    private const val CSV_NEWLINE = "\n"

    // Date format for CSV export/import
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    // CSV Headers - excluding internal Firebase fields (memberUidMap, creatorUid, allowedUsers)
    private val GROUP_HEADER = listOf("name", "members", "memberAvatars", "categories")
    private val EXPENSE_HEADER = listOf(
        "amount", "description", "paidBy",
        "splitBetween", "category", "date", "isCategoryLocked"
    )

    /**
     * Detects the delimiter used in a CSV line by analyzing the structure and content
     * @param line The CSV line to analyze
     * @return The detected delimiter (comma or semicolon)
     */
    private fun detectDelimiter(line: String): String {
        Log.d(TAG, "Detecting delimiter for line: '$line'")

        // Quick check: if line contains semicolons but no commas, it's clearly semicolon-delimited
        val containsSemicolons = line.contains(";")
        val containsCommas = line.contains(",")

        if (containsSemicolons && !containsCommas) {
            Log.d(TAG, "Line contains semicolons but no commas, using semicolon")
            return CSV_DELIMITER_SEMICOLON
        }

        if (containsCommas && !containsSemicolons) {
            Log.d(TAG, "Line contains commas but no semicolons, using comma")
            return CSV_DELIMITER_COMMA
        }

        // More detailed analysis for mixed or unclear cases
        val expectedGroupFields = listOf("name", "members", "memberAvatars", "categories")

        // Try comma first
        val commaFields = line.split(CSV_DELIMITER_COMMA).map { it.trim() }
        val commaFieldsClean = commaFields.dropLastWhile { it.isBlank() }

        // Try semicolon
        val semicolonFields = line.split(CSV_DELIMITER_SEMICOLON).map { it.trim() }
        val semicolonFieldsClean = semicolonFields.dropLastWhile { it.isBlank() }

        Log.d(TAG, "Comma fields (${commaFields.size} total, ${commaFieldsClean.size} non-empty): $commaFieldsClean")
        Log.d(TAG, "Semicolon fields (${semicolonFields.size} total, ${semicolonFieldsClean.size} non-empty): $semicolonFieldsClean")

        // Check if comma parsing produces the expected header structure
        val commaMatchesHeader = commaFieldsClean.size >= expectedGroupFields.size &&
                commaFieldsClean.take(expectedGroupFields.size).zip(expectedGroupFields)
                    .all { (actual, expected) -> actual.lowercase() == expected.lowercase() }

        // Check if semicolon parsing produces the expected header structure
        val semicolonMatchesHeader = semicolonFieldsClean.size >= expectedGroupFields.size &&
                semicolonFieldsClean.take(expectedGroupFields.size).zip(expectedGroupFields)
                    .all { (actual, expected) -> actual.lowercase() == expected.lowercase() }

        Log.d(TAG, "Comma matches header: $commaMatchesHeader")
        Log.d(TAG, "Semicolon matches header: $semicolonMatchesHeader")

        val detectedDelimiter = when {
            semicolonMatchesHeader && !commaMatchesHeader -> {
                Log.d(TAG, "Semicolon produces correct header structure")
                CSV_DELIMITER_SEMICOLON
            }
            commaMatchesHeader && !semicolonMatchesHeader -> {
                Log.d(TAG, "Comma produces correct header structure")
                CSV_DELIMITER_COMMA
            }
            semicolonMatchesHeader && commaMatchesHeader -> {
                // Both match, prefer the one with more reasonable field count
                Log.d(TAG, "Both delimiters match header, comparing field counts")
                if (semicolonFields.size > commaFields.size) {
                    Log.d(TAG, "Semicolon produces more fields, using semicolon")
                    CSV_DELIMITER_SEMICOLON
                } else {
                    Log.d(TAG, "Comma produces more or equal fields, using comma")
                    CSV_DELIMITER_COMMA
                }
            }
            else -> {
                // Fall back to field count comparison
                val commaNonEmptyCount = commaFieldsClean.count { it.isNotBlank() }
                val semicolonNonEmptyCount = semicolonFieldsClean.count { it.isNotBlank() }

                Log.d(TAG, "Header structure unclear, comparing field counts: comma=$commaNonEmptyCount, semicolon=$semicolonNonEmptyCount")

                when {
                    semicolonNonEmptyCount > commaNonEmptyCount -> {
                        Log.d(TAG, "Semicolon produces more meaningful fields")
                        CSV_DELIMITER_SEMICOLON
                    }
                    commaNonEmptyCount > semicolonNonEmptyCount -> {
                        Log.d(TAG, "Comma produces more meaningful fields")
                        CSV_DELIMITER_COMMA
                    }
                    else -> {
                        // Final fallback: prefer comma as it's more common
                        Log.d(TAG, "Using comma as default")
                        CSV_DELIMITER_COMMA
                    }
                }
            }
        }

        Log.d(TAG, "Detected delimiter: '$detectedDelimiter'")
        return detectedDelimiter
    }

    /**
     * Parses a CSV line with proper handling of quoted fields and escaped characters
     * @param line The CSV line to parse
     * @param delimiter The delimiter to use
     * @return List of parsed and trimmed fields
     */
    private fun parseCsvLine(line: String, delimiter: String): List<String> {
        val fields = mutableListOf<String>()
        val currentField = StringBuilder()
        var inQuotes = false
        var i = 0

        while (i < line.length) {
            val char = line[i]

            when {
                char == '"' && !inQuotes -> {
                    inQuotes = true
                }
                char == '"' && inQuotes -> {
                    // Check if this is an escaped quote
                    if (i + 1 < line.length && line[i + 1] == '"') {
                        currentField.append('"')
                        i++ // Skip the next quote
                    } else {
                        inQuotes = false
                    }
                }
                char.toString() == delimiter && !inQuotes -> {
                    fields.add(currentField.toString().trim())
                    currentField.clear()
                }
                else -> {
                    currentField.append(char)
                }
            }
            i++
        }

        // Add the last field
        fields.add(currentField.toString().trim())

        return fields
    }

    /**
     * Escapes a string for CSV format
     * @param value The string to escape
     * @param delimiter The delimiter being used
     * @return The escaped string
     */
    private fun escapeForCsv(value: String, delimiter: String = CSV_DELIMITER_COMMA): String {
        // If the value contains a delimiter, quote, or newline, wrap it in quotes and escape quotes
        return if (value.contains(delimiter) || value.contains(CSV_QUOTE) || value.contains(CSV_NEWLINE)) {
            CSV_QUOTE + value.replace(CSV_QUOTE, CSV_QUOTE + CSV_QUOTE) + CSV_QUOTE
        } else {
            value
        }
    }

    /**
     * Normalizes a row to have the expected number of fields by padding with empty strings or truncating
     * @param fields The list of fields to normalize
     * @param expectedSize The expected number of fields
     * @param lineNumber The line number for logging purposes
     * @return The normalized list of fields
     */
    private fun normalizeRowLength(fields: List<String>, expectedSize: Int, lineNumber: Int): List<String> {
        Log.d(TAG, "Normalizing row at line $lineNumber: input size=${fields.size}, expected=$expectedSize, fields=$fields")
        return when {
            fields.size == expectedSize -> {
                Log.d(TAG, "Row at line $lineNumber already has correct size")
                fields
            }
            fields.size < expectedSize -> {
                Log.d(TAG, "Row at line $lineNumber has ${fields.size} fields, padding to $expectedSize with empty strings")
                fields + List(expectedSize - fields.size) { "" }
            }
            else -> {
                // First try to trim trailing empty fields
                val trimmed = trimTrailingEmptyFields(fields, expectedSize)
                Log.d(TAG, "Row at line $lineNumber: trimming result size=${trimmed.first.size}, trimmed=${trimmed.second}")
                if (trimmed.first.size <= expectedSize) {
                    Log.d(TAG, "Row at line $lineNumber had trailing empty fields, trimmed from ${fields.size} to ${trimmed.first.size} fields")
                    normalizeRowLength(trimmed.first, expectedSize, lineNumber) // Recursive call to handle padding if needed
                } else {
                    Log.d(TAG, "Row at line $lineNumber has ${fields.size} fields, truncating to $expectedSize")
                    fields.take(expectedSize)
                }
            }
        }
    }

    /**
     * Trims trailing empty fields from a list of CSV fields
     * @param fields The list of fields to trim
     * @param expectedSize The expected number of fields
     * @return The trimmed list of fields and a boolean indicating if trimming occurred
     */
    private fun trimTrailingEmptyFields(fields: List<String>, expectedSize: Int): Pair<List<String>, Boolean> {
        // If the list is already the expected size or smaller, return it as is
        if (fields.size <= expectedSize) {
            return Pair(fields, false)
        }

        // Find the last non-empty field index
        var lastNonEmptyIndex = fields.size - 1
        while (lastNonEmptyIndex >= 0 && fields[lastNonEmptyIndex].isBlank()) {
            lastNonEmptyIndex--
        }

        // Determine the correct end index
        val endIndex = if (lastNonEmptyIndex < expectedSize - 1) {
            // All meaningful content fits within expected size
            // Trim to just after the last non-empty field
            lastNonEmptyIndex + 1
        } else {
            // Content extends beyond expected size or exactly fills it
            // Trim to expected size to remove trailing empty fields
            expectedSize
        }

        val trimmedFields = fields.subList(0, endIndex)

        // Return the trimmed fields and whether trimming occurred
        return Pair(trimmedFields, trimmedFields.size != fields.size)
    }

    /**
     * Converts a list to a CSV-friendly string
     * @param list The list to convert
     * @param delimiter The delimiter being used
     * @return A string representation of the list
     */
    private fun listToCsvString(list: List<String>, delimiter: String = CSV_DELIMITER_COMMA): String {
        return escapeForCsv(list.joinToString("|"), delimiter)
    }

    /**
     * Converts a CSV string back to a list
     * @param csvString The CSV string to convert
     * @return A list of strings
     */
    private fun csvStringToList(csvString: String): List<String> {
        return if (csvString.isBlank()) {
            emptyList()
        } else {
            csvString.split("|").map { it.trim() }.filter { it.isNotBlank() }
        }
    }

    /**
     * Validates that the export format is compatible with the import function
     * @param group The group to validate
     * @return List of validation issues, empty if valid
     */
    private fun validateExportCompatibility(group: GroupData): List<String> {
        val issues = mutableListOf<String>()

        // Validate group name
        if (group.name.isBlank()) {
            issues.add("Group name is blank")
        }

        // Validate members
        if (group.members.isEmpty()) {
            issues.add("Group has no members")
        } else {
            group.members.forEach { member ->
                if (member.isBlank()) {
                    issues.add("Group contains blank member name")
                }
                if (member.contains("|")) {
                    issues.add("Member name '$member' contains pipe character which conflicts with list separator")
                }
                if (member.contains(",") || member.contains(";")) {
                    issues.add("Member name '$member' contains delimiter characters")
                }
            }
        }

        // Validate member avatars
        group.memberAvatars.forEach { (name, avatar) ->
            if (name.contains("=") || name.contains(";")) {
                issues.add("Member avatar name '$name' contains reserved characters")
            }
            if (avatar.contains("=") || avatar.contains(";")) {
                issues.add("Member avatar '$avatar' contains reserved characters")
            }
        }



        // Validate categories
        group.categories.forEach { category ->
            if (category.name.contains("~") || category.name.contains(";")) {
                issues.add("Category name '${category.name}' contains reserved characters")
            }
            if (category.emoji.contains("~") || category.emoji.contains(";")) {
                issues.add("Category emoji '${category.emoji}' contains reserved characters")
            }
            category.keywords.forEach { keyword ->
                if (keyword.contains("^") || keyword.contains("~") || keyword.contains(";")) {
                    issues.add("Category keyword '$keyword' contains reserved characters")
                }
            }
        }

        // Validate expenses
        group.expenses.forEachIndexed { index, expense ->
            if (expense.description.isBlank()) {
                issues.add("Expense $index has blank description")
            }
            if (expense.paidBy.isBlank()) {
                issues.add("Expense $index has blank paidBy")
            }
            if (expense.splitBetween.isEmpty()) {
                issues.add("Expense $index has empty splitBetween list")
            }
            if (expense.category.isBlank()) {
                issues.add("Expense $index has blank category")
            }
            if (expense.amount < 0) {
                issues.add("Expense $index has negative amount")
            }
        }

        return issues
    }

    /**
     * Tests export/import round-trip compatibility
     * @param group The group to test
     * @return Test result with success status and any issues found
     */
    fun testExportImportRoundTrip(group: GroupData): CsvRoundTripTestResult {
        return try {
            Log.d(TAG, "Starting export/import round-trip test for group: ${group.name}")

            // Export to byte array
            val outputStream = ByteArrayOutputStream()
            val exportSuccess = exportGroupToCsv(group, outputStream)

            if (!exportSuccess) {
                return CsvRoundTripTestResult(
                    success = false,
                    error = "Export failed",
                    exportedCsv = null,
                    importResult = null
                )
            }

            val exportedCsv = outputStream.toString()
            Log.d(TAG, "Exported CSV:\n$exportedCsv")

            // Import back from byte array
            val inputStream = ByteArrayInputStream(exportedCsv.toByteArray())
            val importResult = importGroupFromCsv(inputStream)

            if (!importResult.success) {
                return CsvRoundTripTestResult(
                    success = false,
                    error = "Import failed: ${importResult.errors.joinToString("; ") { it.message }}",
                    exportedCsv = exportedCsv,
                    importResult = importResult
                )
            }

            // Compare original and imported data
            val importedGroup = importResult.group!!
            val comparisonIssues = compareGroups(group, importedGroup)

            if (comparisonIssues.isNotEmpty()) {
                return CsvRoundTripTestResult(
                    success = false,
                    error = "Data mismatch after round-trip: ${comparisonIssues.joinToString("; ")}",
                    exportedCsv = exportedCsv,
                    importResult = importResult
                )
            }

            Log.d(TAG, "Round-trip test completed successfully")
            CsvRoundTripTestResult(
                success = true,
                error = null,
                exportedCsv = exportedCsv,
                importResult = importResult
            )

        } catch (e: Exception) {
            Log.e(TAG, "Round-trip test failed with exception", e)
            CsvRoundTripTestResult(
                success = false,
                error = "Exception during test: ${e.message}",
                exportedCsv = null,
                importResult = null
            )
        }
    }

    /**
     * Compares two groups for data integrity
     * @param original The original group
     * @param imported The imported group
     * @return List of differences found
     */
    private fun compareGroups(original: GroupData, imported: GroupData): List<String> {
        val differences = mutableListOf<String>()

        // Compare basic properties
        if (original.name != imported.name) {
            differences.add("Group name differs: '${original.name}' vs '${imported.name}'")
        }

        // Compare members
        if (original.members.toSet() != imported.members.toSet()) {
            differences.add("Members differ: ${original.members} vs ${imported.members}")
        }

        // Compare member avatars
        if (original.memberAvatars != imported.memberAvatars) {
            differences.add("Member avatars differ: ${original.memberAvatars} vs ${imported.memberAvatars}")
        }

        // Compare categories (by name and emoji, keywords may vary)
        val originalCategoryMap = original.categories.associateBy { "${it.name}~${it.emoji}" }
        val importedCategoryMap = imported.categories.associateBy { "${it.name}~${it.emoji}" }

        if (originalCategoryMap.keys != importedCategoryMap.keys) {
            differences.add("Categories differ: ${originalCategoryMap.keys} vs ${importedCategoryMap.keys}")
        }

        // Note: User management fields (memberUidMap, creatorUid, allowedUsers) are not exported/imported
        // as they are internal Firebase fields managed by the repository

        // Compare expenses
        if (original.expenses.size != imported.expenses.size) {
            differences.add("Expense count differs: ${original.expenses.size} vs ${imported.expenses.size}")
        } else {
            original.expenses.zip(imported.expenses).forEachIndexed { index, (orig, imp) ->
                if (orig.amount != imp.amount) {
                    differences.add("Expense $index amount differs: ${orig.amount} vs ${imp.amount}")
                }
                if (orig.description != imp.description) {
                    differences.add("Expense $index description differs: '${orig.description}' vs '${imp.description}'")
                }
                if (orig.paidBy != imp.paidBy) {
                    differences.add("Expense $index paidBy differs: '${orig.paidBy}' vs '${imp.paidBy}'")
                }
                if (orig.splitBetween.toSet() != imp.splitBetween.toSet()) {
                    differences.add("Expense $index splitBetween differs: ${orig.splitBetween} vs ${imp.splitBetween}")
                }
                if (orig.category != imp.category) {
                    differences.add("Expense $index category differs: '${orig.category}' vs '${imp.category}'")
                }
                if (orig.isCategoryLocked != imp.isCategoryLocked) {
                    differences.add("Expense $index isCategoryLocked differs: ${orig.isCategoryLocked} vs ${imp.isCategoryLocked}")
                }
                // Note: We don't compare dates exactly as they may have formatting differences
            }
        }

        return differences
    }

    /**
     * Data class for round-trip test results
     */
    data class CsvRoundTripTestResult(
        val success: Boolean,
        val error: String?,
        val exportedCsv: String?,
        val importResult: CsvImportResult?
    )

    /**
     * Exports a group to CSV format with standardized structure and consistent formatting
     * @param group The group to export
     * @param outputStream The output stream to write to
     * @return True if export was successful, false otherwise
     */
    fun exportGroupToCsv(group: GroupData, outputStream: OutputStream): Boolean {
        return try {
            val delimiter = CSV_DELIMITER_COMMA
            Log.d(TAG, "Starting CSV export for group: ${group.name}")

            // Validate group data before export
            val validationIssues = validateExportCompatibility(group)
            if (validationIssues.isNotEmpty()) {
                Log.w(TAG, "Export validation issues found: ${validationIssues.joinToString(", ")}")
                // Continue with export but log warnings
            }

            // Write group header with exact expected format
            val groupHeaderLine = GROUP_HEADER.joinToString(delimiter)
            outputStream.write("$groupHeaderLine$CSV_NEWLINE".toByteArray())
            Log.d(TAG, "Written group header: $groupHeaderLine")

            // Prepare group data fields with consistent formatting
            val groupName = if (group.name.isNotBlank()) group.name else "Exported Group"

            // Convert members list to standardized format
            val membersString = if (group.members.isNotEmpty()) {
                group.members.joinToString("|")
            } else {
                "" // Empty string for no members
            }

            // Convert memberAvatars map to standardized format
            val memberAvatarsString = if (group.memberAvatars.isNotEmpty()) {
                group.memberAvatars.entries
                    .sortedBy { it.key } // Sort for consistency
                    .joinToString(";") { "${it.key}=${it.value}" }
            } else {
                "" // Empty string for no avatars
            }

            // Convert categories to standardized format
            val categoriesString = if (group.categories.isNotEmpty()) {
                group.categories.joinToString(";") { category ->
                    val keywordsString = if (category.keywords.isNotEmpty()) {
                        category.keywords.joinToString("^")
                    } else {
                        "" // Empty string for no keywords
                    }
                    "${category.name}~${category.emoji}~$keywordsString"
                }
            } else {
                "" // Empty string for no categories
            }

            // Write group data with all fields present (no missing columns)
            val groupDataFields = listOf(
                escapeForCsv(groupName, delimiter),
                escapeForCsv(membersString, delimiter),
                escapeForCsv(memberAvatarsString, delimiter),
                escapeForCsv(categoriesString, delimiter)
            )

            // Ensure we have exactly the expected number of fields
            if (groupDataFields.size != GROUP_HEADER.size) {
                Log.e(TAG, "Group data field count mismatch: expected ${GROUP_HEADER.size}, got ${groupDataFields.size}")
                return false
            }

            val groupDataLine = groupDataFields.joinToString(delimiter)
            outputStream.write("$groupDataLine$CSV_NEWLINE".toByteArray())
            Log.d(TAG, "Written group data: $groupDataLine")

            // Write expense header with exact expected format
            val expenseHeaderLine = EXPENSE_HEADER.joinToString(delimiter)
            outputStream.write("$expenseHeaderLine$CSV_NEWLINE".toByteArray())
            Log.d(TAG, "Written expense header: $expenseHeaderLine")

            // Write expense data with consistent field structure
            group.expenses.forEachIndexed { index, expense ->
                val expenseFields = listOf(
                    // Amount - always format as decimal number
                    String.format(Locale.US, "%.2f", expense.amount),
                    // Description - always present, escape properly
                    escapeForCsv(expense.description.ifBlank { "Expense" }, delimiter),
                    // PaidBy - always present, escape properly
                    escapeForCsv(expense.paidBy.ifBlank { "Unknown" }, delimiter),
                    // SplitBetween - always present, use consistent format
                    escapeForCsv(
                        if (expense.splitBetween.isNotEmpty()) {
                            expense.splitBetween.joinToString("|")
                        } else {
                            expense.paidBy // Default to paidBy if splitBetween is empty
                        },
                        delimiter
                    ),
                    // Category - always present
                    escapeForCsv(expense.category.ifBlank { "None" }, delimiter),
                    // Date - always present in consistent format
                    dateFormat.format(Date(expense.date)),
                    // IsCategoryLocked - always present as boolean string
                    expense.isCategoryLocked.toString()
                )

                // Ensure we have exactly the expected number of fields
                if (expenseFields.size != EXPENSE_HEADER.size) {
                    Log.e(TAG, "Expense data field count mismatch at index $index: expected ${EXPENSE_HEADER.size}, got ${expenseFields.size}")
                    return false
                }

                val expenseLine = expenseFields.joinToString(delimiter)
                outputStream.write("$expenseLine$CSV_NEWLINE".toByteArray())

                if (index < 3) { // Log first few expenses for debugging
                    Log.d(TAG, "Written expense $index: $expenseLine")
                }
            }

            Log.d(TAG, "CSV export completed successfully. Exported ${group.expenses.size} expenses")
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        } finally {
            try {
                outputStream.close()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * Imports a group from CSV format with detailed error handling and logging
     * @param inputStream The input stream to read from
     * @param currentUser The current user's name (optional, will use from CSV if not provided)
     * @return CsvImportResult containing the import results, errors, and warnings
     */
    fun importGroupFromCsv(inputStream: InputStream, currentUser: String? = null): CsvImportResult {
        Log.d(TAG, "Starting CSV import process")

        val errors = mutableListOf<CsvImportError>()
        val warnings = mutableListOf<CsvImportWarning>()
        var totalRowsProcessed = 0
        var successfulRows = 0

        try {
            // Read all lines from the input stream
            Log.d(TAG, "Reading lines from input stream")
            val lines = inputStream.bufferedReader().readLines()
                .filter { it.isNotBlank() } // Remove completely empty lines

            // Validate minimum required lines
            if (lines.size < 4) {
                val error = CsvImportError(
                    message = "CSV file must contain at least 4 non-empty lines:\n" +
                            "1. Group header (${GROUP_HEADER.joinToString(",")})\n" +
                            "2. Group data\n" +
                            "3. Expense header (${EXPENSE_HEADER.joinToString(",")})\n" +
                            "4. At least one expense\n" +
                            "Found: ${lines.size} lines.",
                    lineNumber = lines.size
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}")
                return CsvImportResult(success = false, errors = errors)
            }

            // Detect delimiter from the first line
            val delimiter = detectDelimiter(lines[0])
            Log.d(TAG, "Detected delimiter: '$delimiter'")

            // Parse group header with robust parsing
            Log.d(TAG, "Parsing group header with delimiter '$delimiter'")
            Log.d(TAG, "Raw header line: '${lines[0]}'")
            val rawGroupHeader = try {
                val parsed = parseCsvLine(lines[0], delimiter)
                Log.d(TAG, "Parsed header fields: $parsed")
                parsed
            } catch (e: Exception) {
                val error = CsvImportError(
                    message = "Failed to parse group header: ${e.message}",
                    lineNumber = 1,
                    exception = e
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}", e)
                return CsvImportResult(success = false, errors = errors)
            }

            // Normalize group header length
            val groupHeader = normalizeRowLength(rawGroupHeader, GROUP_HEADER.size, 1)

            // Validate group header with better error messages
            val headerMismatches = mutableListOf<String>()
            for (i in GROUP_HEADER.indices) {
                if (i >= groupHeader.size) {
                    headerMismatches.add("Missing field '${GROUP_HEADER[i]}' at position ${i + 1}")
                } else {
                    val expectedField = GROUP_HEADER[i].lowercase()
                    val actualField = groupHeader[i].trim().lowercase()
                    if (actualField != expectedField) {
                        headerMismatches.add("Expected '${GROUP_HEADER[i]}' at position ${i + 1}, found '${groupHeader[i].trim()}'")
                    }
                }
            }

            if (headerMismatches.isNotEmpty()) {
                val error = CsvImportError(
                    message = "Invalid group header format:\n${headerMismatches.joinToString("\n")}\n\nExpected: ${GROUP_HEADER.joinToString(delimiter)}\nFound: ${rawGroupHeader.joinToString(delimiter)}\n\nAfter normalization: ${groupHeader.joinToString(delimiter)}",
                    lineNumber = 1
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}")
                Log.e(TAG, "Raw parsed header had ${rawGroupHeader.size} fields: ${rawGroupHeader}")
                Log.e(TAG, "Normalized header has ${groupHeader.size} fields: ${groupHeader}")
                return CsvImportResult(success = false, errors = errors)
            }

            // Parse group data
            Log.d(TAG, "Parsing group data")
            val rawGroupData = try {
                parseCsvLine(lines[1], delimiter)
            } catch (e: Exception) {
                val error = CsvImportError(
                    message = "Failed to parse group data: ${e.message}",
                    lineNumber = 2,
                    exception = e
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}", e)
                return CsvImportResult(success = false, errors = errors)
            }

            // Normalize group data length
            val groupData = normalizeRowLength(rawGroupData, GROUP_HEADER.size, 2)

            // Extract group fields
            val groupId = UUID.randomUUID().toString() // Generate new ID for imported group

            // Extract and validate group name
            val groupName = if (groupData[0].isNotBlank()) {
                groupData[0]
            } else {
                val warning = CsvImportWarning(
                    message = "Group name is empty, using 'Imported Group'",
                    lineNumber = 2,
                    fieldName = "name",
                    fieldValue = "",
                    fixedValue = "Imported Group"
                )
                warnings.add(warning)
                Log.w(TAG, warning.toString())
                "Imported Group"
            }

            // Extract and validate members list
            var members = try {
                val membersList = csvStringToList(groupData[1])
                if (membersList.isEmpty()) {
                    val warning = CsvImportWarning(
                        message = "Members list is empty",
                        lineNumber = 2,
                        fieldName = "members"
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString())
                }
                membersList
            } catch (e: Exception) {
                val warning = CsvImportWarning(
                    message = "Failed to parse members list, using empty list",
                    lineNumber = 2,
                    fieldName = "members",
                    fieldValue = groupData[1],
                    fixedValue = "[]"
                )
                warnings.add(warning)
                Log.w(TAG, warning.toString(), e)
                emptyList()
            }

            // Current user is now handled client-side, not in the CSV

            // Parse expense header
            Log.d(TAG, "Parsing expense header")
            val rawExpenseHeader = try {
                parseCsvLine(lines[2], delimiter)
            } catch (e: Exception) {
                val error = CsvImportError(
                    message = "Failed to parse expense header: ${e.message}",
                    lineNumber = 3,
                    exception = e
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}", e)
                return CsvImportResult(success = false, errors = errors)
            }

            // Normalize expense header length (minimum 5 required fields)
            val minRequiredFields = 5
            val expenseHeader = normalizeRowLength(rawExpenseHeader, EXPENSE_HEADER.size, 3)

            // Validate expense header with better error messages - the first 5 fields are required
            val requiredExpenseHeader = EXPENSE_HEADER.take(minRequiredFields)
            val expenseHeaderMismatches = mutableListOf<String>()

            for (i in requiredExpenseHeader.indices) {
                if (i >= expenseHeader.size) {
                    expenseHeaderMismatches.add("Missing required field '${requiredExpenseHeader[i]}' at position ${i + 1}")
                } else {
                    val expectedField = requiredExpenseHeader[i].lowercase()
                    val actualField = expenseHeader[i].trim().lowercase()
                    if (actualField != expectedField) {
                        expenseHeaderMismatches.add("Expected '${requiredExpenseHeader[i]}' at position ${i + 1}, found '${expenseHeader[i].trim()}'")
                    }
                }
            }

            if (expenseHeaderMismatches.isNotEmpty()) {
                val error = CsvImportError(
                    message = "Invalid expense header format:\n${expenseHeaderMismatches.joinToString("\n")}\n\nRequired fields: ${requiredExpenseHeader.joinToString(delimiter)}\nFound: ${expenseHeader.take(minRequiredFields).joinToString(delimiter)}",
                    lineNumber = 3
                )
                errors.add(error)
                Log.e(TAG, "Import failed: ${error.message}")
                return CsvImportResult(success = false, errors = errors)
            }

            // Check optional fields and log differences
            if (expenseHeader.size >= 6 && expenseHeader[5].trim().lowercase() != EXPENSE_HEADER[5].lowercase()) {
                Log.d(TAG, "Date field has different name in header: '${expenseHeader[5]}' instead of '${EXPENSE_HEADER[5]}', will still try to parse it")
            }
            if (expenseHeader.size >= 7 && expenseHeader[6].trim().lowercase() != EXPENSE_HEADER[6].lowercase()) {
                Log.d(TAG, "Category locked field has different name in header: '${expenseHeader[6]}' instead of '${EXPENSE_HEADER[6]}', will still try to parse it")
            }

            // Parse expense data
            Log.d(TAG, "Parsing expense data")
            val expenses = mutableListOf<Expense>()

            for (i in 3 until lines.size) {
                totalRowsProcessed++
                val lineNumber = i + 1 // 1-based line number for error messages

                val rawExpenseData = try {
                    parseCsvLine(lines[i], delimiter)
                } catch (e: Exception) {
                    val warning = CsvImportWarning(
                        message = "Failed to parse expense data, skipping line: ${e.message}",
                        lineNumber = lineNumber
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString(), e)
                    continue
                }

                // Normalize expense data length
                val expenseData = normalizeRowLength(rawExpenseData, EXPENSE_HEADER.size, lineNumber)

                // Validate expense data has enough fields for the essential data (amount, description, paidBy, splitBetween, category)
                // Date field (index 5) is optional and will be handled separately
                if (expenseData.size < 5) {
                    val warning = CsvImportWarning(
                        message = "Expense data has fewer fields than required. Required: 5 (amount, description, paidBy, splitBetween, category), Found: ${expenseData.size}, skipping line",
                        lineNumber = lineNumber
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString())
                    continue
                }

                try {
                    // Generate new ID for imported expense
                    val expenseId = UUID.randomUUID().toString()

                    // Parse amount with enhanced validation
                    val amount = try {
                        val amountStr = expenseData[0].trim()

                        // Handle empty amount
                        if (amountStr.isBlank()) {
                            val warning = CsvImportWarning(
                                message = "Amount field is empty, using 0.0",
                                lineNumber = lineNumber,
                                fieldName = "amount",
                                fieldValue = amountStr,
                                fixedValue = "0.0"
                            )
                            warnings.add(warning)
                            Log.w(TAG, warning.toString())
                            0.0
                        } else {
                            // Try to parse as double, handling common formatting issues
                            val cleanedAmount = amountStr
                                .replace(",", ".") // Handle comma as decimal separator
                                .replace("$", "") // Remove currency symbols
                                .replace("€", "")
                                .replace("£", "")
                                .replace("¥", "")
                                .replace("₹", "")
                                .replace(" ", "") // Remove spaces
                                .trim()

                            val parsedAmount = cleanedAmount.toDoubleOrNull()

                            when {
                                parsedAmount == null -> {
                                    val warning = CsvImportWarning(
                                        message = "Invalid amount format '$amountStr', using 0.0",
                                        lineNumber = lineNumber,
                                        fieldName = "amount",
                                        fieldValue = amountStr,
                                        fixedValue = "0.0"
                                    )
                                    warnings.add(warning)
                                    Log.w(TAG, warning.toString())
                                    0.0
                                }
                                parsedAmount < 0 -> {
                                    val warning = CsvImportWarning(
                                        message = "Negative amount '$amountStr', using absolute value",
                                        lineNumber = lineNumber,
                                        fieldName = "amount",
                                        fieldValue = amountStr,
                                        fixedValue = parsedAmount.absoluteValue.toString()
                                    )
                                    warnings.add(warning)
                                    Log.w(TAG, warning.toString())
                                    parsedAmount.absoluteValue
                                }
                                parsedAmount.isNaN() || parsedAmount.isInfinite() -> {
                                    val warning = CsvImportWarning(
                                        message = "Invalid amount value '$amountStr', using 0.0",
                                        lineNumber = lineNumber,
                                        fieldName = "amount",
                                        fieldValue = amountStr,
                                        fixedValue = "0.0"
                                    )
                                    warnings.add(warning)
                                    Log.w(TAG, warning.toString())
                                    0.0
                                }
                                else -> parsedAmount
                            }
                        }
                    } catch (e: Exception) {
                        val warning = CsvImportWarning(
                            message = "Failed to parse amount '${expenseData[0]}': ${e.message}, using 0.0",
                            lineNumber = lineNumber,
                            fieldName = "amount",
                            fieldValue = expenseData[0],
                            fixedValue = "0.0"
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString(), e)
                        0.0
                    }

                    // Parse description with validation
                    val description = expenseData[1].trim().let { desc ->
                        if (desc.isNotBlank()) {
                            // Validate description length and content
                            when {
                                desc.length > 200 -> {
                                    val truncated = desc.take(200)
                                    val warning = CsvImportWarning(
                                        message = "Description too long (${desc.length} chars), truncated to 200 characters",
                                        lineNumber = lineNumber,
                                        fieldName = "description",
                                        fieldValue = desc,
                                        fixedValue = truncated
                                    )
                                    warnings.add(warning)
                                    Log.w(TAG, warning.toString())
                                    truncated
                                }
                                else -> desc
                            }
                        } else {
                            val warning = CsvImportWarning(
                                message = "Description is empty, using 'Imported Expense'",
                                lineNumber = lineNumber,
                                fieldName = "description",
                                fieldValue = desc,
                                fixedValue = "Imported Expense"
                            )
                            warnings.add(warning)
                            Log.w(TAG, warning.toString())
                            "Imported Expense"
                        }
                    }

                    // Parse paidBy with validation
                    val paidBy = expenseData[2].trim().let { payer ->
                        if (payer.isNotBlank()) {
                            // Validate payer name
                            val validatedPayer = when {
                                payer.length > 50 -> {
                                    val truncated = payer.take(50)
                                    val warning = CsvImportWarning(
                                        message = "Payer name too long (${payer.length} chars), truncated to 50 characters",
                                        lineNumber = lineNumber,
                                        fieldName = "paidBy",
                                        fieldValue = payer,
                                        fixedValue = truncated
                                    )
                                    warnings.add(warning)
                                    Log.w(TAG, warning.toString())
                                    truncated
                                }
                                else -> payer
                            }

                            // If paidBy is not in the members list, add them automatically
                            if (!members.contains(validatedPayer) && members.isNotEmpty()) {
                                Log.d(TAG, "Adding user '$validatedPayer' from paidBy field to members list at line $lineNumber")
                                // Use a mutable list to add the new member
                                members = members.toMutableList().apply { add(validatedPayer) }
                            }
                            validatedPayer
                        } else {
                            // Default to current user if paidBy is empty
                            val defaultUser = currentUser ?: members.firstOrNull() ?: "User"
                            val warning = CsvImportWarning(
                                message = "Paid by is empty, using '$defaultUser'",
                                lineNumber = lineNumber,
                                fieldName = "paidBy",
                                fieldValue = payer,
                                fixedValue = defaultUser
                            )
                            warnings.add(warning)
                            Log.w(TAG, warning.toString())
                            defaultUser
                        }
                    }

                    // Parse splitBetween with enhanced validation
                    val splitBetween = try {
                        val splitStr = expenseData[3].trim()
                        val splitList = if (splitStr.isNotBlank()) {
                            csvStringToList(splitStr)
                                .map { it.trim() }
                                .filter { it.isNotBlank() }
                                .map { name ->
                                    // Validate and truncate member names if needed
                                    if (name.length > 50) {
                                        val truncated = name.take(50)
                                        val warning = CsvImportWarning(
                                            message = "Member name in splitBetween too long (${name.length} chars), truncated to 50 characters",
                                            lineNumber = lineNumber,
                                            fieldName = "splitBetween",
                                            fieldValue = name,
                                            fixedValue = truncated
                                        )
                                        warnings.add(warning)
                                        Log.w(TAG, warning.toString())
                                        truncated
                                    } else {
                                        name
                                    }
                                }
                                .distinct() // Remove duplicates
                        } else {
                            emptyList()
                        }

                        if (splitList.isEmpty()) {
                            // Default to all members if splitBetween is empty
                            val defaultSplit = if (members.isNotEmpty()) members else listOf(paidBy)
                            val warning = CsvImportWarning(
                                message = "Split between is empty, using all members",
                                lineNumber = lineNumber,
                                fieldName = "splitBetween",
                                fieldValue = splitStr,
                                fixedValue = defaultSplit.joinToString("|")
                            )
                            warnings.add(warning)
                            Log.w(TAG, warning.toString())
                            defaultSplit
                        } else {
                            // Add any users in splitBetween that are not in the members list
                            if (members.isNotEmpty()) {
                                val newMembers = splitList.filter { !members.contains(it) }
                                if (newMembers.isNotEmpty()) {
                                    Log.d(TAG, "Adding users from splitBetween field to members list at line $lineNumber: ${newMembers.joinToString(", ")}")
                                    // Use a mutable list to add the new members
                                    members = members.toMutableList().apply { addAll(newMembers) }
                                }
                            }
                            splitList
                        }
                    } catch (e: Exception) {
                        // Default to all members if splitBetween parsing fails
                        val defaultSplit = if (members.isNotEmpty()) members else listOf(paidBy)
                        val warning = CsvImportWarning(
                            message = "Failed to parse split between: ${e.message}, using all members",
                            lineNumber = lineNumber,
                            fieldName = "splitBetween",
                            fieldValue = expenseData[3],
                            fixedValue = defaultSplit.joinToString("|")
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString(), e)
                        defaultSplit
                    }

                    // Parse category with enhanced validation
                    val category = expenseData[4].trim().let { cat ->
                        if (cat.isNotBlank()) {
                            // Validate category name length
                            val validatedCategory = if (cat.length > 50) {
                                val truncated = cat.take(50)
                                val warning = CsvImportWarning(
                                    message = "Category name too long (${cat.length} chars), truncated to 50 characters",
                                    lineNumber = lineNumber,
                                    fieldName = "category",
                                    fieldValue = cat,
                                    fixedValue = truncated
                                )
                                warnings.add(warning)
                                Log.w(TAG, warning.toString())
                                truncated
                            } else {
                                cat
                            }

                            // Check if the category exists in the predefined categories
                            val defaultCategories = getDefaultCategories()
                            val categoryExists = defaultCategories.any { it.name.equals(validatedCategory, ignoreCase = true) }

                            if (!categoryExists) {
                                // If category doesn't exist, use "Other" instead
                                val warning = CsvImportWarning(
                                    message = "Unknown category '$validatedCategory', using 'Other' instead",
                                    lineNumber = lineNumber,
                                    fieldName = "category",
                                    fieldValue = validatedCategory,
                                    fixedValue = "Other"
                                )
                                warnings.add(warning)
                                Log.w(TAG, warning.toString())
                                "Other"
                            } else {
                                // Find the exact case match from default categories
                                defaultCategories.find { it.name.equals(validatedCategory, ignoreCase = true) }?.name ?: validatedCategory
                            }
                        } else {
                            val warning = CsvImportWarning(
                                message = "Category is empty, using 'None'",
                                lineNumber = lineNumber,
                                fieldName = "category",
                                fieldValue = cat,
                                fixedValue = "None"
                            )
                            warnings.add(warning)
                            Log.w(TAG, warning.toString())
                            "None"
                        }
                    }

                    // Parse date with enhanced validation and multiple format support
                    val date = try {
                        // Check if the expense data has enough fields to include the date
                        if (expenseData.size > 5) {
                            val dateStr = expenseData[5].trim()
                            if (dateStr.isNotBlank()) {
                                // Try multiple date formats
                                val dateFormats = listOf(
                                    SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()),
                                    SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()),
                                    SimpleDateFormat("MM/dd/yyyy", Locale.getDefault()),
                                    SimpleDateFormat("dd-MM-yyyy", Locale.getDefault()),
                                    SimpleDateFormat("dd-MM-yy", Locale.getDefault()),  // Added for DD-MM-YY format
                                    SimpleDateFormat("yyyy/MM/dd", Locale.getDefault()),
                                    SimpleDateFormat("dd.MM.yyyy", Locale.getDefault()),
                                    SimpleDateFormat("yyyy.MM.dd", Locale.getDefault()),
                                    SimpleDateFormat("dd.MM.yy", Locale.getDefault())   // Added for DD.MM.YY format
                                )

                                var parsedDate: Long? = null
                                var usedFormat: String? = null

                                for (format in dateFormats) {
                                    try {
                                        val parsed = format.parse(dateStr)
                                        if (parsed != null) {
                                            parsedDate = parsed.time
                                            usedFormat = format.toPattern()
                                            break
                                        }
                                    } catch (e: ParseException) {
                                        // Continue to next format
                                    }
                                }

                                if (parsedDate != null) {
                                    // Validate that the date is reasonable (not too far in the past or future)
                                    val currentTime = System.currentTimeMillis()
                                    val tenYearsAgo = currentTime - (10L * 365 * 24 * 60 * 60 * 1000)
                                    val oneYearFromNow = currentTime + (365L * 24 * 60 * 60 * 1000)

                                    if (parsedDate < tenYearsAgo || parsedDate > oneYearFromNow) {
                                        val warning = CsvImportWarning(
                                            message = "Date '$dateStr' seems unreasonable (parsed as ${dateFormat.format(Date(parsedDate))}), using current date",
                                            lineNumber = lineNumber,
                                            fieldName = "date",
                                            fieldValue = dateStr,
                                            fixedValue = dateFormat.format(Date(currentTime))
                                        )
                                        warnings.add(warning)
                                        Log.w(TAG, warning.toString())
                                        currentTime
                                    } else {
                                        if (usedFormat != dateFormat.toPattern()) {
                                            Log.d(TAG, "Date at line $lineNumber parsed using format '$usedFormat': '$dateStr' -> ${dateFormat.format(Date(parsedDate))}")
                                        }
                                        parsedDate
                                    }
                                } else {
                                    // If no format worked, use current date with warning
                                    val currentDate = System.currentTimeMillis()
                                    val warning = CsvImportWarning(
                                        message = "Invalid date format '$dateStr', using current date",
                                        lineNumber = lineNumber,
                                        fieldName = "date",
                                        fieldValue = dateStr,
                                        fixedValue = dateFormat.format(Date(currentDate))
                                    )
                                    warnings.add(warning)
                                    Log.w(TAG, warning.toString())
                                    currentDate
                                }
                            } else {
                                // If date is empty, use current date
                                val currentDate = System.currentTimeMillis()
                                Log.d(TAG, "Empty date field at line $lineNumber, using current date: ${dateFormat.format(Date(currentDate))}")
                                currentDate
                            }
                        } else {
                            // If date field is missing, use current date
                            val currentDate = System.currentTimeMillis()
                            Log.d(TAG, "Missing date field at line $lineNumber, using current date: ${dateFormat.format(Date(currentDate))}")
                            currentDate
                        }
                    } catch (e: Exception) {
                        // Handle any other exceptions by using current date
                        val currentDate = System.currentTimeMillis()
                        val warning = CsvImportWarning(
                            message = "Error parsing date: ${e.message}, using current date",
                            lineNumber = lineNumber,
                            fieldName = "date",
                            fieldValue = if (expenseData.size > 5) expenseData[5] else "",
                            fixedValue = dateFormat.format(Date(currentDate))
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString(), e)
                        currentDate
                    }

                    // Parse isCategoryLocked with enhanced validation
                    val isCategoryLocked = try {
                        // Check if the expense data has enough fields to include isCategoryLocked
                        if (expenseData.size > 6) {
                            val lockedStr = expenseData[6].trim().lowercase()
                            when (lockedStr) {
                                "true", "1", "yes", "y" -> true
                                "false", "0", "no", "n" -> false
                                "" -> {
                                    Log.d(TAG, "Empty isCategoryLocked field at line $lineNumber, using default (true)")
                                    true
                                }
                                else -> {
                                    val warning = CsvImportWarning(
                                        message = "Invalid boolean value for isCategoryLocked: '$lockedStr', using default (true)",
                                        lineNumber = lineNumber,
                                        fieldName = "isCategoryLocked",
                                        fieldValue = expenseData[6],
                                        fixedValue = "true"
                                    )
                                    warnings.add(warning)
                                    Log.w(TAG, warning.toString())
                                    true
                                }
                            }
                        } else {
                            // Default to true for existing expenses
                            Log.d(TAG, "Missing isCategoryLocked field at line $lineNumber, using default (true)")
                            true
                        }
                    } catch (e: Exception) {
                        // Default to true if parsing fails
                        val warning = CsvImportWarning(
                            message = "Error parsing isCategoryLocked: ${e.message}, using default (true)",
                            lineNumber = lineNumber,
                            fieldName = "isCategoryLocked",
                            fieldValue = if (expenseData.size > 6) expenseData[6] else "",
                            fixedValue = "true"
                        )
                        warnings.add(warning)
                        Log.w(TAG, warning.toString(), e)
                        true
                    }

                    // Always use current timestamp
                    val timestamp = System.currentTimeMillis()

                    // Create expense object
                    expenses.add(
                        Expense(
                            id = expenseId,
                            amount = amount,
                            description = description,
                            paidBy = paidBy,
                            splitBetween = splitBetween,
                            category = category,
                            date = date,
                            timestamp = timestamp,
                            isCategoryLocked = isCategoryLocked
                        )
                    )

                    successfulRows++
                    Log.d(TAG, "Successfully parsed expense at line $lineNumber")
                } catch (e: Exception) {
                    val warning = CsvImportWarning(
                        message = "Failed to create expense object, skipping line: ${e.message}",
                        lineNumber = lineNumber
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString(), e)
                }
            }

            // Parse member avatars
            val memberAvatars = mutableMapOf<String, String>()
            if (groupData.size > 2 && groupData[2].isNotBlank()) {
                try {
                    val avatarsEntries = groupData[2].split(";")
                    avatarsEntries.forEach { entry ->
                        val parts = entry.split("=")
                        if (parts.size == 2) {
                            memberAvatars[parts[0]] = parts[1]
                        }
                    }
                } catch (e: Exception) {
                    val warning = CsvImportWarning(
                        message = "Failed to parse member avatars, using empty map",
                        lineNumber = 2,
                        fieldName = "memberAvatars",
                        fieldValue = groupData[2],
                        fixedValue = "{}"
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString(), e)
                }
            }

            // Parse categories
            var categories = getDefaultCategories()
            if (groupData.size > 3 && groupData[3].isNotBlank()) {
                try {
                    val categoryEntries = groupData[3].split(";")
                    val parsedCategories = mutableListOf<com.example.splitexpenses.data.Category>()

                    categoryEntries.forEach { entry ->
                        val parts = entry.split("~")
                        if (parts.size >= 2) {
                            val name = parts[0]
                            val emoji = parts[1]
                            val keywords = if (parts.size > 2 && parts[2].isNotBlank()) {
                                parts[2].split("^")
                            } else {
                                emptyList()
                            }
                            parsedCategories.add(com.example.splitexpenses.data.Category(name, emoji, keywords))
                        }
                    }

                    if (parsedCategories.isNotEmpty()) {
                        categories = parsedCategories
                    }
                } catch (e: Exception) {
                    val warning = CsvImportWarning(
                        message = "Failed to parse categories, using default categories",
                        lineNumber = 2,
                        fieldName = "categories",
                        fieldValue = groupData[3]
                    )
                    warnings.add(warning)
                    Log.w(TAG, warning.toString(), e)
                }
            }

            // Create the group object
            Log.d(TAG, "Creating group object with ${expenses.size} expenses")
            val group = GroupData(
                id = groupId,
                name = groupName,
                members = members,
                expenses = expenses,
                memberUidMap = emptyMap(), // Will be populated by the repository
                allowedUsers = emptyList(), // Will be populated by the repository
                creatorUid = "", // Will be populated by the repository
                categories = categories,
                memberAvatars = memberAvatars
            )

            // Return successful result
            Log.d(TAG, "CSV import completed successfully with ${warnings.size} warnings")
            return CsvImportResult(
                success = true,
                group = group,
                warnings = warnings,
                totalRowsProcessed = totalRowsProcessed,
                successfulRows = successfulRows
            )

        } catch (e: Exception) {
            // Handle any unexpected exceptions
            val error = CsvImportError(
                message = "Unexpected error during CSV import: ${e.message}",
                exception = e
            )
            errors.add(error)
            Log.e(TAG, "Import failed with unexpected error", e)
            return CsvImportResult(
                success = false,
                errors = errors,
                warnings = warnings,
                totalRowsProcessed = totalRowsProcessed,
                successfulRows = successfulRows
            )
        } finally {
            // Close the input stream
            try {
                inputStream.close()
                Log.d(TAG, "Input stream closed")
            } catch (e: Exception) {
                Log.e(TAG, "Error closing input stream", e)
            }
        }
    }
}
