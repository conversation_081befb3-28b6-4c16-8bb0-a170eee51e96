package com.example.splitexpenses.ui.viewmodels

import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch

/**
 * Base ViewModel class that provides offline awareness functionality
 */
abstract class OfflineAwareViewModel<S : UiState>(
    private val connectivityManager: NetworkConnectivityManager
) : BaseViewModel<S>() {

    private val _isConnected = MutableStateFlow(true)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    init {
        // Monitor connectivity status
        viewModelScope.launch {
            connectivityManager.isConnected().collect { connected ->
                _isConnected.value = connected
            }
        }
    }

    /**
     * Check if the device is currently connected
     */
    protected fun isConnected(): <PERSON><PERSON>an {
        return _isConnected.value
    }

    /**
     * Get connectivity status as a flow
     */
    protected fun connectivityFlow(): Flow<Boolean> {
        return connectivityManager.isConnected()
    }

    /**
     * Execute an operation only if connected, otherwise show offline error
     */
    protected fun executeIfConnected(
        operation: suspend () -> Unit,
        onOfflineError: () -> Unit = { handleOfflineError() }
    ) {
        if (isConnected()) {
            launchWithErrorHandling {
                operation()
            }
        } else {
            onOfflineError()
        }
    }

    /**
     * Execute an operation that modifies existing data only if connected
     * This prevents editing existing content when offline
     */
    protected fun executeEditOperationIfConnected(
        operation: suspend () -> Unit,
        onOfflineError: () -> Unit = { handleOfflineEditError() }
    ) {
        if (isConnected()) {
            launchWithErrorHandling {
                operation()
            }
        } else {
            onOfflineError()
        }
    }

    /**
     * Execute an operation that creates new data - allowed offline
     * New content can be created offline and synced later
     */
    protected fun executeCreateOperation(
        operation: suspend () -> Unit
    ) {
        launchWithErrorHandling {
            operation()
        }
    }

    /**
     * Default handler for offline errors
     */
    protected open fun handleOfflineError() {
        println("Operation blocked: Device is offline")
        // Subclasses can override this to show user-friendly messages
    }

    /**
     * Default handler for offline edit errors
     */
    protected open fun handleOfflineEditError() {
        println("Edit operation blocked: Cannot edit existing content while offline")
        // Subclasses can override this to show user-friendly messages
    }
}
