package com.example.splitexpenses.data.sync

import android.util.Log
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.cache.SplitExpensesDatabase
import com.example.splitexpenses.data.cache.entities.SyncEntityType
import com.example.splitexpenses.data.cache.entities.SyncOperationType
import com.example.splitexpenses.data.cache.entities.SyncQueueEntity
import com.example.splitexpenses.data.source.DataSource
import com.google.gson.Gson
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages the sync queue for offline changes
 */
@Singleton
class SyncQueueManager @Inject constructor(
    private val database: SplitExpensesDatabase,
    private val remoteDataSource: DataSource
) {
    private val TAG = "SyncQueueManager"
    private val gson = Gson()
    private val maxRetries = 3

    /**
     * Add a group operation to the sync queue
     */
    suspend fun queueGroupOperation(
        group: GroupData,
        operationType: SyncOperationType
    ) {
        try {
            val syncItem = SyncQueueEntity(
                entityType = SyncEntityType.GROUP,
                entityId = group.id,
                operationType = operationType,
                data = gson.toJson(group)
            )
            database.syncQueueDao().insertSyncItem(syncItem)
            Log.d(TAG, "Queued group operation: ${operationType.name} for group ${group.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error queueing group operation", e)
        }
    }

    /**
     * Add an expense operation to the sync queue
     */
    suspend fun queueExpenseOperation(
        expense: Expense,
        groupId: String,
        operationType: SyncOperationType
    ) {
        try {
            val expenseWithGroupId = mapOf(
                "expense" to expense,
                "groupId" to groupId
            )
            val syncItem = SyncQueueEntity(
                entityType = SyncEntityType.EXPENSE,
                entityId = expense.id,
                operationType = operationType,
                data = gson.toJson(expenseWithGroupId)
            )
            database.syncQueueDao().insertSyncItem(syncItem)
            Log.d(TAG, "Queued expense operation: ${operationType.name} for expense ${expense.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error queueing expense operation", e)
        }
    }

    /**
     * Process all pending sync items
     */
    suspend fun processPendingSyncItems(): Boolean {
        return try {
            val pendingItems = database.syncQueueDao().getSyncItemsForRetry(maxRetries)
            var allSuccessful = true

            for (item in pendingItems) {
                val success = processSyncItem(item)
                if (success) {
                    database.syncQueueDao().deleteSyncItem(item)
                    Log.d(TAG, "Successfully synced ${item.entityType.name} ${item.entityId}")
                } else {
                    allSuccessful = false
                    database.syncQueueDao().incrementRetryCount(
                        item.id,
                        "Sync failed - will retry later"
                    )
                    Log.w(TAG, "Failed to sync ${item.entityType.name} ${item.entityId}")
                }
            }

            allSuccessful
        } catch (e: Exception) {
            Log.e(TAG, "Error processing pending sync items", e)
            false
        }
    }

    /**
     * Process a single sync item
     */
    private suspend fun processSyncItem(item: SyncQueueEntity): Boolean {
        return try {
            when (item.entityType) {
                SyncEntityType.GROUP -> processSyncGroupItem(item)
                SyncEntityType.EXPENSE -> processSyncExpenseItem(item)
                SyncEntityType.CATEGORY -> processSyncCategoryItem(item)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing sync item ${item.id}", e)
            false
        }
    }

    /**
     * Process a group sync item
     */
    private suspend fun processSyncGroupItem(item: SyncQueueEntity): Boolean {
        return try {
            val group = gson.fromJson(item.data, GroupData::class.java)
            
            when (item.operationType) {
                SyncOperationType.CREATE, SyncOperationType.UPDATE -> {
                    remoteDataSource.saveGroup(group)
                }
                SyncOperationType.DELETE -> {
                    remoteDataSource.deleteGroup(group.id)
                }
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error processing group sync item", e)
            false
        }
    }

    /**
     * Process an expense sync item
     */
    private suspend fun processSyncExpenseItem(item: SyncQueueEntity): Boolean {
        return try {
            val dataMap = gson.fromJson(item.data, Map::class.java) as Map<String, Any>
            val expenseJson = gson.toJson(dataMap["expense"])
            val expense = gson.fromJson(expenseJson, Expense::class.java)
            val groupId = dataMap["groupId"] as String
            
            when (item.operationType) {
                SyncOperationType.CREATE -> {
                    remoteDataSource.addExpense(groupId, expense)
                }
                SyncOperationType.UPDATE -> {
                    remoteDataSource.updateExpense(groupId, expense)
                }
                SyncOperationType.DELETE -> {
                    remoteDataSource.deleteExpense(groupId, expense.id)
                }
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error processing expense sync item", e)
            false
        }
    }

    /**
     * Process a category sync item
     */
    private suspend fun processSyncCategoryItem(item: SyncQueueEntity): Boolean {
        // Categories are part of groups, so they're handled through group updates
        return true
    }

    /**
     * Get pending sync count as Flow
     */
    fun getPendingSyncCountFlow(): Flow<Int> {
        return database.syncQueueDao().getPendingSyncCountFlow()
    }

    /**
     * Get pending sync count
     */
    suspend fun getPendingSyncCount(): Int {
        return database.syncQueueDao().getPendingSyncCount()
    }

    /**
     * Clear all sync items (use with caution)
     */
    suspend fun clearAllSyncItems() {
        database.syncQueueDao().clearAllSyncItems()
        Log.d(TAG, "Cleared all sync items")
    }

    /**
     * Remove sync items for a specific entity
     */
    suspend fun removeSyncItemsForEntity(entityId: String) {
        database.syncQueueDao().deleteSyncItemsByEntityId(entityId)
        Log.d(TAG, "Removed sync items for entity $entityId")
    }
}
