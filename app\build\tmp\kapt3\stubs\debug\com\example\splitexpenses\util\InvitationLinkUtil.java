package com.example.splitexpenses.util;

/**
 * Utility class for handling group invitation links
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0007\u001a\u0004\u0018\u00010\u00042\b\u0010\b\u001a\u0004\u0018\u00010\tJ\u000e\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u0004J\u001e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/example/splitexpenses/util/InvitationLinkUtil;", "", "()V", "HOST", "", "PATH", "SCHEME", "extractGroupId", "uri", "Landroid/net/Uri;", "generateInvitationLink", "groupId", "shareInvitationLink", "", "context", "Landroid/content/Context;", "groupName", "app_debug"})
public final class InvitationLinkUtil {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String SCHEME = "https";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String HOST = "splitexpenses.example.com";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PATH = "join";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.splitexpenses.util.InvitationLinkUtil INSTANCE = null;
    
    private InvitationLinkUtil() {
        super();
    }
    
    /**
     * Generate an invitation link for a group
     * @param groupId The ID of the group
     * @return A URI string for the invitation link
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String generateInvitationLink(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return null;
    }
    
    /**
     * Extract the group ID from an invitation link
     * @param uri The URI from the deep link
     * @return The group ID or null if the URI is invalid
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String extractGroupId(@org.jetbrains.annotations.Nullable()
    android.net.Uri uri) {
        return null;
    }
    
    /**
     * Share an invitation link using the Android share sheet
     * @param context The context to use for sharing
     * @param groupId The ID of the group to share
     * @param groupName The name of the group (for the share message)
     */
    public final void shareInvitationLink(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String groupName) {
    }
}