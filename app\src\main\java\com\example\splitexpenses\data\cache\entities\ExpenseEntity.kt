package com.example.splitexpenses.data.cache.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.example.splitexpenses.data.Expense
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Room entity for storing expense data locally
 */
@Entity(tableName = "expenses")
@TypeConverters(ExpenseEntity.Converters::class)
data class ExpenseEntity(
    @PrimaryKey
    val id: String,
    val groupId: String,
    val amount: Double,
    val description: String,
    val paidBy: String,
    val splitBetween: List<String>,
    val category: String,
    val date: Long,
    val timestamp: Long,
    val isCategoryLocked: Boolean,
    val lastModified: Long = System.currentTimeMillis(),
    val isSynced: Boolean = true
) {
    companion object {
        /**
         * Convert Expense to ExpenseEntity
         */
        fun fromExpense(expense: Expense, groupId: String, isSynced: Boolean = true): ExpenseEntity {
            return ExpenseEntity(
                id = expense.id,
                groupId = groupId,
                amount = expense.amount,
                description = expense.description,
                paidBy = expense.paidBy,
                splitBetween = expense.splitBetween,
                category = expense.category,
                date = expense.date,
                timestamp = expense.timestamp,
                isCategoryLocked = expense.isCategoryLocked,
                lastModified = System.currentTimeMillis(),
                isSynced = isSynced
            )
        }
    }

    /**
     * Convert ExpenseEntity to Expense
     */
    fun toExpense(): Expense {
        return Expense(
            id = id,
            amount = amount,
            description = description,
            paidBy = paidBy,
            splitBetween = splitBetween,
            category = category,
            date = date,
            timestamp = timestamp,
            isCategoryLocked = isCategoryLocked
        )
    }

    /**
     * Type converters for Room database
     */
    class Converters {
        private val gson = Gson()

        @TypeConverter
        fun fromStringList(value: List<String>): String {
            return gson.toJson(value)
        }

        @TypeConverter
        fun toStringList(value: String): List<String> {
            val listType = object : TypeToken<List<String>>() {}.type
            return gson.fromJson(value, listType)
        }
    }
}
