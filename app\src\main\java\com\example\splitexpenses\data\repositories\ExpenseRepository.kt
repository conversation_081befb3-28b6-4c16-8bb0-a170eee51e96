package com.example.splitexpenses.data.repositories

import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.UserFinance
import com.example.splitexpenses.data.source.DataSource
import kotlinx.coroutines.flow.StateFlow
import java.util.UUID
import kotlin.math.absoluteValue
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing expenses with offline capability
 */
@Singleton
class ExpenseRepository @Inject constructor(
    private val offlineCapableRepository: OfflineCapableRepository,
    private val groupRepository: GroupRepository
) {
    /**
     * Get the current group
     */
    val currentGroup: StateFlow<GroupData?> = groupRepository.currentGroup

    /**
     * Add a new expense to the current group
     * @param amount The amount of the expense
     * @param description The description of the expense
     * @param paidBy The name of the person who paid
     * @param splitBetween The names of the people to split the expense between
     * @param category The category of the expense
     * @param date The date of the expense
     * @param isCategoryLocked Whether the category is locked and should not be auto-detected
     */
    suspend fun addExpense(
        amount: Double,
        description: String,
        paidBy: String,
        splitBetween: List<String>,
        category: String,
        date: Long,
        isCategoryLocked: Boolean = false
    ) {
        println("ExpenseRepository: addExpense called with isCategoryLocked=$isCategoryLocked")

        val currentGroup = groupRepository.currentGroup.value ?: return
        val expenseId = UUID.randomUUID().toString()

        val expense = Expense(
            id = expenseId,
            amount = amount,
            description = description,
            paidBy = paidBy,
            splitBetween = splitBetween,
            category = category,
            date = date,
            isCategoryLocked = isCategoryLocked
        )

        println("ExpenseRepository: New expense created with isCategoryLocked=${expense.isCategoryLocked}")

        // Add the expense using the offline-capable repository
        offlineCapableRepository.addExpense(currentGroup.id, expense)
    }

    /**
     * Update an existing expense
     * @param expenseId The ID of the expense to update
     * @param amount The new amount
     * @param description The new description
     * @param paidBy The new payer
     * @param splitBetween The new split
     * @param category The new category
     * @param date The new date
     */
    suspend fun updateExpense(
        expenseId: String,
        amount: Double,
        description: String,
        paidBy: String,
        splitBetween: List<String>,
        category: String,
        date: Long,
        isCategoryLocked: Boolean = false
    ) {
        println("ExpenseRepository: updateExpense called with expenseId=$expenseId, isCategoryLocked=$isCategoryLocked")

        val currentGroup = groupRepository.currentGroup.value ?: return
        val expenseIndex = currentGroup.expenses.indexOfFirst { it.id == expenseId }

        if (expenseIndex == -1) return

        val originalExpense = currentGroup.expenses[expenseIndex]
        println("ExpenseRepository: Original expense isCategoryLocked=${originalExpense.isCategoryLocked}")

        val updatedExpense = Expense(
            id = expenseId,
            amount = amount,
            description = description,
            paidBy = paidBy,
            splitBetween = splitBetween,
            category = category,
            date = date,
            timestamp = originalExpense.timestamp,
            isCategoryLocked = isCategoryLocked
        )

        println("ExpenseRepository: Updated expense isCategoryLocked=${updatedExpense.isCategoryLocked}")

        // Update the expense using the offline-capable repository
        offlineCapableRepository.updateExpense(currentGroup.id, updatedExpense)
    }

    /**
     * Delete an expense
     * @param expenseId The ID of the expense to delete
     */
    suspend fun deleteExpense(expenseId: String) {
        val currentGroup = groupRepository.currentGroup.value ?: return

        // Delete the expense using the offline-capable repository
        offlineCapableRepository.deleteExpense(currentGroup.id, expenseId)
    }

    /**
     * Delete multiple expenses
     * @param expenseIds The IDs of the expenses to delete
     */
    suspend fun deleteExpenses(expenseIds: Set<String>) {
        val currentGroup = groupRepository.currentGroup.value ?: return

        // Delete expenses using the offline-capable repository
        offlineCapableRepository.deleteExpenses(currentGroup.id, expenseIds)
    }

    /**
     * Calculate the financial balances for the current group
     * @return A list of UserFinance objects representing each member's financial state
     */
    fun calculateFinances(): List<UserFinance> {
        val group = groupRepository.currentGroup.value ?: return emptyList()

        if (group.expenses.isEmpty()) return emptyList()

        val userFinances = mutableMapOf<String, UserFinance>()

        // Initialize finances for all members
        group.members.forEach { member ->
            userFinances[member] = UserFinance(userId = member)
        }

        // Calculate expenses and balances
        group.expenses.forEach { expense ->
            val paidBy = expense.paidBy
            val amount = expense.amount
            val splitBetween = expense.splitBetween

            // Add to total paid
            val paidByFinance = userFinances[paidBy] ?: UserFinance(userId = paidBy)
            userFinances[paidBy] = paidByFinance.copy(
                userExpense = paidByFinance.userExpense + amount
            )

            // Calculate splits
            if (splitBetween.isNotEmpty()) {
                val amountPerPerson = amount / splitBetween.size

                // Handle the case where the payer is not in the splitBetween list
                if (paidBy !in splitBetween) {
                    // If payer is not in splitBetween, they are owed the full amount
                    val payer = userFinances[paidBy] ?: UserFinance(userId = paidBy)
                    userFinances[paidBy] = payer.copy(
                        userBalance = payer.userBalance + amount
                    )
                }

                splitBetween.forEach { member ->
                    val memberFinance = userFinances[member] ?: UserFinance(userId = member)

                    // If this person paid, they owe less (everyone including the payer owes amountPerPerson)
                    if (member == paidBy) {
                        userFinances[member] = memberFinance.copy(
                            userBalance = memberFinance.userBalance + (amount - amountPerPerson)
                        )
                    } else {
                        userFinances[member] = memberFinance.copy(
                            userBalance = memberFinance.userBalance - amountPerPerson
                        )
                    }
                }
            } else {
                // If splitBetween is empty, the payer paid for themselves only
                val payer = userFinances[paidBy] ?: UserFinance(userId = paidBy)
                userFinances[paidBy] = payer.copy(
                    userBalance = payer.userBalance
                )
            }
        }

        return userFinances.values.toList()
    }

    /**
     * Calculate optimal settlements between members to resolve debts
     * @return A list of Triple objects representing (debtor, creditor, amount)
     */
    fun calculateSettlements(): List<Triple<String, String, Double>> {
        val finances = calculateFinances()

        // Filter out users with zero or near-zero balances (to handle floating-point precision issues)
        val significantFinances = finances.filter { kotlin.math.abs(it.userBalance) >= 0.01 }

        // Separate creditors (positive balance) and debtors (negative balance)
        val creditors = significantFinances.filter { it.userBalance > 0 }
            .sortedByDescending { it.userBalance }
            .map { it.copy() }
            .toMutableList()

        val debtors = significantFinances.filter { it.userBalance < 0 }
            .sortedBy { it.userBalance }
            .map { it.copy() }
            .toMutableList()

        val settlements = mutableListOf<Triple<String, String, Double>>()

        var i = 0
        var j = 0

        // Calculate settlements by matching debtors with creditors
        while (i < debtors.size && j < creditors.size) {
            val debtor = debtors[i]
            val creditor = creditors[j]

            // Find the minimum amount that can be settled (with a small threshold to handle floating-point precision)
            val amount = minOf(-debtor.userBalance, creditor.userBalance)

            if (amount > 0.01) {  // Only process settlements above a small threshold
                // Round the amount to 2 decimal places to avoid floating-point precision issues
                val roundedAmount = kotlin.math.round(amount * 100) / 100

                // Add the settlement
                settlements.add(Triple(debtor.userId, creditor.userId, roundedAmount))

                // Update balances
                debtors[i] = debtor.copy(userBalance = debtor.userBalance + roundedAmount)
                creditors[j] = creditor.copy(userBalance = creditor.userBalance - roundedAmount)

                // Move to the next person if their balance is settled (with a small threshold for floating-point precision)
                if (kotlin.math.abs(debtors[i].userBalance) < 0.01) {
                    i++
                }

                if (kotlin.math.abs(creditors[j].userBalance) < 0.01) {
                    j++
                }
            } else {
                // Skip very small amounts
                if (i < debtors.size - 1) {
                    i++
                } else if (j < creditors.size - 1) {
                    j++
                } else {
                    // We've reached the end of both lists
                    break
                }
            }
        }

        return settlements
    }
}
