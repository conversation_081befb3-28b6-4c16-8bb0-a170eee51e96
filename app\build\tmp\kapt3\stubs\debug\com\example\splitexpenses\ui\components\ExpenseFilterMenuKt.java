package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001ad\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00072\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00072\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0010H\u0007\u00a8\u0006\u0011"}, d2 = {"ExpenseFilterMenu", "", "isVisible", "", "filterState", "Lcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;", "availableCategories", "", "Lcom/example/splitexpenses/data/Category;", "availableMembers", "", "allExpenses", "Lcom/example/splitexpenses/data/Expense;", "onFilterStateChange", "Lkotlin/Function1;", "onDismiss", "Lkotlin/Function0;", "app_debug"})
public final class ExpenseFilterMenuKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class, androidx.compose.foundation.ExperimentalFoundationApi.class, androidx.compose.animation.ExperimentalAnimationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void ExpenseFilterMenu(boolean isVisible, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.ExpenseFilterState filterState, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Category> availableCategories, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> availableMembers, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Expense> allExpenses, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.splitexpenses.ui.viewmodels.ExpenseFilterState, kotlin.Unit> onFilterStateChange, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}