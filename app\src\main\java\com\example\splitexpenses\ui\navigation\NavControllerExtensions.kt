package com.example.splitexpenses.ui.navigation

import androidx.navigation.NavController
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.navOptions
import com.example.splitexpenses.R

/**
 * Extension functions for NavController to handle animations
 */

/**
 * Navigate to a destination with slide animations
 * - Next screen slides in from the right
 * - Current screen slides out to the left
 */
fun NavController.navigateWithSlideAnimation(route: String) {
    val navOptions = navOptions {
        anim {
            enter = R.anim.slide_in_right
            exit = R.anim.slide_out_left
            popEnter = R.anim.slide_in_left
            popExit = R.anim.slide_out_right
        }
    }
    navigate(route, navOptions)
}

/**
 * Navigate to a destination with custom options and slide animations
 */
fun NavController.navigateWithSlideAnimation(
    route: String,
    builder: NavOptionsBuilder.() -> Unit
) {
    val navOptions = navOptions {
        // Apply the custom options
        builder()

        // Add slide animations
        anim {
            enter = R.anim.slide_in_right
            exit = R.anim.slide_out_left
            popEnter = R.anim.slide_in_left
            popExit = R.anim.slide_out_right
        }
    }

    navigate(route, navOptions)
}

/**
 * Legacy functions for backward compatibility
 * These will now use slide animations instead of no animations
 */
fun NavController.navigateWithoutAnimation(route: String) {
    // Legacy function - now uses slide animations for all routes
    navigateWithSlideAnimation(route)
}

fun NavController.navigateWithoutAnimation(
    route: String,
    builder: NavOptionsBuilder.() -> Unit
) {
    // Legacy function - now uses slide animations for all routes
    navigateWithSlideAnimation(route, builder)
}

/**
 * Pop back stack with slide animation
 */
fun NavController.popBackStackWithoutAnimation() {
    // We can't directly control the animation for popBackStack,
    // but the NavHost's popEnterTransition and popExitTransition will handle it
    popBackStack()
}
