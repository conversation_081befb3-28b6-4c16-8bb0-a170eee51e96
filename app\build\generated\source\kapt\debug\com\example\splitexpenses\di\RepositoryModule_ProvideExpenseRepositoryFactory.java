package com.example.splitexpenses.di;

import com.example.splitexpenses.data.repositories.ExpenseRepository;
import com.example.splitexpenses.data.repositories.GroupRepository;
import com.example.splitexpenses.data.repositories.OfflineCapableRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideExpenseRepositoryFactory implements Factory<ExpenseRepository> {
  private final Provider<OfflineCapableRepository> offlineCapableRepositoryProvider;

  private final Provider<GroupRepository> groupRepositoryProvider;

  public RepositoryModule_ProvideExpenseRepositoryFactory(
      Provider<OfflineCapableRepository> offlineCapableRepositoryProvider,
      Provider<GroupRepository> groupRepositoryProvider) {
    this.offlineCapableRepositoryProvider = offlineCapableRepositoryProvider;
    this.groupRepositoryProvider = groupRepositoryProvider;
  }

  @Override
  public ExpenseRepository get() {
    return provideExpenseRepository(offlineCapableRepositoryProvider.get(), groupRepositoryProvider.get());
  }

  public static RepositoryModule_ProvideExpenseRepositoryFactory create(
      Provider<OfflineCapableRepository> offlineCapableRepositoryProvider,
      Provider<GroupRepository> groupRepositoryProvider) {
    return new RepositoryModule_ProvideExpenseRepositoryFactory(offlineCapableRepositoryProvider, groupRepositoryProvider);
  }

  public static ExpenseRepository provideExpenseRepository(
      OfflineCapableRepository offlineCapableRepository, GroupRepository groupRepository) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideExpenseRepository(offlineCapableRepository, groupRepository));
  }
}
