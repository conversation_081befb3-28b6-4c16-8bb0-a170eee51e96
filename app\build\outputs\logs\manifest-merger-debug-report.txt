-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:2:1-68:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:2:1-68:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:2:1-68:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:2:1-68:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b006af4544baf01915a313645ff82db4\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a8dd2d2846f89fa7e97ae33bbd91ce2\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da89fa1c76c2ef4b0cb85de664d475f6\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1fb0cb77ab1a413a3074f5def1029a\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3b3d42a002ffab03541fdce60c7ab1c\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c133b1e5355c57d17c18af202063bd6\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c17d1781abb50cd1e06a7a2ddfeec098\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.48.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\219993618b5e2fbd163a11f8888c0d21\transformed\hilt-android-2.48.1\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f109c297f73b0c8e9403384d4fc447\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\defafbafe2e50dbe8421fdf31646746d\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ca2460edd588319810ba949b78f7f1b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3421fec7e533ef58e0a7bc8b2bf86306\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:17:1-39:12
MERGED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4b7eedc5a96e03c63edd7d2a118e526\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f998e5bbfc36a99a5abfd12ff8f4f6ed\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.patrykandpatrick.vico:compose-m3:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f76bbb98091f8aeb658c89490b5ade3a\transformed\compose-m3-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.patrykandpatrick.vico:compose:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb227d39ce2b21aa210557752c335617\transformed\compose-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7dc3a7185889b10bd023552a3b0513dc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbd72a3f323a8e4b4a1a522b1d003ecb\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cfa3e17055ba867044109b8d277db9d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6224dea4b3d69e6f0ceb6a3ea64bcd0f\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d9aec6bc3c0f3b148374ba2464ebaa0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a570daead3d9a0e09cee3c22b866f0e\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\155a5df1ff3036106e3ee5a37a88717f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cc6d87dc86af54fbd04eea7ee2d4c68\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fde9642e2b6a1cf9a507c36dfe061b2\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc9046055456d2a525d3e05d85381d24\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c121a2318ae3d2f4d4e7c078d71b5a62\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd56c23ff56c948e88709a27b59c6fa\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\035a4e7aa700a3b95d1ef9282dca08ff\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\198c641ac00803315f8060b22477db38\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c03aff0a6fcc7d6e0621aded74932422\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6fc83ab0707f32d459245be6e3ee94d\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b83d3de83f199e14dbe26a60deaf5ce\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0fdaec7ddc36958ad1a04e6fa761ef5\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73ec72bdd12bb0ce8b690c3bf06c67a4\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test.ext:junit:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4d45055781bdf7722ddf056cda183ee\transformed\junit-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:17:1-53:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\201bc572c097d552b0819e7e72fbc4bc\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6663e44de3084f93b2fe84a211a475b\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9914e010bf00d3285757f938c6829cf\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71b71203351f72e57efbcd5f51f0ebf8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d353b5fe63a1a8f18242e27d2f5b71f\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e41e00b20117d1aab37721f81a9cd4d9\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e6032d02b225d138ca39200cc2f2b67\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9c5c293b9bccb3452a24d1d22534197\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8952469578e45a57c22f86a1b631e875\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7990dbba7e220bbb86ba2c26d527690a\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30d809214462b80fbfe822607332fe\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57db576eadd03f06fea2efc7ef81387b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5ea8ec2356f5c9f95c96870928d5a11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4c2b9620e0654bdb4d40d7aeaaa954\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d182bcbf78f80662a6794fca3249e3f9\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08b202a85ee381a8046024641512a8e\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf3237b0c2d36e666189ffdc0129c619\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cd2de34331f6503d4d89fcffeead9\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\463b12b032bf8942f440fc5dcbc9ab42\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e282d6e17406a40180758eca5db1c1b5\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fd3574bd8e1f70f233c38ad40ef04cd\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.patrykandpatrick.vico:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ad4620270cafcb163cad6ef82f7d012\transformed\core-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32ecea5e05b938f12fd80eec4962bae1\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2c2b067070e6ef49086890aefaa08b3\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa911471e545f6e40186656fffc1c810\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c4c2b1bdaa549b401251e443abe1016\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d95c334c310e44010b4d1f0b827c71c\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32ffe7f18302515d9d73a141b3dfe2c2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d4b4fd6b12060c14cb17ace579b48aa\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.tracing:tracing-perfetto-binary:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dc352d08cd03f872a00f40df6a3ff81\transformed\tracing-perfetto-binary-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:17:1-58:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb4e805a7093247a5c4c38d8c64f7393\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\763183c68ff5d21dd9bd171de9d56dec\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7545f60795553792f0ff02ede687070e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b780b5538f0f4dabb582f550579cb67\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test.services:storage:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4371408ee2739727c27f94518a918bce\transformed\storage-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:monitor:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eeab4f44c72f2ed0352ccdb74fd126d2\transformed\monitor-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75d175235add0f8427f8a1abbb87845\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a73bd7b60ecfe2e449f0f74c2df2338\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71a5eb892e83449b93587c97a186331b\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5328546d37b565873a519751732948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68705badfa779d69b402919df6ccc6b2\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f34c0c9d38bc40e7399042c715d3a12\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e33a3f3676c7da62f5f2265a8188299\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f00b581999b7efbfd718ef8ecc8e8f7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5d47a7b5a2b97216b4298d89f5c559d\transformed\dagger-lint-aar-2.48.1\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16f1c534c44147540a4a9c6d96af748f\transformed\uiautomator-2.2.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:6:5-7:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:7:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:8:5-9:38
MERGED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:27:5-81
MERGED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:27:5-81
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:12:22-73
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:15:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:31:5-67
MERGED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:31:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:15:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:16:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:16:22-76
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:18:5-66:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:18:5-66:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f109c297f73b0c8e9403384d4fc447\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f109c297f73b0c8e9403384d4fc447\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ca2460edd588319810ba949b78f7f1b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ca2460edd588319810ba949b78f7f1b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:29:5-37:19
MERGED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:29:5-37:19
MERGED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:42:5-81
MERGED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:42:5-81
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:26:5-51:19
MERGED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:26:5-51:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32ecea5e05b938f12fd80eec4962bae1\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32ecea5e05b938f12fd80eec4962bae1\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:23:5-56:19
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:23:5-56:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75d175235add0f8427f8a1abbb87845\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75d175235add0f8427f8a1abbb87845\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5328546d37b565873a519751732948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5328546d37b565873a519751732948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16f1c534c44147540a4a9c6d96af748f\transformed\uiautomator-2.2.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16f1c534c44147540a4a9c6d96af748f\transformed\uiautomator-2.2.0\AndroidManifest.xml:22:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:26:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:24:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:22:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:25:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:28:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:23:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:20:9-35
	android:networkSecurityConfig
		ADDED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:42:18-78
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:27:9-51
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:21:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:19:9-49
activity#com.example.splitexpenses.ui.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:29:9-65:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:32:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:31:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:33:13-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:30:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:35:13-38:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:36:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:36:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:37:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:join+data:pathPattern:/.*+data:scheme:splitexpenses
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:41:13-51:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:42:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:43:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:44:27-75
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:47:17-50:49
	android:host
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:49:21-40
	android:scheme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:48:21-51
	android:pathPattern
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:50:21-46
	android:pathPrefix
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:63:21-47
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:splitexpenses.example.com+data:pathPrefix:/join+data:scheme:https
ADDED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml:54:13-64:29
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b006af4544baf01915a313645ff82db4\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b006af4544baf01915a313645ff82db4\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a8dd2d2846f89fa7e97ae33bbd91ce2\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a8dd2d2846f89fa7e97ae33bbd91ce2\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da89fa1c76c2ef4b0cb85de664d475f6\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da89fa1c76c2ef4b0cb85de664d475f6\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1fb0cb77ab1a413a3074f5def1029a\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1fb0cb77ab1a413a3074f5def1029a\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3b3d42a002ffab03541fdce60c7ab1c\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3b3d42a002ffab03541fdce60c7ab1c\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c133b1e5355c57d17c18af202063bd6\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c133b1e5355c57d17c18af202063bd6\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c17d1781abb50cd1e06a7a2ddfeec098\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c17d1781abb50cd1e06a7a2ddfeec098\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.48.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\219993618b5e2fbd163a11f8888c0d21\transformed\hilt-android-2.48.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\219993618b5e2fbd163a11f8888c0d21\transformed\hilt-android-2.48.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f109c297f73b0c8e9403384d4fc447\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f109c297f73b0c8e9403384d4fc447\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\defafbafe2e50dbe8421fdf31646746d\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\defafbafe2e50dbe8421fdf31646746d\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ca2460edd588319810ba949b78f7f1b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ca2460edd588319810ba949b78f7f1b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3421fec7e533ef58e0a7bc8b2bf86306\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3421fec7e533ef58e0a7bc8b2bf86306\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4b7eedc5a96e03c63edd7d2a118e526\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4b7eedc5a96e03c63edd7d2a118e526\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f998e5bbfc36a99a5abfd12ff8f4f6ed\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f998e5bbfc36a99a5abfd12ff8f4f6ed\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.patrykandpatrick.vico:compose-m3:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f76bbb98091f8aeb658c89490b5ade3a\transformed\compose-m3-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.patrykandpatrick.vico:compose-m3:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f76bbb98091f8aeb658c89490b5ade3a\transformed\compose-m3-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.patrykandpatrick.vico:compose:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb227d39ce2b21aa210557752c335617\transformed\compose-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.patrykandpatrick.vico:compose:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb227d39ce2b21aa210557752c335617\transformed\compose-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7dc3a7185889b10bd023552a3b0513dc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7dc3a7185889b10bd023552a3b0513dc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbd72a3f323a8e4b4a1a522b1d003ecb\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbd72a3f323a8e4b4a1a522b1d003ecb\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cfa3e17055ba867044109b8d277db9d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cfa3e17055ba867044109b8d277db9d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6224dea4b3d69e6f0ceb6a3ea64bcd0f\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6224dea4b3d69e6f0ceb6a3ea64bcd0f\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d9aec6bc3c0f3b148374ba2464ebaa0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d9aec6bc3c0f3b148374ba2464ebaa0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a570daead3d9a0e09cee3c22b866f0e\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a570daead3d9a0e09cee3c22b866f0e\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\155a5df1ff3036106e3ee5a37a88717f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\155a5df1ff3036106e3ee5a37a88717f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cc6d87dc86af54fbd04eea7ee2d4c68\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cc6d87dc86af54fbd04eea7ee2d4c68\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fde9642e2b6a1cf9a507c36dfe061b2\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fde9642e2b6a1cf9a507c36dfe061b2\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc9046055456d2a525d3e05d85381d24\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc9046055456d2a525d3e05d85381d24\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c121a2318ae3d2f4d4e7c078d71b5a62\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c121a2318ae3d2f4d4e7c078d71b5a62\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd56c23ff56c948e88709a27b59c6fa\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd56c23ff56c948e88709a27b59c6fa\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\035a4e7aa700a3b95d1ef9282dca08ff\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\035a4e7aa700a3b95d1ef9282dca08ff\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\198c641ac00803315f8060b22477db38\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\198c641ac00803315f8060b22477db38\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c03aff0a6fcc7d6e0621aded74932422\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c03aff0a6fcc7d6e0621aded74932422\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6fc83ab0707f32d459245be6e3ee94d\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6fc83ab0707f32d459245be6e3ee94d\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b83d3de83f199e14dbe26a60deaf5ce\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b83d3de83f199e14dbe26a60deaf5ce\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0fdaec7ddc36958ad1a04e6fa761ef5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0fdaec7ddc36958ad1a04e6fa761ef5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73ec72bdd12bb0ce8b690c3bf06c67a4\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73ec72bdd12bb0ce8b690c3bf06c67a4\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.test.ext:junit:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4d45055781bdf7722ddf056cda183ee\transformed\junit-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.ext:junit:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4d45055781bdf7722ddf056cda183ee\transformed\junit-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\201bc572c097d552b0819e7e72fbc4bc\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\201bc572c097d552b0819e7e72fbc4bc\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6663e44de3084f93b2fe84a211a475b\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6663e44de3084f93b2fe84a211a475b\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9914e010bf00d3285757f938c6829cf\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9914e010bf00d3285757f938c6829cf\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71b71203351f72e57efbcd5f51f0ebf8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71b71203351f72e57efbcd5f51f0ebf8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d353b5fe63a1a8f18242e27d2f5b71f\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d353b5fe63a1a8f18242e27d2f5b71f\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e41e00b20117d1aab37721f81a9cd4d9\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e41e00b20117d1aab37721f81a9cd4d9\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e6032d02b225d138ca39200cc2f2b67\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e6032d02b225d138ca39200cc2f2b67\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9c5c293b9bccb3452a24d1d22534197\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9c5c293b9bccb3452a24d1d22534197\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8952469578e45a57c22f86a1b631e875\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8952469578e45a57c22f86a1b631e875\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7990dbba7e220bbb86ba2c26d527690a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7990dbba7e220bbb86ba2c26d527690a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30d809214462b80fbfe822607332fe\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30d809214462b80fbfe822607332fe\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57db576eadd03f06fea2efc7ef81387b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57db576eadd03f06fea2efc7ef81387b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5ea8ec2356f5c9f95c96870928d5a11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5ea8ec2356f5c9f95c96870928d5a11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4c2b9620e0654bdb4d40d7aeaaa954\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4c2b9620e0654bdb4d40d7aeaaa954\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d182bcbf78f80662a6794fca3249e3f9\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d182bcbf78f80662a6794fca3249e3f9\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08b202a85ee381a8046024641512a8e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08b202a85ee381a8046024641512a8e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf3237b0c2d36e666189ffdc0129c619\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf3237b0c2d36e666189ffdc0129c619\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cd2de34331f6503d4d89fcffeead9\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cd2de34331f6503d4d89fcffeead9\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\463b12b032bf8942f440fc5dcbc9ab42\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\463b12b032bf8942f440fc5dcbc9ab42\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e282d6e17406a40180758eca5db1c1b5\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e282d6e17406a40180758eca5db1c1b5\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fd3574bd8e1f70f233c38ad40ef04cd\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fd3574bd8e1f70f233c38ad40ef04cd\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.patrykandpatrick.vico:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ad4620270cafcb163cad6ef82f7d012\transformed\core-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.patrykandpatrick.vico:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ad4620270cafcb163cad6ef82f7d012\transformed\core-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32ecea5e05b938f12fd80eec4962bae1\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32ecea5e05b938f12fd80eec4962bae1\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2c2b067070e6ef49086890aefaa08b3\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2c2b067070e6ef49086890aefaa08b3\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa911471e545f6e40186656fffc1c810\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa911471e545f6e40186656fffc1c810\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c4c2b1bdaa549b401251e443abe1016\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c4c2b1bdaa549b401251e443abe1016\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d95c334c310e44010b4d1f0b827c71c\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d95c334c310e44010b4d1f0b827c71c\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32ffe7f18302515d9d73a141b3dfe2c2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32ffe7f18302515d9d73a141b3dfe2c2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d4b4fd6b12060c14cb17ace579b48aa\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d4b4fd6b12060c14cb17ace579b48aa\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.tracing:tracing-perfetto-binary:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dc352d08cd03f872a00f40df6a3ff81\transformed\tracing-perfetto-binary-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-perfetto-binary:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dc352d08cd03f872a00f40df6a3ff81\transformed\tracing-perfetto-binary-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb4e805a7093247a5c4c38d8c64f7393\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb4e805a7093247a5c4c38d8c64f7393\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\763183c68ff5d21dd9bd171de9d56dec\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\763183c68ff5d21dd9bd171de9d56dec\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7545f60795553792f0ff02ede687070e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7545f60795553792f0ff02ede687070e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b780b5538f0f4dabb582f550579cb67\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b780b5538f0f4dabb582f550579cb67\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.test.services:storage:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4371408ee2739727c27f94518a918bce\transformed\storage-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.services:storage:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4371408ee2739727c27f94518a918bce\transformed\storage-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eeab4f44c72f2ed0352ccdb74fd126d2\transformed\monitor-1.7.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eeab4f44c72f2ed0352ccdb74fd126d2\transformed\monitor-1.7.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75d175235add0f8427f8a1abbb87845\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75d175235add0f8427f8a1abbb87845\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a73bd7b60ecfe2e449f0f74c2df2338\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a73bd7b60ecfe2e449f0f74c2df2338\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71a5eb892e83449b93587c97a186331b\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71a5eb892e83449b93587c97a186331b\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5328546d37b565873a519751732948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5328546d37b565873a519751732948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68705badfa779d69b402919df6ccc6b2\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68705badfa779d69b402919df6ccc6b2\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f34c0c9d38bc40e7399042c715d3a12\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f34c0c9d38bc40e7399042c715d3a12\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e33a3f3676c7da62f5f2265a8188299\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e33a3f3676c7da62f5f2265a8188299\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f00b581999b7efbfd718ef8ecc8e8f7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f00b581999b7efbfd718ef8ecc8e8f7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5d47a7b5a2b97216b4298d89f5c559d\transformed\dagger-lint-aar-2.48.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5d47a7b5a2b97216b4298d89f5c559d\transformed\dagger-lint-aar-2.48.1\AndroidManifest.xml:18:3-42
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16f1c534c44147540a4a9c6d96af748f\transformed\uiautomator-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.test.uiautomator:uiautomator:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16f1c534c44147540a4a9c6d96af748f\transformed\uiautomator-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:28:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:27:13-84
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2855345bbcf1a1e292c18893122fe855\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2673b804717d9b59abfbd1af2f09ef\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5609bbc6e44fda6838b7c0d7c7b1c045\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b2e80354745b47be47d86cd00e4247\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2263f789bee9de3e8936c9adae687da6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
activity#androidx.benchmark.IsolationActivity
ADDED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:32:9-36:20
	android:exported
		ADDED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:34:13-36
	android:theme
		ADDED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:35:13-77
	android:name
		ADDED from [androidx.benchmark:benchmark-common:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78be77585a15065cafa3e7988ed84a8d\transformed\benchmark-common-1.3.4\AndroidManifest.xml:33:13-64
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:23:5-25:53
	tools:ignore
		ADDED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:25:9-50
	android:name
		ADDED from [androidx.benchmark:benchmark-macro:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\577c614c642d08c94724ae4fb259801b\transformed\benchmark-macro-1.3.4\AndroidManifest.xml:24:9-61
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66c906c982f7eeb2087a5653a7677910\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a5fbbe3f3dd51b578fb2a256ed3023\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-permission#android.permission.REORDER_TASKS
ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:24:22-69
activity#androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity
ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:27:9-34:20
	android:exported
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:30:13-56
	android:name
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:28:13-99
intent-filter#category:name:android.intent.category.LAUNCHER
ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
	android:priority
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
activity#androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity
ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:35:9-42:20
	android:exported
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:37:13-36
	android:theme
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:38:13-56
	android:name
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:36:13-95
activity#androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity
ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:43:9-50:20
	android:exported
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:45:13-36
	android:theme
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:46:13-62
	android:name
		ADDED from [androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5657f6063dd1869549247e75dad7ed17\transformed\core-1.6.1\AndroidManifest.xml:44:13-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:39:9-47:20
MERGED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:39:9-47:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75d175235add0f8427f8a1abbb87845\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75d175235add0f8427f8a1abbb87845\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c435e89784dfd22d1dcd7676321409\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd30e78710a09d31b87886400cef71f\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.splitexpenses.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\faac399e6b2ef4972910475a8683353b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43e0e4658725c97c9b3b7b9218894ad\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
receiver#androidx.tracing.perfetto.TracingReceiver
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:24:9-37:20
	android:enabled
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:27:13-35
	android:exported
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:29:13-57
	android:directBootAware
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:26:13-44
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:25:13-69
intent-filter#action:name:androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START+action:name:androidx.tracing.perfetto.action.ENABLE_TRACING+action:name:androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:32:13-36:29
action#androidx.tracing.perfetto.action.ENABLE_TRACING
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:33:17-90
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:33:25-87
action#androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:34:17-101
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:34:25-98
action#androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:35:17-102
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:35:25-99
meta-data#androidx.tracing.perfetto.StartupTracingInitializer
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:44:13-46:52
	android:value
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:46:17-49
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:45:17-83
receiver#androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate
ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:49:9-55:20
	android:enabled
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:52:13-36
	android:exported
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:53:13-37
	tools:targetApi
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:54:13-32
	android:directBootAware
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:51:13-44
	android:name
		ADDED from [androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f929f74dece7675f6dd2b5d7532a84f\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:50:13-92
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c0eb4009999e1235787d8c079f8b18\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
