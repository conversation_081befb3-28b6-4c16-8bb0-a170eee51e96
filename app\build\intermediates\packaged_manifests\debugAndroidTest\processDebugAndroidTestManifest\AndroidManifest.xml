<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.splitexpenses.test" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <instrumentation
        android:name="androidx.test.runner.AndroidJUnitRunner"
        android:functionalTest="false"
        android:handleProfiling="false"
        android:label="Tests for com.example.splitexpenses"
        android:targetPackage="com.example.splitexpenses" />

    <queries>
        <package android:name="androidx.test.orchestrator" />
        <package android:name="androidx.test.services" />
        <package android:name="com.google.android.apps.common.testing.services" />
    </queries>

    <application
        android:debuggable="true"
        android:extractNativeLibs="false" >
        <uses-library android:name="android.test.runner" />
    </application>

</manifest>