package com.example.splitexpenses.ui.components

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp

/**
 * Dialog for confirming deletion of multiple groups
 * @param groupCount The number of groups to delete
 * @param onDismiss Callback for when the dialog is dismissed
 * @param onConfirm Callback for when the deletion is confirmed
 */
@Composable
fun DeleteMultipleGroupsDialog(
    groupCount: Int,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = { Text("Delete Groups") },
        text = { 
            Text(
                if (groupCount == 1) {
                    "Are you sure you want to delete this group? This action cannot be undone."
                } else {
                    "Are you sure you want to delete these $groupCount groups? This action cannot be undone."
                }
            )
        },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("Delete")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
