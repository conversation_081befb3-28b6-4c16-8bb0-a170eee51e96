package com.example.splitexpenses.ui.navigation;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u0012\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0004\u001a+\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u00042\u0017\u0010\u0005\u001a\u0013\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0006\u00a2\u0006\u0002\b\b\u001a\u0012\u0010\t\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0004\u001a+\u0010\t\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u00042\u0017\u0010\u0005\u001a\u0013\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0006\u00a2\u0006\u0002\b\b\u001a\n\u0010\n\u001a\u00020\u0001*\u00020\u0002\u00a8\u0006\u000b"}, d2 = {"navigateWithSlideAnimation", "", "Landroidx/navigation/NavController;", "route", "", "builder", "Lkotlin/Function1;", "Landroidx/navigation/NavOptionsBuilder;", "Lkotlin/ExtensionFunctionType;", "navigateWithoutAnimation", "popBackStackWithoutAnimation", "app_debug"})
public final class NavControllerExtensionsKt {
    
    /**
     * Navigate to a destination with slide animations
     * The actual animations are defined in the NavHost
     */
    public static final void navigateWithSlideAnimation(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController $this$navigateWithSlideAnimation, @org.jetbrains.annotations.NotNull()
    java.lang.String route) {
    }
    
    /**
     * Navigate to a destination with custom options and slide animations
     * The actual animations are defined in the NavHost
     */
    public static final void navigateWithSlideAnimation(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController $this$navigateWithSlideAnimation, @org.jetbrains.annotations.NotNull()
    java.lang.String route, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super androidx.navigation.NavOptionsBuilder, kotlin.Unit> builder) {
    }
    
    /**
     * Legacy functions for backward compatibility
     * These now rely on the NavHost's global animations
     */
    public static final void navigateWithoutAnimation(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController $this$navigateWithoutAnimation, @org.jetbrains.annotations.NotNull()
    java.lang.String route) {
    }
    
    public static final void navigateWithoutAnimation(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController $this$navigateWithoutAnimation, @org.jetbrains.annotations.NotNull()
    java.lang.String route, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super androidx.navigation.NavOptionsBuilder, kotlin.Unit> builder) {
    }
    
    /**
     * Pop back stack - animations handled by NavHost
     */
    public static final void popBackStackWithoutAnimation(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController $this$popBackStackWithoutAnimation) {
    }
}