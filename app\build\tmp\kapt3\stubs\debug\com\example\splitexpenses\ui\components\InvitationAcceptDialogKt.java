package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a`\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005H\u0007\u00a8\u0006\f"}, d2 = {"InvitationAcceptDialog", "", "groupName", "", "unassignedMembers", "", "onAccept", "Lkotlin/Function1;", "onDismiss", "Lkotlin/Function0;", "allMembers", "assignedMembers", "app_debug"})
public final class InvitationAcceptDialogKt {
    
    /**
     * Dialog for accepting a group invitation
     * @param groupName The name of the group
     * @param unassignedMembers List of unassigned member names to choose from
     * @param onAccept Callback when the user accepts the invitation with a selected member name
     * @param onDismiss Callback when the user dismisses the dialog
     * @param allMembers List of all members in the group (optional)
     * @param assignedMembers List of already assigned members (optional)
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void InvitationAcceptDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String groupName, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> unassignedMembers, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAccept, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> allMembers, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> assignedMembers) {
    }
}