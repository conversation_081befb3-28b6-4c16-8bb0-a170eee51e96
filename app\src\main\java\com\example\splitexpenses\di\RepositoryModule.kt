package com.example.splitexpenses.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

import javax.inject.Singleton

import com.example.splitexpenses.data.repositories.ExpenseRepository
import com.example.splitexpenses.data.repositories.GroupRepository
import com.example.splitexpenses.data.repositories.OfflineCapableRepository
import com.example.splitexpenses.data.source.DataSource
import com.example.splitexpenses.data.source.LocalDataSource

/**
 * Hilt module that provides repository dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    /**
     * Provides the group repository
     * @param offlineCapableRepository The offline-capable repository
     * @param localDataSource The data source for local operations
     * @return The group repository implementation
     */
    @Provides
    @Singleton
    fun provideGroupRepository(
        offlineCapableRepository: OfflineCapableRepository,
        localDataSource: LocalDataSource
    ): GroupRepository {
        return GroupRepository(offlineCapableRepository, localDataSource)
    }

    /**
     * Provides the expense repository
     * @param offlineCapableRepository The offline-capable repository
     * @param groupRepository The group repository
     * @return The expense repository implementation
     */
    @Provides
    @Singleton
    fun provideExpenseRepository(
        offlineCapableRepository: OfflineCapableRepository,
        groupRepository: GroupRepository
    ): ExpenseRepository {
        return ExpenseRepository(offlineCapableRepository, groupRepository)
    }
}
