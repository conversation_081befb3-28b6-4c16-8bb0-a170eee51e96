package com.example.splitexpenses.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.example.splitexpenses.ui.components.OfflineStatusIndicator

/**
 * Dialog for editing a group's name
 * @param currentName The current name of the group
 * @param onDismiss Callback for when the dialog is dismissed
 * @param onSave Callback for when the name is saved, with a callback for success/failure
 * @param isOffline Whether the device is currently offline
 */
@Composable
fun EditGroupNameDialog(
    currentName: String,
    onDismiss: () -> Unit,
    onSave: (String, (Boolean, String?) -> Unit) -> Unit,
    isOffline: Boolean = false
) {
    var groupName by remember { mutableStateOf(currentName) }
    var showError by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = {
            Text(
                text = "Edit Group Name",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.primary
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
            ) {
                // Show offline indicator if offline
                if (isOffline) {
                    OfflineStatusIndicator(
                        isOffline = true,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }

                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.padding(vertical = 16.dp)
                    )
                } else {
                    OutlinedTextField(
                        value = groupName,
                        onValueChange = {
                            groupName = it
                            showError = false
                            errorMessage = null
                        },
                        label = { Text("Group Name") },
                        modifier = Modifier.fillMaxWidth(),
                        isError = showError || errorMessage != null,
                        singleLine = true,
                        enabled = !isOffline,
                        colors = TextFieldDefaults.colors(
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    )

                    if (showError) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Group name cannot be empty",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    if (errorMessage != null) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = errorMessage!!,
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (groupName.isBlank()) {
                        showError = true
                        return@Button
                    }

                    isLoading = true
                    onSave(groupName) { success, error ->
                        isLoading = false
                        if (success) {
                            onDismiss()
                        } else {
                            errorMessage = error ?: "Failed to update group name"
                        }
                    }
                },
                enabled = !isLoading && groupName.isNotBlank() && !isOffline
            ) {
                Text("Save")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text("Cancel")
            }
        }
    )
}
