{"logs": [{"outputFile": "com.example.splitexpenses.test.app-mergeDebugAndroidTestResources-39:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\af30d809214462b80fbfe822607332fe\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,954,1036,1105,1179,1257,1333,1407,1478", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,73,77,75,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,949,1031,1100,1174,1252,1328,1402,1473,1592"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "802,891,975,1068,1163,1246,1323,1408,1494,1573,1651,1733,1802,1876,1954,2131,2205,2276", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,73,77,75,73,70,118", "endOffsets": "886,970,1063,1158,1241,1318,1403,1489,1568,1646,1728,1797,1871,1949,2025,2200,2271,2390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\faac399e6b2ef4972910475a8683353b\\transformed\\core-1.16.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,495,596,696,2030", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "194,296,393,490,591,691,797,2126"}}]}]}