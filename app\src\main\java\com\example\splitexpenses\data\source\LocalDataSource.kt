package com.example.splitexpenses.data.source

import android.content.Context
import android.content.SharedPreferences
import com.example.splitexpenses.data.UserPreferences
import java.util.UUID

/**
 * Local data source for user preferences and device-specific data
 */
class LocalDataSource(private val context: Context) {
    private val userPreferences = UserPreferences(context)

    /**
     * Get the application context
     * @return The application context
     */
    fun getContext(): Context {
        return context
    }

    /**
     * Get the device's unique identifier
     * @return The device UID
     */
    fun getDeviceUid(): String {
        return userPreferences.getDeviceUid()
    }

    /**
     * Get the saved user name for a specific group
     * @param groupId The ID of the group
     * @return The user name or null if not found
     */
    fun getSavedUserForGroup(groupId: String): String? {
        return userPreferences.getSavedUserForGroup(groupId)
    }

    /**
     * Save the mapping between a user name and the group
     * @param groupId The ID of the group
     * @param userName The user name to save
     */
    fun saveUserForGroup(groupId: String, userName: String) {
        userPreferences.saveUserForGroup(groupId, userName)
    }

    /**
     * Get the UID for a user in a specific group
     * @param groupId The ID of the group
     * @param userName The user name
     * @return The UID or null if not found
     */
    fun getUidForUserInGroup(groupId: String, userName: String): String? {
        return userPreferences.getUidForUserInGroup(groupId, userName)
    }

    /**
     * Save the mapping between a user name and UID for a specific group
     * @param groupId The ID of the group
     * @param userName The user name
     * @param uid The UID to save
     */
    fun saveUidForUserInGroup(groupId: String, userName: String, uid: String) {
        userPreferences.saveUidForUserInGroup(groupId, userName, uid)
    }

    /**
     * Get the user name for a UID in a specific group
     * @param groupId The ID of the group
     * @param uid The UID
     * @return The user name or null if not found
     */
    fun getUserNameForUidInGroup(groupId: String, uid: String): String? {
        return userPreferences.getUserNameForUidInGroup(groupId, uid)
    }
}
