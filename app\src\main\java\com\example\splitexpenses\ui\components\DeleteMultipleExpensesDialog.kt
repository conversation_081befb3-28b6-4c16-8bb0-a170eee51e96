package com.example.splitexpenses.ui.components

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp

/**
 * Dialog for confirming deletion of multiple expenses
 * @param expenseCount The number of expenses to delete
 * @param onDismiss Callback for when the dialog is dismissed
 * @param onConfirm Callback for when the deletion is confirmed
 */
@Composable
fun DeleteMultipleExpensesDialog(
    expenseCount: Int,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = { Text("Delete Expenses") },
        text = { 
            Text(
                if (expenseCount == 1) {
                    "Are you sure you want to delete this expense? This action cannot be undone."
                } else {
                    "Are you sure you want to delete these $expenseCount expenses? This action cannot be undone."
                }
            )
        },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("Delete")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
