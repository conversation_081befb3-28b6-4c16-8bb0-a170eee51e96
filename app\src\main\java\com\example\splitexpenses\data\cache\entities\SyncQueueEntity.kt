package com.example.splitexpenses.data.cache.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters

/**
 * Entity types that can be synced
 */
enum class SyncEntityType {
    GROUP,
    EXPENSE,
    CATEGORY
}

/**
 * Types of operations that can be synced
 */
enum class SyncOperationType {
    CREATE,
    UPDATE,
    DELETE
}

/**
 * Room entity for storing sync queue items
 * This tracks changes made while offline that need to be synced when online
 */
@Entity(tableName = "sync_queue")
@TypeConverters(SyncQueueEntity.Converters::class)
data class SyncQueueEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,
    val entityType: SyncEntityType,
    val entityId: String,
    val operationType: SyncOperationType,
    val data: String, // JSON representation of the entity data
    val timestamp: Long = System.currentTimeMillis(),
    val retryCount: Int = 0,
    val lastError: String? = null
) {
    /**
     * Type converters for Room database
     */
    class Converters {
        @TypeConverter
        fun fromSyncEntityType(value: SyncEntityType): String {
            return value.name
        }

        @TypeConverter
        fun toSyncEntityType(value: String): SyncEntityType {
            return SyncEntityType.valueOf(value)
        }

        @TypeConverter
        fun fromSyncOperationType(value: SyncOperationType): String {
            return value.name
        }

        @TypeConverter
        fun toSyncOperationType(value: String): SyncOperationType {
            return SyncOperationType.valueOf(value)
        }
    }
}
