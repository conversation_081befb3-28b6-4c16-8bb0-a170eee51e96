package com.example.splitexpenses.data.repositories;

import com.example.splitexpenses.data.source.LocalDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GroupRepository_Factory implements Factory<GroupRepository> {
  private final Provider<OfflineCapableRepository> offlineCapableRepositoryProvider;

  private final Provider<LocalDataSource> localDataSourceProvider;

  public GroupRepository_Factory(
      Provider<OfflineCapableRepository> offlineCapableRepositoryProvider,
      Provider<LocalDataSource> localDataSourceProvider) {
    this.offlineCapableRepositoryProvider = offlineCapableRepositoryProvider;
    this.localDataSourceProvider = localDataSourceProvider;
  }

  @Override
  public GroupRepository get() {
    return newInstance(offlineCapableRepositoryProvider.get(), localDataSourceProvider.get());
  }

  public static GroupRepository_Factory create(
      Provider<OfflineCapableRepository> offlineCapableRepositoryProvider,
      Provider<LocalDataSource> localDataSourceProvider) {
    return new GroupRepository_Factory(offlineCapableRepositoryProvider, localDataSourceProvider);
  }

  public static GroupRepository newInstance(OfflineCapableRepository offlineCapableRepository,
      LocalDataSource localDataSource) {
    return new GroupRepository(offlineCapableRepository, localDataSource);
  }
}
