package com.example.splitexpenses.data.cache.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.splitexpenses.data.Category;
import com.example.splitexpenses.data.cache.entities.GroupEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class GroupDao_Impl implements GroupDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<GroupEntity> __insertionAdapterOfGroupEntity;

  private final GroupEntity.Converters __converters = new GroupEntity.Converters();

  private final EntityDeletionOrUpdateAdapter<GroupEntity> __deletionAdapterOfGroupEntity;

  private final EntityDeletionOrUpdateAdapter<GroupEntity> __updateAdapterOfGroupEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteGroupById;

  private final SharedSQLiteStatement __preparedStmtOfMarkGroupAsSynced;

  private final SharedSQLiteStatement __preparedStmtOfMarkGroupAsUnsynced;

  private final SharedSQLiteStatement __preparedStmtOfClearAllGroups;

  public GroupDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfGroupEntity = new EntityInsertionAdapter<GroupEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `groups` (`id`,`name`,`members`,`memberUidMap`,`allowedUsers`,`creatorUid`,`categories`,`memberAvatars`,`lastModified`,`isSynced`) VALUES (?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GroupEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        final String _tmp = __converters.fromStringList(entity.getMembers());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        final String _tmp_1 = __converters.fromStringMap(entity.getMemberUidMap());
        if (_tmp_1 == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp_1);
        }
        final String _tmp_2 = __converters.fromStringList(entity.getAllowedUsers());
        if (_tmp_2 == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp_2);
        }
        if (entity.getCreatorUid() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getCreatorUid());
        }
        final String _tmp_3 = __converters.fromCategoryList(entity.getCategories());
        if (_tmp_3 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_3);
        }
        final String _tmp_4 = __converters.fromStringMap(entity.getMemberAvatars());
        if (_tmp_4 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_4);
        }
        statement.bindLong(9, entity.getLastModified());
        final int _tmp_5 = entity.isSynced() ? 1 : 0;
        statement.bindLong(10, _tmp_5);
      }
    };
    this.__deletionAdapterOfGroupEntity = new EntityDeletionOrUpdateAdapter<GroupEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `groups` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GroupEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfGroupEntity = new EntityDeletionOrUpdateAdapter<GroupEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `groups` SET `id` = ?,`name` = ?,`members` = ?,`memberUidMap` = ?,`allowedUsers` = ?,`creatorUid` = ?,`categories` = ?,`memberAvatars` = ?,`lastModified` = ?,`isSynced` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GroupEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        final String _tmp = __converters.fromStringList(entity.getMembers());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        final String _tmp_1 = __converters.fromStringMap(entity.getMemberUidMap());
        if (_tmp_1 == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp_1);
        }
        final String _tmp_2 = __converters.fromStringList(entity.getAllowedUsers());
        if (_tmp_2 == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp_2);
        }
        if (entity.getCreatorUid() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getCreatorUid());
        }
        final String _tmp_3 = __converters.fromCategoryList(entity.getCategories());
        if (_tmp_3 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_3);
        }
        final String _tmp_4 = __converters.fromStringMap(entity.getMemberAvatars());
        if (_tmp_4 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_4);
        }
        statement.bindLong(9, entity.getLastModified());
        final int _tmp_5 = entity.isSynced() ? 1 : 0;
        statement.bindLong(10, _tmp_5);
        if (entity.getId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteGroupById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM groups WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkGroupAsSynced = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE groups SET isSynced = 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkGroupAsUnsynced = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE groups SET isSynced = 0, lastModified = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllGroups = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM groups";
        return _query;
      }
    };
  }

  @Override
  public Object insertGroup(final GroupEntity group, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfGroupEntity.insert(group);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object insertGroups(final List<GroupEntity> groups,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfGroupEntity.insert(groups);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteGroup(final GroupEntity group, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfGroupEntity.handle(group);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object updateGroup(final GroupEntity group, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfGroupEntity.handle(group);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteGroupById(final String groupId, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteGroupById.acquire();
        int _argIndex = 1;
        if (groupId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, groupId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteGroupById.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Object markGroupAsSynced(final String groupId, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkGroupAsSynced.acquire();
        int _argIndex = 1;
        if (groupId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, groupId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkGroupAsSynced.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Object markGroupAsUnsynced(final String groupId, final long timestamp,
      final Continuation<? super Unit> arg2) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkGroupAsUnsynced.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        _argIndex = 2;
        if (groupId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, groupId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkGroupAsUnsynced.release(_stmt);
        }
      }
    }, arg2);
  }

  @Override
  public Object clearAllGroups(final Continuation<? super Unit> arg0) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllGroups.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllGroups.release(_stmt);
        }
      }
    }, arg0);
  }

  @Override
  public Flow<List<GroupEntity>> getAllGroupsFlow() {
    final String _sql = "SELECT * FROM groups ORDER BY lastModified DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"groups"}, new Callable<List<GroupEntity>>() {
      @Override
      @NonNull
      public List<GroupEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfMembers = CursorUtil.getColumnIndexOrThrow(_cursor, "members");
          final int _cursorIndexOfMemberUidMap = CursorUtil.getColumnIndexOrThrow(_cursor, "memberUidMap");
          final int _cursorIndexOfAllowedUsers = CursorUtil.getColumnIndexOrThrow(_cursor, "allowedUsers");
          final int _cursorIndexOfCreatorUid = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUid");
          final int _cursorIndexOfCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "categories");
          final int _cursorIndexOfMemberAvatars = CursorUtil.getColumnIndexOrThrow(_cursor, "memberAvatars");
          final int _cursorIndexOfLastModified = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModified");
          final int _cursorIndexOfIsSynced = CursorUtil.getColumnIndexOrThrow(_cursor, "isSynced");
          final List<GroupEntity> _result = new ArrayList<GroupEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GroupEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final List<String> _tmpMembers;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfMembers)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfMembers);
            }
            _tmpMembers = __converters.toStringList(_tmp);
            final Map<String, String> _tmpMemberUidMap;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfMemberUidMap)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfMemberUidMap);
            }
            _tmpMemberUidMap = __converters.toStringMap(_tmp_1);
            final List<String> _tmpAllowedUsers;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfAllowedUsers)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfAllowedUsers);
            }
            _tmpAllowedUsers = __converters.toStringList(_tmp_2);
            final String _tmpCreatorUid;
            if (_cursor.isNull(_cursorIndexOfCreatorUid)) {
              _tmpCreatorUid = null;
            } else {
              _tmpCreatorUid = _cursor.getString(_cursorIndexOfCreatorUid);
            }
            final List<Category> _tmpCategories;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCategories)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfCategories);
            }
            _tmpCategories = __converters.toCategoryList(_tmp_3);
            final Map<String, String> _tmpMemberAvatars;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfMemberAvatars)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfMemberAvatars);
            }
            _tmpMemberAvatars = __converters.toStringMap(_tmp_4);
            final long _tmpLastModified;
            _tmpLastModified = _cursor.getLong(_cursorIndexOfLastModified);
            final boolean _tmpIsSynced;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsSynced);
            _tmpIsSynced = _tmp_5 != 0;
            _item = new GroupEntity(_tmpId,_tmpName,_tmpMembers,_tmpMemberUidMap,_tmpAllowedUsers,_tmpCreatorUid,_tmpCategories,_tmpMemberAvatars,_tmpLastModified,_tmpIsSynced);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllGroups(final Continuation<? super List<GroupEntity>> arg0) {
    final String _sql = "SELECT * FROM groups ORDER BY lastModified DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GroupEntity>>() {
      @Override
      @NonNull
      public List<GroupEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfMembers = CursorUtil.getColumnIndexOrThrow(_cursor, "members");
          final int _cursorIndexOfMemberUidMap = CursorUtil.getColumnIndexOrThrow(_cursor, "memberUidMap");
          final int _cursorIndexOfAllowedUsers = CursorUtil.getColumnIndexOrThrow(_cursor, "allowedUsers");
          final int _cursorIndexOfCreatorUid = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUid");
          final int _cursorIndexOfCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "categories");
          final int _cursorIndexOfMemberAvatars = CursorUtil.getColumnIndexOrThrow(_cursor, "memberAvatars");
          final int _cursorIndexOfLastModified = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModified");
          final int _cursorIndexOfIsSynced = CursorUtil.getColumnIndexOrThrow(_cursor, "isSynced");
          final List<GroupEntity> _result = new ArrayList<GroupEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GroupEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final List<String> _tmpMembers;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfMembers)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfMembers);
            }
            _tmpMembers = __converters.toStringList(_tmp);
            final Map<String, String> _tmpMemberUidMap;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfMemberUidMap)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfMemberUidMap);
            }
            _tmpMemberUidMap = __converters.toStringMap(_tmp_1);
            final List<String> _tmpAllowedUsers;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfAllowedUsers)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfAllowedUsers);
            }
            _tmpAllowedUsers = __converters.toStringList(_tmp_2);
            final String _tmpCreatorUid;
            if (_cursor.isNull(_cursorIndexOfCreatorUid)) {
              _tmpCreatorUid = null;
            } else {
              _tmpCreatorUid = _cursor.getString(_cursorIndexOfCreatorUid);
            }
            final List<Category> _tmpCategories;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCategories)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfCategories);
            }
            _tmpCategories = __converters.toCategoryList(_tmp_3);
            final Map<String, String> _tmpMemberAvatars;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfMemberAvatars)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfMemberAvatars);
            }
            _tmpMemberAvatars = __converters.toStringMap(_tmp_4);
            final long _tmpLastModified;
            _tmpLastModified = _cursor.getLong(_cursorIndexOfLastModified);
            final boolean _tmpIsSynced;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsSynced);
            _tmpIsSynced = _tmp_5 != 0;
            _item = new GroupEntity(_tmpId,_tmpName,_tmpMembers,_tmpMemberUidMap,_tmpAllowedUsers,_tmpCreatorUid,_tmpCategories,_tmpMemberAvatars,_tmpLastModified,_tmpIsSynced);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getGroupById(final String groupId, final Continuation<? super GroupEntity> arg1) {
    final String _sql = "SELECT * FROM groups WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (groupId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, groupId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<GroupEntity>() {
      @Override
      @Nullable
      public GroupEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfMembers = CursorUtil.getColumnIndexOrThrow(_cursor, "members");
          final int _cursorIndexOfMemberUidMap = CursorUtil.getColumnIndexOrThrow(_cursor, "memberUidMap");
          final int _cursorIndexOfAllowedUsers = CursorUtil.getColumnIndexOrThrow(_cursor, "allowedUsers");
          final int _cursorIndexOfCreatorUid = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUid");
          final int _cursorIndexOfCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "categories");
          final int _cursorIndexOfMemberAvatars = CursorUtil.getColumnIndexOrThrow(_cursor, "memberAvatars");
          final int _cursorIndexOfLastModified = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModified");
          final int _cursorIndexOfIsSynced = CursorUtil.getColumnIndexOrThrow(_cursor, "isSynced");
          final GroupEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final List<String> _tmpMembers;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfMembers)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfMembers);
            }
            _tmpMembers = __converters.toStringList(_tmp);
            final Map<String, String> _tmpMemberUidMap;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfMemberUidMap)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfMemberUidMap);
            }
            _tmpMemberUidMap = __converters.toStringMap(_tmp_1);
            final List<String> _tmpAllowedUsers;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfAllowedUsers)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfAllowedUsers);
            }
            _tmpAllowedUsers = __converters.toStringList(_tmp_2);
            final String _tmpCreatorUid;
            if (_cursor.isNull(_cursorIndexOfCreatorUid)) {
              _tmpCreatorUid = null;
            } else {
              _tmpCreatorUid = _cursor.getString(_cursorIndexOfCreatorUid);
            }
            final List<Category> _tmpCategories;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCategories)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfCategories);
            }
            _tmpCategories = __converters.toCategoryList(_tmp_3);
            final Map<String, String> _tmpMemberAvatars;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfMemberAvatars)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfMemberAvatars);
            }
            _tmpMemberAvatars = __converters.toStringMap(_tmp_4);
            final long _tmpLastModified;
            _tmpLastModified = _cursor.getLong(_cursorIndexOfLastModified);
            final boolean _tmpIsSynced;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsSynced);
            _tmpIsSynced = _tmp_5 != 0;
            _result = new GroupEntity(_tmpId,_tmpName,_tmpMembers,_tmpMemberUidMap,_tmpAllowedUsers,_tmpCreatorUid,_tmpCategories,_tmpMemberAvatars,_tmpLastModified,_tmpIsSynced);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Flow<GroupEntity> getGroupByIdFlow(final String groupId) {
    final String _sql = "SELECT * FROM groups WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (groupId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, groupId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"groups"}, new Callable<GroupEntity>() {
      @Override
      @Nullable
      public GroupEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfMembers = CursorUtil.getColumnIndexOrThrow(_cursor, "members");
          final int _cursorIndexOfMemberUidMap = CursorUtil.getColumnIndexOrThrow(_cursor, "memberUidMap");
          final int _cursorIndexOfAllowedUsers = CursorUtil.getColumnIndexOrThrow(_cursor, "allowedUsers");
          final int _cursorIndexOfCreatorUid = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUid");
          final int _cursorIndexOfCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "categories");
          final int _cursorIndexOfMemberAvatars = CursorUtil.getColumnIndexOrThrow(_cursor, "memberAvatars");
          final int _cursorIndexOfLastModified = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModified");
          final int _cursorIndexOfIsSynced = CursorUtil.getColumnIndexOrThrow(_cursor, "isSynced");
          final GroupEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final List<String> _tmpMembers;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfMembers)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfMembers);
            }
            _tmpMembers = __converters.toStringList(_tmp);
            final Map<String, String> _tmpMemberUidMap;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfMemberUidMap)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfMemberUidMap);
            }
            _tmpMemberUidMap = __converters.toStringMap(_tmp_1);
            final List<String> _tmpAllowedUsers;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfAllowedUsers)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfAllowedUsers);
            }
            _tmpAllowedUsers = __converters.toStringList(_tmp_2);
            final String _tmpCreatorUid;
            if (_cursor.isNull(_cursorIndexOfCreatorUid)) {
              _tmpCreatorUid = null;
            } else {
              _tmpCreatorUid = _cursor.getString(_cursorIndexOfCreatorUid);
            }
            final List<Category> _tmpCategories;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCategories)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfCategories);
            }
            _tmpCategories = __converters.toCategoryList(_tmp_3);
            final Map<String, String> _tmpMemberAvatars;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfMemberAvatars)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfMemberAvatars);
            }
            _tmpMemberAvatars = __converters.toStringMap(_tmp_4);
            final long _tmpLastModified;
            _tmpLastModified = _cursor.getLong(_cursorIndexOfLastModified);
            final boolean _tmpIsSynced;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsSynced);
            _tmpIsSynced = _tmp_5 != 0;
            _result = new GroupEntity(_tmpId,_tmpName,_tmpMembers,_tmpMemberUidMap,_tmpAllowedUsers,_tmpCreatorUid,_tmpCategories,_tmpMemberAvatars,_tmpLastModified,_tmpIsSynced);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUnsyncedGroups(final Continuation<? super List<GroupEntity>> arg0) {
    final String _sql = "SELECT * FROM groups WHERE isSynced = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GroupEntity>>() {
      @Override
      @NonNull
      public List<GroupEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfMembers = CursorUtil.getColumnIndexOrThrow(_cursor, "members");
          final int _cursorIndexOfMemberUidMap = CursorUtil.getColumnIndexOrThrow(_cursor, "memberUidMap");
          final int _cursorIndexOfAllowedUsers = CursorUtil.getColumnIndexOrThrow(_cursor, "allowedUsers");
          final int _cursorIndexOfCreatorUid = CursorUtil.getColumnIndexOrThrow(_cursor, "creatorUid");
          final int _cursorIndexOfCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "categories");
          final int _cursorIndexOfMemberAvatars = CursorUtil.getColumnIndexOrThrow(_cursor, "memberAvatars");
          final int _cursorIndexOfLastModified = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModified");
          final int _cursorIndexOfIsSynced = CursorUtil.getColumnIndexOrThrow(_cursor, "isSynced");
          final List<GroupEntity> _result = new ArrayList<GroupEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GroupEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final List<String> _tmpMembers;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfMembers)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfMembers);
            }
            _tmpMembers = __converters.toStringList(_tmp);
            final Map<String, String> _tmpMemberUidMap;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfMemberUidMap)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfMemberUidMap);
            }
            _tmpMemberUidMap = __converters.toStringMap(_tmp_1);
            final List<String> _tmpAllowedUsers;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfAllowedUsers)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfAllowedUsers);
            }
            _tmpAllowedUsers = __converters.toStringList(_tmp_2);
            final String _tmpCreatorUid;
            if (_cursor.isNull(_cursorIndexOfCreatorUid)) {
              _tmpCreatorUid = null;
            } else {
              _tmpCreatorUid = _cursor.getString(_cursorIndexOfCreatorUid);
            }
            final List<Category> _tmpCategories;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCategories)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfCategories);
            }
            _tmpCategories = __converters.toCategoryList(_tmp_3);
            final Map<String, String> _tmpMemberAvatars;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfMemberAvatars)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfMemberAvatars);
            }
            _tmpMemberAvatars = __converters.toStringMap(_tmp_4);
            final long _tmpLastModified;
            _tmpLastModified = _cursor.getLong(_cursorIndexOfLastModified);
            final boolean _tmpIsSynced;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsSynced);
            _tmpIsSynced = _tmp_5 != 0;
            _item = new GroupEntity(_tmpId,_tmpName,_tmpMembers,_tmpMemberUidMap,_tmpAllowedUsers,_tmpCreatorUid,_tmpCategories,_tmpMemberAvatars,_tmpLastModified,_tmpIsSynced);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
