  
addExpense androidx.lifecycle.ViewModel  createGroup androidx.lifecycle.ViewModel  deleteCurrentGroup androidx.lifecycle.ViewModel  
deleteExpense androidx.lifecycle.ViewModel  deleteExpenses androidx.lifecycle.ViewModel  deleteGroups androidx.lifecycle.ViewModel  	joinGroup androidx.lifecycle.ViewModel  setMultiSelectMode androidx.lifecycle.ViewModel  toggleExpenseSelection androidx.lifecycle.ViewModel  toggleGroupSelection androidx.lifecycle.ViewModel  
updateExpense androidx.lifecycle.ViewModel  updateGroupMembers androidx.lifecycle.ViewModel  ExampleUnitTest com.example.splitexpenses  Test )com.example.splitexpenses.ExampleUnitTest  Category com.example.splitexpenses.data  Expense com.example.splitexpenses.data  	GroupData com.example.splitexpenses.data  IllegalStateException com.example.splitexpenses.data  OfflineCapableRepositoryTest com.example.splitexpenses.data  Unit com.example.splitexpenses.data  UserFinance com.example.splitexpenses.data  assert com.example.splitexpenses.data  coEvery com.example.splitexpenses.data  coVerify com.example.splitexpenses.data  contains com.example.splitexpenses.data  createTestExpense com.example.splitexpenses.data  createTestGroup com.example.splitexpenses.data  every com.example.splitexpenses.data  flowOf com.example.splitexpenses.data  networkConnectivityManager com.example.splitexpenses.data  offlineDataSource com.example.splitexpenses.data  remoteDataSource com.example.splitexpenses.data  
repository com.example.splitexpenses.data  runTest com.example.splitexpenses.data  syncQueueManager com.example.splitexpenses.data  amount &com.example.splitexpenses.data.Expense  category &com.example.splitexpenses.data.Expense  date &com.example.splitexpenses.data.Expense  description &com.example.splitexpenses.data.Expense  id &com.example.splitexpenses.data.Expense  paidBy &com.example.splitexpenses.data.Expense  splitBetween &com.example.splitexpenses.data.Expense  allowedUsers (com.example.splitexpenses.data.GroupData  copy (com.example.splitexpenses.data.GroupData  currentUser (com.example.splitexpenses.data.GroupData  getCURRENTUser (com.example.splitexpenses.data.GroupData  getCurrentUser (com.example.splitexpenses.data.GroupData  id (com.example.splitexpenses.data.GroupData  members (com.example.splitexpenses.data.GroupData  name (com.example.splitexpenses.data.GroupData  Before ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  
DataSource ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  Expense ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  	GroupData ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  IllegalStateException ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  NetworkConnectivityManager ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  OfflineCapableRepository ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  OfflineDataSource ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  SyncQueueManager ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  Test ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  Unit ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  assert ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  coEvery ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  coVerify ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  contains ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  createTestExpense ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  createTestGroup ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  every ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  flowOf ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  	getASSERT ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  	getAssert ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  
getCOEvery ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  getCONTAINS ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  getCOVerify ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  
getCoEvery ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  getCoVerify ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  getContains ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  getEVERY ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  getEvery ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  	getFLOWOf ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  	getFlowOf ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  
getRUNTest ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  
getRunTest ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  networkConnectivityManager ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  offlineDataSource ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  remoteDataSource ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  
repository ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  runTest ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  syncQueueManager ;com.example.splitexpenses.data.OfflineCapableRepositoryTest  NetworkConnectivityManager +com.example.splitexpenses.data.connectivity  isConnected Fcom.example.splitexpenses.data.connectivity.NetworkConnectivityManager  ByteArrayOutputStream +com.example.splitexpenses.data.repositories  Expense +com.example.splitexpenses.data.repositories  ExpenseRepository +com.example.splitexpenses.data.repositories  ExpenseRepositoryTest +com.example.splitexpenses.data.repositories  	GroupData +com.example.splitexpenses.data.repositories  GroupRepository +com.example.splitexpenses.data.repositories  GroupRepositoryTest +com.example.splitexpenses.data.repositories  MutableStateFlow +com.example.splitexpenses.data.repositories  OfflineCapableRepository +com.example.splitexpenses.data.repositories  UUID +com.example.splitexpenses.data.repositories  assertEquals +com.example.splitexpenses.data.repositories  
assertNull +com.example.splitexpenses.data.repositories  coEvery +com.example.splitexpenses.data.repositories  coVerify +com.example.splitexpenses.data.repositories  com +com.example.splitexpenses.data.repositories  currentUser +com.example.splitexpenses.data.repositories  
dataSource +com.example.splitexpenses.data.repositories  	emptyList +com.example.splitexpenses.data.repositories  every +com.example.splitexpenses.data.repositories  expenseRepository +com.example.splitexpenses.data.repositories  first +com.example.splitexpenses.data.repositories  groupRepository +com.example.splitexpenses.data.repositories  java +com.example.splitexpenses.data.repositories  listOf +com.example.splitexpenses.data.repositories  localDataSource +com.example.splitexpenses.data.repositories  mapOf +com.example.splitexpenses.data.repositories  mockkStatic +com.example.splitexpenses.data.repositories  runTest +com.example.splitexpenses.data.repositories  setOf +com.example.splitexpenses.data.repositories  
testDeviceUid +com.example.splitexpenses.data.repositories  	testGroup +com.example.splitexpenses.data.repositories  testGroupId +com.example.splitexpenses.data.repositories  to +com.example.splitexpenses.data.repositories  verify +com.example.splitexpenses.data.repositories  
addExpense =com.example.splitexpenses.data.repositories.ExpenseRepository  
deleteExpense =com.example.splitexpenses.data.repositories.ExpenseRepository  deleteExpenses =com.example.splitexpenses.data.repositories.ExpenseRepository  
updateExpense =com.example.splitexpenses.data.repositories.ExpenseRepository  Before Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  
DataSource Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  Expense Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  ExpenseRepository Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  	GroupData Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  GroupRepository Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  Test Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  coVerify Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  
dataSource Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  expenseRepository Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  getCOVerify Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  getCoVerify Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  	getLISTOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  	getListOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  getMAPOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  getMapOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  
getRUNTest Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  
getRunTest Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  getSETOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  getSetOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  getTO Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  getTo Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  listOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  mapOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  runTest Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  setOf Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  testGroupId Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  to Acom.example.splitexpenses.data.repositories.ExpenseRepositoryTest  createGroup ;com.example.splitexpenses.data.repositories.GroupRepository  currentGroup ;com.example.splitexpenses.data.repositories.GroupRepository  deleteGroup ;com.example.splitexpenses.data.repositories.GroupRepository  exportGroupToCsv ;com.example.splitexpenses.data.repositories.GroupRepository  	joinGroup ;com.example.splitexpenses.data.repositories.GroupRepository  updateGroupMembers ;com.example.splitexpenses.data.repositories.GroupRepository  Before ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  ByteArrayOutputStream ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
DataSource ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  	GroupData ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  GroupRepository ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  LocalDataSource ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  MutableStateFlow ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  Test ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  UUID ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  assertEquals ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
assertNull ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  coEvery ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  coVerify ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  com ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  currentUser ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
dataSource ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  	emptyList ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  every ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  first ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getASSERTEquals ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
getASSERTNull ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getAssertEquals ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
getAssertNull ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
getCOEvery ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getCOM ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getCOVerify ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getCURRENTUser ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
getCoEvery ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getCoVerify ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getCom ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getCurrentUser ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getEMPTYList ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getEVERY ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getEmptyList ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getEvery ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getFIRST ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getFirst ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  	getLISTOf ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  	getListOf ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getMOCKKStatic ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  getMockkStatic ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
getRUNTest ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
getRunTest ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  	getVERIFY ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  	getVerify ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  groupRepository ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  java ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  listOf ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  localDataSource ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  mockkStatic ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  runTest ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
testDeviceUid ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  	testGroup ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  testGroupId ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  verify ?com.example.splitexpenses.data.repositories.GroupRepositoryTest  
addExpense Dcom.example.splitexpenses.data.repositories.OfflineCapableRepository  
deleteExpense Dcom.example.splitexpenses.data.repositories.OfflineCapableRepository  	saveGroup Dcom.example.splitexpenses.data.repositories.OfflineCapableRepository  
updateExpense Dcom.example.splitexpenses.data.repositories.OfflineCapableRepository  
DataSource %com.example.splitexpenses.data.source  Expense %com.example.splitexpenses.data.source  FirebaseDataSource %com.example.splitexpenses.data.source  FirebaseDataSourceTest %com.example.splitexpenses.data.source  	GroupData %com.example.splitexpenses.data.source  LocalDataSource %com.example.splitexpenses.data.source  OfflineDataSource %com.example.splitexpenses.data.source  assertEquals %com.example.splitexpenses.data.source  
assertNull %com.example.splitexpenses.data.source  await %com.example.splitexpenses.data.source  coEvery %com.example.splitexpenses.data.source  coVerify %com.example.splitexpenses.data.source  
dataSource %com.example.splitexpenses.data.source  	emptyList %com.example.splitexpenses.data.source  every %com.example.splitexpenses.data.source  first %com.example.splitexpenses.data.source  groupReference %com.example.splitexpenses.data.source  groupsReference %com.example.splitexpenses.data.source  java %com.example.splitexpenses.data.source  launch %com.example.splitexpenses.data.source  listOf %com.example.splitexpenses.data.source  mockk %com.example.splitexpenses.data.source  returns %com.example.splitexpenses.data.source  runTest %com.example.splitexpenses.data.source  slot %com.example.splitexpenses.data.source  	testGroup %com.example.splitexpenses.data.source  testGroupId %com.example.splitexpenses.data.source  
addExpense 0com.example.splitexpenses.data.source.DataSource  
deleteExpense 0com.example.splitexpenses.data.source.DataSource  deleteExpenses 0com.example.splitexpenses.data.source.DataSource  deleteGroup 0com.example.splitexpenses.data.source.DataSource  getGroup 0com.example.splitexpenses.data.source.DataSource  	saveGroup 0com.example.splitexpenses.data.source.DataSource  
updateExpense 0com.example.splitexpenses.data.source.DataSource  updateGroupField 0com.example.splitexpenses.data.source.DataSource  
addExpense 8com.example.splitexpenses.data.source.FirebaseDataSource  deleteGroup 8com.example.splitexpenses.data.source.FirebaseDataSource  getAvailableGroups 8com.example.splitexpenses.data.source.FirebaseDataSource  getGroup 8com.example.splitexpenses.data.source.FirebaseDataSource  observeGroup 8com.example.splitexpenses.data.source.FirebaseDataSource  	saveGroup 8com.example.splitexpenses.data.source.FirebaseDataSource  updateGroupField 8com.example.splitexpenses.data.source.FirebaseDataSource  Before <com.example.splitexpenses.data.source.FirebaseDataSourceTest  DataSnapshot <com.example.splitexpenses.data.source.FirebaseDataSourceTest  DatabaseReference <com.example.splitexpenses.data.source.FirebaseDataSourceTest  Expense <com.example.splitexpenses.data.source.FirebaseDataSourceTest  FirebaseDataSource <com.example.splitexpenses.data.source.FirebaseDataSourceTest  	GroupData <com.example.splitexpenses.data.source.FirebaseDataSourceTest  Test <com.example.splitexpenses.data.source.FirebaseDataSourceTest  ValueEventListener <com.example.splitexpenses.data.source.FirebaseDataSourceTest  assertEquals <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
assertNull <com.example.splitexpenses.data.source.FirebaseDataSourceTest  await <com.example.splitexpenses.data.source.FirebaseDataSourceTest  coEvery <com.example.splitexpenses.data.source.FirebaseDataSourceTest  coVerify <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
dataSource <com.example.splitexpenses.data.source.FirebaseDataSourceTest  	emptyList <com.example.splitexpenses.data.source.FirebaseDataSourceTest  every <com.example.splitexpenses.data.source.FirebaseDataSourceTest  first <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getASSERTEquals <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
getASSERTNull <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getAWAIT <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getAssertEquals <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
getAssertNull <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getAwait <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
getCOEvery <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getCOVerify <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
getCoEvery <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getCoVerify <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getEMPTYList <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getEVERY <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getEmptyList <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getEvery <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getFIRST <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getFirst <com.example.splitexpenses.data.source.FirebaseDataSourceTest  	getLAUNCH <com.example.splitexpenses.data.source.FirebaseDataSourceTest  	getLISTOf <com.example.splitexpenses.data.source.FirebaseDataSourceTest  	getLaunch <com.example.splitexpenses.data.source.FirebaseDataSourceTest  	getListOf <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getMOCKK <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getMockk <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
getRETURNS <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
getRUNTest <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
getReturns <com.example.splitexpenses.data.source.FirebaseDataSourceTest  
getRunTest <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getSLOT <com.example.splitexpenses.data.source.FirebaseDataSourceTest  getSlot <com.example.splitexpenses.data.source.FirebaseDataSourceTest  groupReference <com.example.splitexpenses.data.source.FirebaseDataSourceTest  groupsReference <com.example.splitexpenses.data.source.FirebaseDataSourceTest  java <com.example.splitexpenses.data.source.FirebaseDataSourceTest  launch <com.example.splitexpenses.data.source.FirebaseDataSourceTest  listOf <com.example.splitexpenses.data.source.FirebaseDataSourceTest  mockk <com.example.splitexpenses.data.source.FirebaseDataSourceTest  returns <com.example.splitexpenses.data.source.FirebaseDataSourceTest  runTest <com.example.splitexpenses.data.source.FirebaseDataSourceTest  slot <com.example.splitexpenses.data.source.FirebaseDataSourceTest  	testGroup <com.example.splitexpenses.data.source.FirebaseDataSourceTest  testGroupId <com.example.splitexpenses.data.source.FirebaseDataSourceTest  saveUserForGroup 5com.example.splitexpenses.data.source.LocalDataSource  
addExpense 7com.example.splitexpenses.data.source.OfflineDataSource  
cacheGroup 7com.example.splitexpenses.data.source.OfflineDataSource  	saveGroup 7com.example.splitexpenses.data.source.OfflineDataSource  SyncQueueManager #com.example.splitexpenses.data.sync  queueExpenseOperation 4com.example.splitexpenses.data.sync.SyncQueueManager  queueGroupOperation 4com.example.splitexpenses.data.sync.SyncQueueManager  Expense 'com.example.splitexpenses.ui.viewmodels  ExpenseListViewModel 'com.example.splitexpenses.ui.viewmodels  ExpenseListViewModelTest 'com.example.splitexpenses.ui.viewmodels  	GroupData 'com.example.splitexpenses.ui.viewmodels  GroupListViewModel 'com.example.splitexpenses.ui.viewmodels  GroupListViewModelTest 'com.example.splitexpenses.ui.viewmodels  StandardTestDispatcher 'com.example.splitexpenses.ui.viewmodels  String 'com.example.splitexpenses.ui.viewmodels  assertEquals 'com.example.splitexpenses.ui.viewmodels  assertFalse 'com.example.splitexpenses.ui.viewmodels  coVerify 'com.example.splitexpenses.ui.viewmodels  	emptyList 'com.example.splitexpenses.ui.viewmodels  emptySet 'com.example.splitexpenses.ui.viewmodels  expenseRepository 'com.example.splitexpenses.ui.viewmodels  groupRepository 'com.example.splitexpenses.ui.viewmodels  listOf 'com.example.splitexpenses.ui.viewmodels  runTest 'com.example.splitexpenses.ui.viewmodels  setOf 'com.example.splitexpenses.ui.viewmodels  testDispatcher 'com.example.splitexpenses.ui.viewmodels  testGroupId 'com.example.splitexpenses.ui.viewmodels  	viewModel 'com.example.splitexpenses.ui.viewmodels  
addExpense 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  createGroup 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  deleteCurrentGroup 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  
deleteExpense 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  deleteExpenses 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  deleteGroups 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  	joinGroup 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  setMultiSelectMode 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  toggleExpenseSelection 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  toggleGroupSelection 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  
updateExpense 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  updateGroupMembers 5com.example.splitexpenses.ui.viewmodels.BaseViewModel  isMultiSelectMode :com.example.splitexpenses.ui.viewmodels.ExpenseListUiState  selectedExpenses :com.example.splitexpenses.ui.viewmodels.ExpenseListUiState  
addExpense <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  deleteCurrentGroup <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  
deleteExpense <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  deleteExpenses <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  setMultiSelectMode <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  toggleExpenseSelection <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  uiState <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  
updateExpense <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  updateGroupMembers <com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel  After @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  Before @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  Expense @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  ExpenseListViewModel @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  ExpenseRepository @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  	GroupData @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  GroupRepository @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  StandardTestDispatcher @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  String @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  Test @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  assertEquals @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  assertFalse @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  coVerify @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  emptySet @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  expenseRepository @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getASSERTEquals @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getASSERTFalse @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getAssertEquals @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getAssertFalse @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getCOVerify @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getCoVerify @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getEMPTYSet @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getEmptySet @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  	getLISTOf @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  	getListOf @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  
getRUNTest @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  
getRunTest @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getSETOf @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  getSetOf @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  groupRepository @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  listOf @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  runTest @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  setOf @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  testDispatcher @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  testGroupId @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  	viewModel @com.example.splitexpenses.ui.viewmodels.ExpenseListViewModelTest  isMultiSelectMode 8com.example.splitexpenses.ui.viewmodels.GroupListUiState  selectedGroups 8com.example.splitexpenses.ui.viewmodels.GroupListUiState  createGroup :com.example.splitexpenses.ui.viewmodels.GroupListViewModel  deleteGroups :com.example.splitexpenses.ui.viewmodels.GroupListViewModel  	joinGroup :com.example.splitexpenses.ui.viewmodels.GroupListViewModel  setMultiSelectMode :com.example.splitexpenses.ui.viewmodels.GroupListViewModel  toggleGroupSelection :com.example.splitexpenses.ui.viewmodels.GroupListViewModel  uiState :com.example.splitexpenses.ui.viewmodels.GroupListViewModel  After >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  Before >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  	GroupData >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  GroupListViewModel >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  GroupRepository >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  StandardTestDispatcher >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  String >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  Test >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  assertEquals >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  assertFalse >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  coVerify >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  	emptyList >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  emptySet >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getASSERTEquals >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getASSERTFalse >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getAssertEquals >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getAssertFalse >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getCOVerify >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getCoVerify >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getEMPTYList >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getEMPTYSet >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getEmptyList >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getEmptySet >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  	getLISTOf >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  	getListOf >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  
getRUNTest >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  
getRunTest >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getSETOf >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  getSetOf >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  groupRepository >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  listOf >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  runTest >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  setOf >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  testDispatcher >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  	viewModel >com.example.splitexpenses.ui.viewmodels.GroupListViewModelTest  
addExpense =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  createGroup =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  deleteCurrentGroup =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  
deleteExpense =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  deleteExpenses =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  deleteGroups =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  	joinGroup =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  setMultiSelectMode =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  toggleExpenseSelection =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  toggleGroupSelection =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  
updateExpense =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  updateGroupMembers =com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel  CsvUtil com.example.splitexpenses.util  CsvUtilTest com.example.splitexpenses.util  exportGroupToCsv &com.example.splitexpenses.util.CsvUtil  Before *com.example.splitexpenses.util.CsvUtilTest  Expense *com.example.splitexpenses.util.CsvUtilTest  	GroupData *com.example.splitexpenses.util.CsvUtilTest  Test *com.example.splitexpenses.util.CsvUtilTest  await !com.google.android.gms.tasks.Task  getAWAIT !com.google.android.gms.tasks.Task  getAwait !com.google.android.gms.tasks.Task  DataSnapshot com.google.firebase.database  
DatabaseError com.google.firebase.database  DatabaseReference com.google.firebase.database  ValueEventListener com.google.firebase.database  children )com.google.firebase.database.DataSnapshot  getCHILDREN )com.google.firebase.database.DataSnapshot  getChildren )com.google.firebase.database.DataSnapshot  getValue )com.google.firebase.database.DataSnapshot  setChildren )com.google.firebase.database.DataSnapshot  addValueEventListener .com.google.firebase.database.DatabaseReference  child .com.google.firebase.database.DatabaseReference  get .com.google.firebase.database.DatabaseReference  removeValue .com.google.firebase.database.DatabaseReference  setValue .com.google.firebase.database.DatabaseReference  addValueEventListener "com.google.firebase.database.Query  child "com.google.firebase.database.Query  get "com.google.firebase.database.Query  removeValue "com.google.firebase.database.Query  setValue "com.google.firebase.database.Query  onDataChange /com.google.firebase.database.ValueEventListener  Call io.mockk  
CapturingSlot io.mockk  MockKAdditionalAnswerScope io.mockk  MockKAnswerScope io.mockk  MockKMatcherScope io.mockk  MockKStubScope io.mockk  MockKVerificationScope io.mockk  coEvery io.mockk  coVerify io.mockk  every io.mockk  mockk io.mockk  slot io.mockk  verify io.mockk  captured io.mockk.CapturingSlot  getMOCKK io.mockk.MockKAnswerScope  getMockk io.mockk.MockKAnswerScope  mockk io.mockk.MockKAnswerScope  	GroupData io.mockk.MockKMatcherScope  UUID io.mockk.MockKMatcherScope  any io.mockk.MockKMatcherScope  await io.mockk.MockKMatcherScope  capture io.mockk.MockKMatcherScope  com io.mockk.MockKMatcherScope  currentUser io.mockk.MockKMatcherScope  
dataSource io.mockk.MockKMatcherScope  getAWAIT io.mockk.MockKMatcherScope  getAwait io.mockk.MockKMatcherScope  getCOM io.mockk.MockKMatcherScope  getCom io.mockk.MockKMatcherScope  
getDATASource io.mockk.MockKMatcherScope  
getDataSource io.mockk.MockKMatcherScope  getGROUPReference io.mockk.MockKMatcherScope  getGROUPSReference io.mockk.MockKMatcherScope  getGroupReference io.mockk.MockKMatcherScope  getGroupsReference io.mockk.MockKMatcherScope  getNETWORKConnectivityManager io.mockk.MockKMatcherScope  getNetworkConnectivityManager io.mockk.MockKMatcherScope  getOFFLINEDataSource io.mockk.MockKMatcherScope  getOfflineDataSource io.mockk.MockKMatcherScope  getREMOTEDataSource io.mockk.MockKMatcherScope  getRemoteDataSource io.mockk.MockKMatcherScope  getSYNCQueueManager io.mockk.MockKMatcherScope  getSyncQueueManager io.mockk.MockKMatcherScope  getTESTGroupId io.mockk.MockKMatcherScope  getTestGroupId io.mockk.MockKMatcherScope  groupReference io.mockk.MockKMatcherScope  groupsReference io.mockk.MockKMatcherScope  java io.mockk.MockKMatcherScope  listOf io.mockk.MockKMatcherScope  match io.mockk.MockKMatcherScope  networkConnectivityManager io.mockk.MockKMatcherScope  offlineDataSource io.mockk.MockKMatcherScope  remoteDataSource io.mockk.MockKMatcherScope  syncQueueManager io.mockk.MockKMatcherScope  testGroupId io.mockk.MockKMatcherScope  answers io.mockk.MockKStubScope  
getRETURNS io.mockk.MockKStubScope  
getReturns io.mockk.MockKStubScope  returns io.mockk.MockKStubScope  any io.mockk.MockKVerificationScope  await io.mockk.MockKVerificationScope  com io.mockk.MockKVerificationScope  currentUser io.mockk.MockKVerificationScope  
dataSource io.mockk.MockKVerificationScope  expenseRepository io.mockk.MockKVerificationScope  getAWAIT io.mockk.MockKVerificationScope  getAwait io.mockk.MockKVerificationScope  getCOM io.mockk.MockKVerificationScope  getCURRENTUser io.mockk.MockKVerificationScope  getCom io.mockk.MockKVerificationScope  getCurrentUser io.mockk.MockKVerificationScope  
getDATASource io.mockk.MockKVerificationScope  
getDataSource io.mockk.MockKVerificationScope  getEXPENSERepository io.mockk.MockKVerificationScope  getExpenseRepository io.mockk.MockKVerificationScope  getGROUPReference io.mockk.MockKVerificationScope  getGROUPRepository io.mockk.MockKVerificationScope  getGroupReference io.mockk.MockKVerificationScope  getGroupRepository io.mockk.MockKVerificationScope  	getLISTOf io.mockk.MockKVerificationScope  getLOCALDataSource io.mockk.MockKVerificationScope  	getListOf io.mockk.MockKVerificationScope  getLocalDataSource io.mockk.MockKVerificationScope  getOFFLINEDataSource io.mockk.MockKVerificationScope  getOfflineDataSource io.mockk.MockKVerificationScope  getREMOTEDataSource io.mockk.MockKVerificationScope  getRemoteDataSource io.mockk.MockKVerificationScope  getSYNCQueueManager io.mockk.MockKVerificationScope  getSyncQueueManager io.mockk.MockKVerificationScope  getTESTDeviceUid io.mockk.MockKVerificationScope  getTESTGroup io.mockk.MockKVerificationScope  getTESTGroupId io.mockk.MockKVerificationScope  getTestDeviceUid io.mockk.MockKVerificationScope  getTestGroup io.mockk.MockKVerificationScope  getTestGroupId io.mockk.MockKVerificationScope  groupReference io.mockk.MockKVerificationScope  groupRepository io.mockk.MockKVerificationScope  listOf io.mockk.MockKVerificationScope  localDataSource io.mockk.MockKVerificationScope  match io.mockk.MockKVerificationScope  offlineDataSource io.mockk.MockKVerificationScope  remoteDataSource io.mockk.MockKVerificationScope  syncQueueManager io.mockk.MockKVerificationScope  
testDeviceUid io.mockk.MockKVerificationScope  	testGroup io.mockk.MockKVerificationScope  testGroupId io.mockk.MockKVerificationScope  ByteArrayInputStream java.io  ByteArrayOutputStream java.io  ByteArrayOutputStream 	java.lang  Class 	java.lang  Expense 	java.lang  	GroupData 	java.lang  GroupRepository 	java.lang  IllegalStateException 	java.lang  MutableStateFlow 	java.lang  StandardTestDispatcher 	java.lang  UUID 	java.lang  Unit 	java.lang  assert 	java.lang  assertEquals 	java.lang  assertFalse 	java.lang  
assertNull 	java.lang  await 	java.lang  coEvery 	java.lang  coVerify 	java.lang  com 	java.lang  contains 	java.lang  createTestExpense 	java.lang  createTestGroup 	java.lang  currentUser 	java.lang  
dataSource 	java.lang  	emptyList 	java.lang  emptySet 	java.lang  every 	java.lang  expenseRepository 	java.lang  first 	java.lang  flowOf 	java.lang  groupReference 	java.lang  groupRepository 	java.lang  groupsReference 	java.lang  java 	java.lang  launch 	java.lang  listOf 	java.lang  localDataSource 	java.lang  mapOf 	java.lang  mockk 	java.lang  mockkStatic 	java.lang  networkConnectivityManager 	java.lang  offlineDataSource 	java.lang  remoteDataSource 	java.lang  
repository 	java.lang  returns 	java.lang  runTest 	java.lang  setOf 	java.lang  slot 	java.lang  syncQueueManager 	java.lang  
testDeviceUid 	java.lang  testDispatcher 	java.lang  	testGroup 	java.lang  testGroupId 	java.lang  to 	java.lang  verify 	java.lang  	viewModel 	java.lang  getDeclaredField java.lang.Class  message java.lang.IllegalStateException  Field java.lang.reflect  set "java.lang.reflect.AccessibleObject  getISAccessible java.lang.reflect.Field  getIsAccessible java.lang.reflect.Field  isAccessible java.lang.reflect.Field  set java.lang.reflect.Field  
setAccessible java.lang.reflect.Field  Date 	java.util  UUID 	java.util  
randomUUID java.util.UUID  toString java.util.UUID  Boolean kotlin  ByteArrayOutputStream kotlin  Double kotlin  Expense kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	GroupData kotlin  GroupRepository kotlin  IllegalStateException kotlin  Int kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  Pair kotlin  StandardTestDispatcher kotlin  String kotlin  UUID kotlin  Unit kotlin  assert kotlin  assertEquals kotlin  assertFalse kotlin  
assertNull kotlin  await kotlin  coEvery kotlin  coVerify kotlin  com kotlin  contains kotlin  createTestExpense kotlin  createTestGroup kotlin  currentUser kotlin  
dataSource kotlin  	emptyList kotlin  emptySet kotlin  every kotlin  expenseRepository kotlin  first kotlin  flowOf kotlin  groupReference kotlin  groupRepository kotlin  groupsReference kotlin  java kotlin  launch kotlin  listOf kotlin  localDataSource kotlin  mapOf kotlin  mockk kotlin  mockkStatic kotlin  networkConnectivityManager kotlin  offlineDataSource kotlin  remoteDataSource kotlin  
repository kotlin  returns kotlin  runTest kotlin  setOf kotlin  slot kotlin  syncQueueManager kotlin  
testDeviceUid kotlin  testDispatcher kotlin  	testGroup kotlin  testGroupId kotlin  to kotlin  verify kotlin  	viewModel kotlin  getCONTAINS 
kotlin.String  getContains 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  ByteArrayOutputStream kotlin.annotation  Expense kotlin.annotation  	GroupData kotlin.annotation  GroupRepository kotlin.annotation  IllegalStateException kotlin.annotation  MutableStateFlow kotlin.annotation  StandardTestDispatcher kotlin.annotation  UUID kotlin.annotation  Unit kotlin.annotation  assert kotlin.annotation  assertEquals kotlin.annotation  assertFalse kotlin.annotation  
assertNull kotlin.annotation  await kotlin.annotation  coEvery kotlin.annotation  coVerify kotlin.annotation  com kotlin.annotation  contains kotlin.annotation  createTestExpense kotlin.annotation  createTestGroup kotlin.annotation  currentUser kotlin.annotation  
dataSource kotlin.annotation  	emptyList kotlin.annotation  emptySet kotlin.annotation  every kotlin.annotation  expenseRepository kotlin.annotation  first kotlin.annotation  flowOf kotlin.annotation  groupReference kotlin.annotation  groupRepository kotlin.annotation  groupsReference kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  listOf kotlin.annotation  localDataSource kotlin.annotation  mapOf kotlin.annotation  mockk kotlin.annotation  mockkStatic kotlin.annotation  networkConnectivityManager kotlin.annotation  offlineDataSource kotlin.annotation  remoteDataSource kotlin.annotation  
repository kotlin.annotation  returns kotlin.annotation  runTest kotlin.annotation  setOf kotlin.annotation  slot kotlin.annotation  syncQueueManager kotlin.annotation  
testDeviceUid kotlin.annotation  testDispatcher kotlin.annotation  	testGroup kotlin.annotation  testGroupId kotlin.annotation  to kotlin.annotation  verify kotlin.annotation  	viewModel kotlin.annotation  ByteArrayOutputStream kotlin.collections  Expense kotlin.collections  	GroupData kotlin.collections  GroupRepository kotlin.collections  IllegalStateException kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterable kotlin.collections  MutableStateFlow kotlin.collections  Set kotlin.collections  StandardTestDispatcher kotlin.collections  UUID kotlin.collections  Unit kotlin.collections  assert kotlin.collections  assertEquals kotlin.collections  assertFalse kotlin.collections  
assertNull kotlin.collections  await kotlin.collections  coEvery kotlin.collections  coVerify kotlin.collections  com kotlin.collections  contains kotlin.collections  createTestExpense kotlin.collections  createTestGroup kotlin.collections  currentUser kotlin.collections  
dataSource kotlin.collections  	emptyList kotlin.collections  emptySet kotlin.collections  every kotlin.collections  expenseRepository kotlin.collections  first kotlin.collections  flowOf kotlin.collections  groupReference kotlin.collections  groupRepository kotlin.collections  groupsReference kotlin.collections  java kotlin.collections  launch kotlin.collections  listOf kotlin.collections  localDataSource kotlin.collections  mapOf kotlin.collections  mockk kotlin.collections  mockkStatic kotlin.collections  networkConnectivityManager kotlin.collections  offlineDataSource kotlin.collections  remoteDataSource kotlin.collections  
repository kotlin.collections  returns kotlin.collections  runTest kotlin.collections  setOf kotlin.collections  slot kotlin.collections  syncQueueManager kotlin.collections  
testDeviceUid kotlin.collections  testDispatcher kotlin.collections  	testGroup kotlin.collections  testGroupId kotlin.collections  to kotlin.collections  verify kotlin.collections  	viewModel kotlin.collections  ByteArrayOutputStream kotlin.comparisons  Expense kotlin.comparisons  	GroupData kotlin.comparisons  GroupRepository kotlin.comparisons  IllegalStateException kotlin.comparisons  MutableStateFlow kotlin.comparisons  StandardTestDispatcher kotlin.comparisons  UUID kotlin.comparisons  Unit kotlin.comparisons  assert kotlin.comparisons  assertEquals kotlin.comparisons  assertFalse kotlin.comparisons  
assertNull kotlin.comparisons  await kotlin.comparisons  coEvery kotlin.comparisons  coVerify kotlin.comparisons  com kotlin.comparisons  contains kotlin.comparisons  createTestExpense kotlin.comparisons  createTestGroup kotlin.comparisons  currentUser kotlin.comparisons  
dataSource kotlin.comparisons  	emptyList kotlin.comparisons  emptySet kotlin.comparisons  every kotlin.comparisons  expenseRepository kotlin.comparisons  first kotlin.comparisons  flowOf kotlin.comparisons  groupReference kotlin.comparisons  groupRepository kotlin.comparisons  groupsReference kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  listOf kotlin.comparisons  localDataSource kotlin.comparisons  mapOf kotlin.comparisons  mockk kotlin.comparisons  mockkStatic kotlin.comparisons  networkConnectivityManager kotlin.comparisons  offlineDataSource kotlin.comparisons  remoteDataSource kotlin.comparisons  
repository kotlin.comparisons  returns kotlin.comparisons  runTest kotlin.comparisons  setOf kotlin.comparisons  slot kotlin.comparisons  syncQueueManager kotlin.comparisons  
testDeviceUid kotlin.comparisons  testDispatcher kotlin.comparisons  	testGroup kotlin.comparisons  testGroupId kotlin.comparisons  to kotlin.comparisons  verify kotlin.comparisons  	viewModel kotlin.comparisons  SuspendFunction1 kotlin.coroutines  advanceUntilIdle 1kotlin.coroutines.AbstractCoroutineContextElement  ByteArrayOutputStream 	kotlin.io  Expense 	kotlin.io  	GroupData 	kotlin.io  GroupRepository 	kotlin.io  IllegalStateException 	kotlin.io  MutableStateFlow 	kotlin.io  StandardTestDispatcher 	kotlin.io  UUID 	kotlin.io  Unit 	kotlin.io  assert 	kotlin.io  assertEquals 	kotlin.io  assertFalse 	kotlin.io  
assertNull 	kotlin.io  await 	kotlin.io  coEvery 	kotlin.io  coVerify 	kotlin.io  com 	kotlin.io  contains 	kotlin.io  createTestExpense 	kotlin.io  createTestGroup 	kotlin.io  currentUser 	kotlin.io  
dataSource 	kotlin.io  	emptyList 	kotlin.io  emptySet 	kotlin.io  every 	kotlin.io  expenseRepository 	kotlin.io  first 	kotlin.io  flowOf 	kotlin.io  groupReference 	kotlin.io  groupRepository 	kotlin.io  groupsReference 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  listOf 	kotlin.io  localDataSource 	kotlin.io  mapOf 	kotlin.io  mockk 	kotlin.io  mockkStatic 	kotlin.io  networkConnectivityManager 	kotlin.io  offlineDataSource 	kotlin.io  remoteDataSource 	kotlin.io  
repository 	kotlin.io  returns 	kotlin.io  runTest 	kotlin.io  setOf 	kotlin.io  slot 	kotlin.io  syncQueueManager 	kotlin.io  
testDeviceUid 	kotlin.io  testDispatcher 	kotlin.io  	testGroup 	kotlin.io  testGroupId 	kotlin.io  to 	kotlin.io  verify 	kotlin.io  	viewModel 	kotlin.io  ByteArrayOutputStream 
kotlin.jvm  Expense 
kotlin.jvm  	GroupData 
kotlin.jvm  GroupRepository 
kotlin.jvm  IllegalStateException 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  StandardTestDispatcher 
kotlin.jvm  UUID 
kotlin.jvm  Unit 
kotlin.jvm  assert 
kotlin.jvm  assertEquals 
kotlin.jvm  assertFalse 
kotlin.jvm  
assertNull 
kotlin.jvm  await 
kotlin.jvm  coEvery 
kotlin.jvm  coVerify 
kotlin.jvm  com 
kotlin.jvm  contains 
kotlin.jvm  createTestExpense 
kotlin.jvm  createTestGroup 
kotlin.jvm  currentUser 
kotlin.jvm  
dataSource 
kotlin.jvm  	emptyList 
kotlin.jvm  emptySet 
kotlin.jvm  every 
kotlin.jvm  expenseRepository 
kotlin.jvm  first 
kotlin.jvm  flowOf 
kotlin.jvm  groupReference 
kotlin.jvm  groupRepository 
kotlin.jvm  groupsReference 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  listOf 
kotlin.jvm  localDataSource 
kotlin.jvm  mapOf 
kotlin.jvm  mockk 
kotlin.jvm  mockkStatic 
kotlin.jvm  networkConnectivityManager 
kotlin.jvm  offlineDataSource 
kotlin.jvm  remoteDataSource 
kotlin.jvm  
repository 
kotlin.jvm  returns 
kotlin.jvm  runTest 
kotlin.jvm  setOf 
kotlin.jvm  slot 
kotlin.jvm  syncQueueManager 
kotlin.jvm  
testDeviceUid 
kotlin.jvm  testDispatcher 
kotlin.jvm  	testGroup 
kotlin.jvm  testGroupId 
kotlin.jvm  to 
kotlin.jvm  verify 
kotlin.jvm  	viewModel 
kotlin.jvm  ByteArrayOutputStream 
kotlin.ranges  Expense 
kotlin.ranges  	GroupData 
kotlin.ranges  GroupRepository 
kotlin.ranges  IllegalStateException 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  StandardTestDispatcher 
kotlin.ranges  UUID 
kotlin.ranges  Unit 
kotlin.ranges  assert 
kotlin.ranges  assertEquals 
kotlin.ranges  assertFalse 
kotlin.ranges  
assertNull 
kotlin.ranges  await 
kotlin.ranges  coEvery 
kotlin.ranges  coVerify 
kotlin.ranges  com 
kotlin.ranges  contains 
kotlin.ranges  createTestExpense 
kotlin.ranges  createTestGroup 
kotlin.ranges  currentUser 
kotlin.ranges  
dataSource 
kotlin.ranges  	emptyList 
kotlin.ranges  emptySet 
kotlin.ranges  every 
kotlin.ranges  expenseRepository 
kotlin.ranges  first 
kotlin.ranges  flowOf 
kotlin.ranges  groupReference 
kotlin.ranges  groupRepository 
kotlin.ranges  groupsReference 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  listOf 
kotlin.ranges  localDataSource 
kotlin.ranges  mapOf 
kotlin.ranges  mockk 
kotlin.ranges  mockkStatic 
kotlin.ranges  networkConnectivityManager 
kotlin.ranges  offlineDataSource 
kotlin.ranges  remoteDataSource 
kotlin.ranges  
repository 
kotlin.ranges  returns 
kotlin.ranges  runTest 
kotlin.ranges  setOf 
kotlin.ranges  slot 
kotlin.ranges  syncQueueManager 
kotlin.ranges  
testDeviceUid 
kotlin.ranges  testDispatcher 
kotlin.ranges  	testGroup 
kotlin.ranges  testGroupId 
kotlin.ranges  to 
kotlin.ranges  verify 
kotlin.ranges  	viewModel 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ByteArrayOutputStream kotlin.sequences  Expense kotlin.sequences  	GroupData kotlin.sequences  GroupRepository kotlin.sequences  IllegalStateException kotlin.sequences  MutableStateFlow kotlin.sequences  StandardTestDispatcher kotlin.sequences  UUID kotlin.sequences  Unit kotlin.sequences  assert kotlin.sequences  assertEquals kotlin.sequences  assertFalse kotlin.sequences  
assertNull kotlin.sequences  await kotlin.sequences  coEvery kotlin.sequences  coVerify kotlin.sequences  com kotlin.sequences  contains kotlin.sequences  createTestExpense kotlin.sequences  createTestGroup kotlin.sequences  currentUser kotlin.sequences  
dataSource kotlin.sequences  	emptyList kotlin.sequences  emptySet kotlin.sequences  every kotlin.sequences  expenseRepository kotlin.sequences  first kotlin.sequences  flowOf kotlin.sequences  groupReference kotlin.sequences  groupRepository kotlin.sequences  groupsReference kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  listOf kotlin.sequences  localDataSource kotlin.sequences  mapOf kotlin.sequences  mockk kotlin.sequences  mockkStatic kotlin.sequences  networkConnectivityManager kotlin.sequences  offlineDataSource kotlin.sequences  remoteDataSource kotlin.sequences  
repository kotlin.sequences  returns kotlin.sequences  runTest kotlin.sequences  setOf kotlin.sequences  slot kotlin.sequences  syncQueueManager kotlin.sequences  
testDeviceUid kotlin.sequences  testDispatcher kotlin.sequences  	testGroup kotlin.sequences  testGroupId kotlin.sequences  to kotlin.sequences  verify kotlin.sequences  	viewModel kotlin.sequences  ByteArrayOutputStream kotlin.text  Expense kotlin.text  	GroupData kotlin.text  GroupRepository kotlin.text  IllegalStateException kotlin.text  MutableStateFlow kotlin.text  StandardTestDispatcher kotlin.text  UUID kotlin.text  Unit kotlin.text  assert kotlin.text  assertEquals kotlin.text  assertFalse kotlin.text  
assertNull kotlin.text  await kotlin.text  coEvery kotlin.text  coVerify kotlin.text  com kotlin.text  contains kotlin.text  createTestExpense kotlin.text  createTestGroup kotlin.text  currentUser kotlin.text  
dataSource kotlin.text  	emptyList kotlin.text  emptySet kotlin.text  every kotlin.text  expenseRepository kotlin.text  first kotlin.text  flowOf kotlin.text  groupReference kotlin.text  groupRepository kotlin.text  groupsReference kotlin.text  java kotlin.text  launch kotlin.text  listOf kotlin.text  localDataSource kotlin.text  mapOf kotlin.text  mockk kotlin.text  mockkStatic kotlin.text  networkConnectivityManager kotlin.text  offlineDataSource kotlin.text  remoteDataSource kotlin.text  
repository kotlin.text  returns kotlin.text  runTest kotlin.text  setOf kotlin.text  slot kotlin.text  syncQueueManager kotlin.text  
testDeviceUid kotlin.text  testDispatcher kotlin.text  	testGroup kotlin.text  testGroupId kotlin.text  to kotlin.text  verify kotlin.text  	viewModel kotlin.text  Dispatchers kotlinx.coroutines  ExperimentalCoroutinesApi kotlinx.coroutines  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  first kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  getFIRST kotlinx.coroutines.flow.Flow  getFirst kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  first !kotlinx.coroutines.flow.StateFlow  getFIRST !kotlinx.coroutines.flow.StateFlow  getFirst !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  StandardTestDispatcher kotlinx.coroutines.test  TestDispatcher kotlinx.coroutines.test  	TestScope kotlinx.coroutines.test  	resetMain kotlinx.coroutines.test  runTest kotlinx.coroutines.test  setMain kotlinx.coroutines.test  advanceUntilIdle .kotlinx.coroutines.test.TestCoroutineScheduler  	scheduler &kotlinx.coroutines.test.TestDispatcher  ByteArrayOutputStream !kotlinx.coroutines.test.TestScope  Expense !kotlinx.coroutines.test.TestScope  	GroupData !kotlinx.coroutines.test.TestScope  GroupRepository !kotlinx.coroutines.test.TestScope  MutableStateFlow !kotlinx.coroutines.test.TestScope  UUID !kotlinx.coroutines.test.TestScope  Unit !kotlinx.coroutines.test.TestScope  assert !kotlinx.coroutines.test.TestScope  assertEquals !kotlinx.coroutines.test.TestScope  assertFalse !kotlinx.coroutines.test.TestScope  
assertNull !kotlinx.coroutines.test.TestScope  await !kotlinx.coroutines.test.TestScope  coEvery !kotlinx.coroutines.test.TestScope  coVerify !kotlinx.coroutines.test.TestScope  com !kotlinx.coroutines.test.TestScope  contains !kotlinx.coroutines.test.TestScope  createTestExpense !kotlinx.coroutines.test.TestScope  createTestGroup !kotlinx.coroutines.test.TestScope  currentUser !kotlinx.coroutines.test.TestScope  
dataSource !kotlinx.coroutines.test.TestScope  emptySet !kotlinx.coroutines.test.TestScope  every !kotlinx.coroutines.test.TestScope  expenseRepository !kotlinx.coroutines.test.TestScope  first !kotlinx.coroutines.test.TestScope  flowOf !kotlinx.coroutines.test.TestScope  	getASSERT !kotlinx.coroutines.test.TestScope  getASSERTEquals !kotlinx.coroutines.test.TestScope  getASSERTFalse !kotlinx.coroutines.test.TestScope  
getASSERTNull !kotlinx.coroutines.test.TestScope  getAWAIT !kotlinx.coroutines.test.TestScope  	getAssert !kotlinx.coroutines.test.TestScope  getAssertEquals !kotlinx.coroutines.test.TestScope  getAssertFalse !kotlinx.coroutines.test.TestScope  
getAssertNull !kotlinx.coroutines.test.TestScope  getAwait !kotlinx.coroutines.test.TestScope  
getCOEvery !kotlinx.coroutines.test.TestScope  getCOM !kotlinx.coroutines.test.TestScope  getCONTAINS !kotlinx.coroutines.test.TestScope  getCOVerify !kotlinx.coroutines.test.TestScope  getCREATETestExpense !kotlinx.coroutines.test.TestScope  getCREATETestGroup !kotlinx.coroutines.test.TestScope  getCURRENTUser !kotlinx.coroutines.test.TestScope  
getCoEvery !kotlinx.coroutines.test.TestScope  getCoVerify !kotlinx.coroutines.test.TestScope  getCom !kotlinx.coroutines.test.TestScope  getContains !kotlinx.coroutines.test.TestScope  getCreateTestExpense !kotlinx.coroutines.test.TestScope  getCreateTestGroup !kotlinx.coroutines.test.TestScope  getCurrentUser !kotlinx.coroutines.test.TestScope  
getDATASource !kotlinx.coroutines.test.TestScope  
getDataSource !kotlinx.coroutines.test.TestScope  getEMPTYSet !kotlinx.coroutines.test.TestScope  getEVERY !kotlinx.coroutines.test.TestScope  getEXPENSERepository !kotlinx.coroutines.test.TestScope  getEmptySet !kotlinx.coroutines.test.TestScope  getEvery !kotlinx.coroutines.test.TestScope  getExpenseRepository !kotlinx.coroutines.test.TestScope  getFIRST !kotlinx.coroutines.test.TestScope  	getFLOWOf !kotlinx.coroutines.test.TestScope  getFirst !kotlinx.coroutines.test.TestScope  	getFlowOf !kotlinx.coroutines.test.TestScope  getGROUPReference !kotlinx.coroutines.test.TestScope  getGROUPRepository !kotlinx.coroutines.test.TestScope  getGROUPSReference !kotlinx.coroutines.test.TestScope  getGroupReference !kotlinx.coroutines.test.TestScope  getGroupRepository !kotlinx.coroutines.test.TestScope  getGroupsReference !kotlinx.coroutines.test.TestScope  	getLAUNCH !kotlinx.coroutines.test.TestScope  	getLISTOf !kotlinx.coroutines.test.TestScope  getLOCALDataSource !kotlinx.coroutines.test.TestScope  	getLaunch !kotlinx.coroutines.test.TestScope  	getListOf !kotlinx.coroutines.test.TestScope  getLocalDataSource !kotlinx.coroutines.test.TestScope  getMOCKK !kotlinx.coroutines.test.TestScope  getMOCKKStatic !kotlinx.coroutines.test.TestScope  getMockk !kotlinx.coroutines.test.TestScope  getMockkStatic !kotlinx.coroutines.test.TestScope  getNETWORKConnectivityManager !kotlinx.coroutines.test.TestScope  getNetworkConnectivityManager !kotlinx.coroutines.test.TestScope  getOFFLINEDataSource !kotlinx.coroutines.test.TestScope  getOfflineDataSource !kotlinx.coroutines.test.TestScope  getREMOTEDataSource !kotlinx.coroutines.test.TestScope  
getREPOSITORY !kotlinx.coroutines.test.TestScope  
getRETURNS !kotlinx.coroutines.test.TestScope  getRemoteDataSource !kotlinx.coroutines.test.TestScope  
getRepository !kotlinx.coroutines.test.TestScope  
getReturns !kotlinx.coroutines.test.TestScope  getSETOf !kotlinx.coroutines.test.TestScope  getSLOT !kotlinx.coroutines.test.TestScope  getSYNCQueueManager !kotlinx.coroutines.test.TestScope  getSetOf !kotlinx.coroutines.test.TestScope  getSlot !kotlinx.coroutines.test.TestScope  getSyncQueueManager !kotlinx.coroutines.test.TestScope  getTESTDeviceUid !kotlinx.coroutines.test.TestScope  getTESTDispatcher !kotlinx.coroutines.test.TestScope  getTESTGroup !kotlinx.coroutines.test.TestScope  getTESTGroupId !kotlinx.coroutines.test.TestScope  getTestDeviceUid !kotlinx.coroutines.test.TestScope  getTestDispatcher !kotlinx.coroutines.test.TestScope  getTestGroup !kotlinx.coroutines.test.TestScope  getTestGroupId !kotlinx.coroutines.test.TestScope  	getVERIFY !kotlinx.coroutines.test.TestScope  getVIEWModel !kotlinx.coroutines.test.TestScope  	getVerify !kotlinx.coroutines.test.TestScope  getViewModel !kotlinx.coroutines.test.TestScope  groupReference !kotlinx.coroutines.test.TestScope  groupRepository !kotlinx.coroutines.test.TestScope  groupsReference !kotlinx.coroutines.test.TestScope  java !kotlinx.coroutines.test.TestScope  launch !kotlinx.coroutines.test.TestScope  listOf !kotlinx.coroutines.test.TestScope  localDataSource !kotlinx.coroutines.test.TestScope  mockk !kotlinx.coroutines.test.TestScope  mockkStatic !kotlinx.coroutines.test.TestScope  networkConnectivityManager !kotlinx.coroutines.test.TestScope  offlineDataSource !kotlinx.coroutines.test.TestScope  remoteDataSource !kotlinx.coroutines.test.TestScope  
repository !kotlinx.coroutines.test.TestScope  returns !kotlinx.coroutines.test.TestScope  setOf !kotlinx.coroutines.test.TestScope  slot !kotlinx.coroutines.test.TestScope  syncQueueManager !kotlinx.coroutines.test.TestScope  
testDeviceUid !kotlinx.coroutines.test.TestScope  testDispatcher !kotlinx.coroutines.test.TestScope  	testGroup !kotlinx.coroutines.test.TestScope  testGroupId !kotlinx.coroutines.test.TestScope  verify !kotlinx.coroutines.test.TestScope  	viewModel !kotlinx.coroutines.test.TestScope  After 	org.junit  Assert 	org.junit  Before 	org.junit  Test 	org.junit  assertEquals org.junit.Assert  assertFalse org.junit.Assert  
assertNull org.junit.Assert  
assertTrue org.junit.Assert                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             