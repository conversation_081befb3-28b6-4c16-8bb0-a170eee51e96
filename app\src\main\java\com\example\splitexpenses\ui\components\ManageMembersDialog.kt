package com.example.splitexpenses.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.res.painterResource
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ManageMembersDialog(
    group: GroupData,
    currentUser: String,
    onDismiss: () -> Unit,
    onSave: (List<String>) -> Unit,
    onAddMember: (String) -> Unit = { },
    onRemoveMember: (String) -> Unit = { },
    onAddAllowedUser: (String) -> Unit = {},
    onRemoveAllowedUser: (String) -> Unit = {},
    isCurrentUserGroupCreator: Boolean = false,
    getUidForUserInGroup: (String, String) -> String? = { _, _ -> null },
    getUserNameForUidInGroup: (String, String) -> String? = { _, _ -> null },
    onInviteClick: (String, String) -> Unit = { _, _ -> },
    onEditGroupName: () -> Unit = {},
    onEditMemberInfo: () -> Unit = {},
    getMemberAvatar: (String) -> String? = { null },
    isOffline: Boolean = false
) {
    // Force recomposition when group changes
    // This is the key to making the UI update when the group data changes
    val groupId = group.id
    val groupMembers = group.members

    // Debug print to see when the dialog is recomposed with new members
    println("ManageMembersDialog: Recomposing with group ID: $groupId, members: $groupMembers")

    var newMember by remember { mutableStateOf("") }
    var newAllowedUserUid by remember { mutableStateOf("") }
    var showError by remember { mutableStateOf(false) }
    var showAllowedUsersSection by remember { mutableStateOf(false) }

    AlertDialog(
        modifier = Modifier.padding(vertical = 56.dp),
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        onDismissRequest = onDismiss,
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Manage Group",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.primary
                )

                // Edit group name button - only visible for group creator and when online
                if (isCurrentUserGroupCreator && !isOffline) {
                    IconButton(
                        onClick = {
                            println("ManageMembersDialog: Edit group name button clicked")
                            onEditGroupName()
                        }
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Edit Group Name",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                } else if (isCurrentUserGroupCreator && isOffline) {
                    // Show disabled edit button when offline
                    IconButton(
                        onClick = { /* Do nothing when offline */ },
                        enabled = false
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Edit Group Name (Offline)",
                            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        )
                    }
                }
            }
        },
        text = {
            Column {
                // Add new member section
                Text(
                    text = "Add New Member",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = newMember,
                        onValueChange = {
                            newMember = it
                            showError = false
                        },
                        label = { Text("Member Name") },
                        modifier = Modifier.weight(1f),
                        isError = showError && newMember.isBlank(),
                        singleLine = true,
                        colors = TextFieldDefaults.colors(
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    )

                    IconButton(
                        onClick = {
                            if (newMember.isBlank()) {
                                showError = true
                                return@IconButton
                            }
                            // Check if member already exists
                            if (!groupMembers.contains(newMember)) {
                                println("ManageMembersDialog: Adding new member: $newMember")
                                try {
                                    // Call the onAddMember callback to update the database
                                    onAddMember(newMember)
                                    println("ManageMembersDialog: onAddMember called successfully")
                                } catch (e: Exception) {
                                    println("ManageMembersDialog: Error calling onAddMember: ${e.message}")
                                    e.printStackTrace()
                                }
                            } else {
                                println("ManageMembersDialog: Member already exists: $newMember")
                            }
                            newMember = ""
                        },
                        modifier = Modifier.padding(start = 8.dp)
                    ) {
                        Icon(
                            Icons.Default.Add,
                            contentDescription = "Add member",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Current members section with invite button
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Current Members",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    // Debug print
                    println("ManageMembersDialog: isCurrentUserGroupCreator = $isCurrentUserGroupCreator")

                    // Always show the invite button for debugging
                    Button(
                        onClick = {
                            println("ManageMembersDialog: Invite button clicked for group $groupId")
                            println("ManageMembersDialog: Group name: ${group.name}")
                            println("ManageMembersDialog: onInviteClick function: $onInviteClick")
                            try {
                                onInviteClick(groupId, group.name)
                                println("ManageMembersDialog: onInviteClick called successfully")
                            } catch (e: Exception) {
                                println("ManageMembersDialog: Error calling onInviteClick: ${e.message}")
                                e.printStackTrace()
                            }
                        },
                        modifier = Modifier.padding(start = 8.dp)
                    ) {
                        Text("Invite")
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(150.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    // Debug print to see the current members list
                    println("ManageMembersDialog: Rendering members list: $groupMembers")

                    // List of current members
                    groupMembers.forEach { member ->
                        key(member) { // Add key to ensure proper recomposition
                            Spacer(modifier = Modifier.height(4.dp))

                            // Check if this member is assigned to a UID
                            val isAssigned = member in group.memberUidMap.keys

                            Surface(
                            modifier = Modifier
                                .height(34.dp)
                                .fillMaxWidth(),
                            shape = MaterialTheme.shapes.small,
                            color = if (isAssigned)
                                MaterialTheme.colorScheme.inverseOnSurface
                            else
                                MaterialTheme.colorScheme.surfaceVariant

                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(4.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    // Use custom avatar if available, otherwise use default
                                    val avatar = getMemberAvatar(member)
                                    if (avatar != null) {
                                        Text(
                                            text = avatar,
                                            style = MaterialTheme.typography.titleMedium,
                                            modifier = Modifier.size(24.dp)
                                        )
                                    } else {
                                        Icon(
                                            painter = painterResource(id = R.drawable.account_outline),
                                            contentDescription = null,
                                            modifier = Modifier.size(20.dp),
                                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                    Column {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                                        ) {
                                            Text(
                                                text = member,
                                                style = MaterialTheme.typography.titleMedium
                                            )


                                        }

                                        if (!isAssigned) {
                                            Text(
                                                text = "Unassigned",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.primary
                                            )
                                        }
                                    }
                                }

                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                                ) {
                                    // Show star icon for the creator
                                    // First check the memberUidMap from the group data
                                    val memberUidFromMap = group.memberUidMap[member]
                                    val memberUidFromPrefs = getUidForUserInGroup(group.id, member)
                                    val memberUid = memberUidFromMap ?: memberUidFromPrefs

                                    // Debug logging
                                    println("ManageMembersDialog: Member: $member, UID from map: $memberUidFromMap, UID from prefs: $memberUidFromPrefs, Creator UID: ${group.creatorUid}, Is Creator: ${memberUid == group.creatorUid}")

                                    if (memberUid != null && memberUid == group.creatorUid) {
                                        Icon(
                                            painter = painterResource(id = R.drawable.crown),//imageVector = Icons.Default.Star,
                                            contentDescription = "Group Creator",
                                            tint = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier
                                                .size(16.dp)
                                        )
                                    }
                                    // If the member is the current user, show a badge and edit button
                                    if (member == currentUser) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                                        ) {
                                            Surface(
                                                shape = MaterialTheme.shapes.small,
                                                color = MaterialTheme.colorScheme.primaryContainer,
                                            ) {
                                                Text(
                                                    text = "You",
                                                    style = MaterialTheme.typography.labelSmall,
                                                    modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp),
                                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                                )
                                            }

                                            // Edit button for current user
                                            IconButton(
                                                onClick = {
                                                    if (!isOffline) {
                                                        println("ManageMembersDialog: Edit member info button clicked")
                                                        onEditMemberInfo()
                                                    }
                                                },
                                                modifier = Modifier.size(24.dp),
                                                enabled = !isOffline
                                            ) {
                                                Icon(
                                                    Icons.Default.Edit,
                                                    contentDescription = "Edit your information",
                                                    tint = if (isOffline)
                                                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                                    else
                                                        MaterialTheme.colorScheme.primary,
                                                    modifier = Modifier.size(16.dp)
                                                )
                                            }
                                        }
                                    } else if (isCurrentUserGroupCreator) {
                                        // Delete button for non-current users - only shown if the current user is the creator
                                        Surface(
                                            modifier = Modifier
                                                .width(32.dp)
                                                .height(32.dp)
                                                .clickable {
                                                    // Don't allow removing the last member
                                                    if (groupMembers.size > 1) {
                                                        println("ManageMembersDialog: Removing member: $member")
                                                        try {
                                                            // Call the onRemoveMember callback to update the database
                                                            // This will also kick the member from the group
                                                            onRemoveMember(member)
                                                            println("ManageMembersDialog: onRemoveMember called successfully")
                                                        } catch (e: Exception) {
                                                            println("ManageMembersDialog: Error calling onRemoveMember: ${e.message}")
                                                            e.printStackTrace()
                                                        }
                                                    } else {
                                                        println("ManageMembersDialog: Cannot remove last member: $member")
                                                    }
                                                },
                                            color = Color.Transparent
                                        ) {
                                            Box(
                                                modifier = Modifier.fillMaxSize(),
                                                contentAlignment = Alignment.Center
                                            ) {
                                                Icon(
                                                    Icons.Default.Delete,
                                                    contentDescription = "Remove member",
                                                    tint = MaterialTheme.colorScheme.error
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        } // Close the key block
                    }
                }
            }
        },
        confirmButton = {
            Button(onClick = {
                // We don't need to do anything here since changes are saved immediately
                // Just close the dialog
                onDismiss()
            }) {
                Text("Close")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}