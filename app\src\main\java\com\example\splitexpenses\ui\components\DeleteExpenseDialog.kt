package com.example.splitexpenses.ui.components

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp

/**
 * Dialog for confirming expense deletion
 * @param expenseDescription The description of the expense to delete
 * @param onDismiss Callback for when the dialog is dismissed
 * @param onConfirm Callback for when the deletion is confirmed
 */
@Composable
fun DeleteExpenseDialog(
    expenseDescription: String,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp,
        title = { Text("Delete Expense") },
        text = { Text("Are you sure you want to delete the expense \"$expenseDescription\"?") },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("Delete")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
