package com.example.splitexpenses.ui.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a%\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0011\u0010\u0004\u001a\r\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\u0006H\u0007\u001a:\u0010\u0007\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0013\b\u0002\u0010\b\u001a\r\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\u00062\u0011\u0010\u0004\u001a\r\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\u0006H\u0007\u00a8\u0006\t"}, d2 = {"DeferredContent", "", "delayMs", "", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "TransitionOptimizedContent", "placeholder", "app_debug"})
public final class AnimationUtilsKt {
    
    /**
     * Composable that defers heavy operations until after navigation animations complete
     * This helps prevent choppy animations by reducing CPU load during transitions
     */
    @androidx.compose.runtime.Composable()
    public static final void DeferredContent(long delayMs, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * Composable that provides a loading placeholder during navigation transitions
     * Helps maintain smooth animations by showing lightweight content initially
     */
    @androidx.compose.runtime.Composable()
    public static final void TransitionOptimizedContent(long delayMs, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> placeholder, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
}