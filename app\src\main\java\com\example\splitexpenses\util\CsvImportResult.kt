package com.example.splitexpenses.util

import com.example.splitexpenses.data.GroupData

/**
 * Class to hold the results of a CSV import operation
 */
data class CsvImportResult(
    val success: Boolean,
    val group: GroupData? = null,
    val errors: List<CsvImportError> = emptyList(),
    val warnings: List<CsvImportWarning> = emptyList(),
    val totalRowsProcessed: Int = 0,
    val successfulRows: Int = 0
) {
    val hasErrors: <PERSON>olean get() = errors.isNotEmpty()
    val hasWarnings: <PERSON>olean get() = warnings.isNotEmpty()
    
    /**
     * Get a summary of the import operation
     */
    fun getSummary(): String {
        return if (success) {
            "Successfully imported ${successfulRows}/${totalRowsProcessed} rows" +
                    (if (hasWarnings) " with ${warnings.size} warnings" else "")
        } else {
            "Import failed with ${errors.size} errors" +
                    (if (hasWarnings) " and ${warnings.size} warnings" else "")
        }
    }
    
    /**
     * Get detailed error and warning messages
     */
    fun getDetailedMessages(): String {
        val sb = StringBuilder()
        
        if (hasErrors) {
            sb.append("Errors:\n")
            errors.forEach { error ->
                sb.append("- ${error.message}\n")
            }
            sb.append("\n")
        }
        
        if (hasWarnings) {
            sb.append("Warnings:\n")
            warnings.forEach { warning ->
                sb.append("- ${warning.message}\n")
            }
        }
        
        return sb.toString()
    }
}

/**
 * Class to hold information about an error during CSV import
 */
data class CsvImportError(
    val message: String,
    val lineNumber: Int = -1,
    val fieldName: String = "",
    val fieldValue: String = "",
    val exception: Exception? = null
) {
    override fun toString(): String {
        val location = if (lineNumber > 0) {
            if (fieldName.isNotEmpty()) {
                "Line $lineNumber, Field '$fieldName'"
            } else {
                "Line $lineNumber"
            }
        } else {
            "Unknown location"
        }
        
        val value = if (fieldValue.isNotEmpty()) {
            " (Value: '$fieldValue')"
        } else {
            ""
        }
        
        return "$location: $message$value"
    }
}

/**
 * Class to hold information about a warning during CSV import
 */
data class CsvImportWarning(
    val message: String,
    val lineNumber: Int = -1,
    val fieldName: String = "",
    val fieldValue: String = "",
    val fixedValue: String = ""
) {
    override fun toString(): String {
        val location = if (lineNumber > 0) {
            if (fieldName.isNotEmpty()) {
                "Line $lineNumber, Field '$fieldName'"
            } else {
                "Line $lineNumber"
            }
        } else {
            "Unknown location"
        }
        
        val value = if (fieldValue.isNotEmpty()) {
            " (Original: '$fieldValue'"
        } else {
            ""
        }
        
        val fixed = if (fixedValue.isNotEmpty()) {
            if (value.isNotEmpty()) {
                ", Fixed: '$fixedValue')"
            } else {
                " (Fixed: '$fixedValue')"
            }
        } else if (value.isNotEmpty()) {
            ")"
        } else {
            ""
        }
        
        return "$location: $message$value$fixed"
    }
}
