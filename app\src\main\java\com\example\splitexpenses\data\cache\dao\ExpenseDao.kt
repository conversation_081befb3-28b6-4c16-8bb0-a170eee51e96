package com.example.splitexpenses.data.cache.dao

import androidx.room.*
import com.example.splitexpenses.data.cache.entities.ExpenseEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Expense entities
 */
@Dao
interface ExpenseDao {

    /**
     * Get all expenses for a specific group as Flow
     */
    @Query("SELECT * FROM expenses WHERE groupId = :groupId ORDER BY date DESC")
    fun getExpensesForGroupFlow(groupId: String): Flow<List<ExpenseEntity>>

    /**
     * Get all expenses for a specific group
     */
    @Query("SELECT * FROM expenses WHERE groupId = :groupId ORDER BY date DESC")
    suspend fun getExpensesForGroup(groupId: String): List<ExpenseEntity>

    /**
     * Get a specific expense by ID
     */
    @Query("SELECT * FROM expenses WHERE id = :expenseId")
    suspend fun getExpenseById(expenseId: String): ExpenseEntity?

    /**
     * Get expenses that are not synced
     */
    @Query("SELECT * FROM expenses WHERE isSynced = 0")
    suspend fun getUnsyncedExpenses(): List<ExpenseEntity>

    /**
     * Get unsynced expenses for a specific group
     */
    @Query("SELECT * FROM expenses WHERE groupId = :groupId AND isSynced = 0")
    suspend fun getUnsyncedExpensesForGroup(groupId: String): List<ExpenseEntity>

    /**
     * Insert or replace an expense
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertExpense(expense: ExpenseEntity)

    /**
     * Insert or replace multiple expenses
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertExpenses(expenses: List<ExpenseEntity>)

    /**
     * Update an expense
     */
    @Update
    suspend fun updateExpense(expense: ExpenseEntity)

    /**
     * Delete an expense
     */
    @Delete
    suspend fun deleteExpense(expense: ExpenseEntity)

    /**
     * Delete an expense by ID
     */
    @Query("DELETE FROM expenses WHERE id = :expenseId")
    suspend fun deleteExpenseById(expenseId: String)

    /**
     * Delete all expenses for a group
     */
    @Query("DELETE FROM expenses WHERE groupId = :groupId")
    suspend fun deleteExpensesForGroup(groupId: String)

    /**
     * Mark an expense as synced
     */
    @Query("UPDATE expenses SET isSynced = 1 WHERE id = :expenseId")
    suspend fun markExpenseAsSynced(expenseId: String)

    /**
     * Mark an expense as unsynced
     */
    @Query("UPDATE expenses SET isSynced = 0, lastModified = :timestamp WHERE id = :expenseId")
    suspend fun markExpenseAsUnsynced(expenseId: String, timestamp: Long = System.currentTimeMillis())

    /**
     * Clear all expenses
     */
    @Query("DELETE FROM expenses")
    suspend fun clearAllExpenses()
}
