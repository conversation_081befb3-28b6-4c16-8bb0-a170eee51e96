package com.example.splitexpenses.data.cache.dao;

/**
 * Data Access Object for Group entities
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\t\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00070\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\u0010H\'J\u0018\u0010\u0011\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0018\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u00102\u0006\u0010\n\u001a\u00020\u000bH\'J\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00070\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u0015\u001a\u00020\u00032\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00070\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010\u0018\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ \u0010\u0019\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u001cJ\u0016\u0010\u001d\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\u001e"}, d2 = {"Lcom/example/splitexpenses/data/cache/dao/GroupDao;", "", "clearAllGroups", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGroup", "group", "Lcom/example/splitexpenses/data/cache/entities/GroupEntity;", "(Lcom/example/splitexpenses/data/cache/entities/GroupEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGroupById", "groupId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllGroups", "", "getAllGroupsFlow", "Lkotlinx/coroutines/flow/Flow;", "getGroupById", "getGroupByIdFlow", "getUnsyncedGroups", "insertGroup", "insertGroups", "groups", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markGroupAsSynced", "markGroupAsUnsynced", "timestamp", "", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGroup", "app_debug"})
@androidx.room.Dao()
public abstract interface GroupDao {
    
    /**
     * Get all groups as a Flow for reactive updates
     */
    @androidx.room.Query(value = "SELECT * FROM groups ORDER BY lastModified DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.splitexpenses.data.cache.entities.GroupEntity>> getAllGroupsFlow();
    
    /**
     * Get all groups
     */
    @androidx.room.Query(value = "SELECT * FROM groups ORDER BY lastModified DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.GroupEntity>> $completion);
    
    /**
     * Get a specific group by ID
     */
    @androidx.room.Query(value = "SELECT * FROM groups WHERE id = :groupId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getGroupById(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.data.cache.entities.GroupEntity> $completion);
    
    /**
     * Get a specific group by ID as Flow
     */
    @androidx.room.Query(value = "SELECT * FROM groups WHERE id = :groupId")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.splitexpenses.data.cache.entities.GroupEntity> getGroupByIdFlow(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId);
    
    /**
     * Get groups that are not synced
     */
    @androidx.room.Query(value = "SELECT * FROM groups WHERE isSynced = 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUnsyncedGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.GroupEntity>> $completion);
    
    /**
     * Insert or replace a group
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertGroup(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.GroupEntity group, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Insert or replace multiple groups
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertGroups(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.cache.entities.GroupEntity> groups, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Update a group
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateGroup(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.GroupEntity group, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete a group
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteGroup(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.GroupEntity group, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete a group by ID
     */
    @androidx.room.Query(value = "DELETE FROM groups WHERE id = :groupId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteGroupById(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Mark a group as synced
     */
    @androidx.room.Query(value = "UPDATE groups SET isSynced = 1 WHERE id = :groupId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object markGroupAsSynced(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Mark a group as unsynced
     */
    @androidx.room.Query(value = "UPDATE groups SET isSynced = 0, lastModified = :timestamp WHERE id = :groupId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object markGroupAsUnsynced(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Clear all groups
     */
    @androidx.room.Query(value = "DELETE FROM groups")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Data Access Object for Group entities
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}