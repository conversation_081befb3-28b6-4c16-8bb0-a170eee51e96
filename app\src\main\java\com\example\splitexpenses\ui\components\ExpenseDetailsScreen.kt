package com.example.splitexpenses.ui.components

/**
 * ExpenseDetailsScreen displays the details of a single expense.
 *
 * Features:
 * - <PERSON>tal swipe gesture navigation between expenses
 * - Swipe right to see previous expense
 * - Swipe left to see next expense
 * - Animated drag that follows finger movement with next/previous expense sliding into view
 */

import androidx.compose.foundation.BorderStroke
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton

import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.example.splitexpenses.R
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.MaterialTheme
import kotlin.math.abs
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun ExpenseDetailsScreen(
    expense: Expense?,
    group: GroupData,
    onBackClick: () -> Unit,
    onEditClick: () -> Unit,
    onPreviousClick: () -> Unit,
    onNextClick: () -> Unit,
    hasPrevious: Boolean,
    hasNext: Boolean,
    onNavigateToExpense: ((String) -> Unit)? = null,
    isOffline: Boolean = false
) {
    // Get the current expense index in the full expense list
    val currentIndex = group.expenses.indexOf(expense)

    // If expense not found, show error or return
    if (currentIndex == -1 || expense == null) {
        // Handle error case - expense not found
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text("Expense not found")
        }
        return
    }

    // Create pager state with the full expense list size
    val pagerState = rememberPagerState(
        initialPage = currentIndex,
        pageCount = { group.expenses.size }
    )

    // Handle navigation when page changes
    LaunchedEffect(pagerState.currentPage, pagerState.isScrollInProgress) {
        // Only trigger navigation when scrolling has completely stopped and we're on a different page
        if (!pagerState.isScrollInProgress && pagerState.currentPage != currentIndex) {
            // Add a small delay to ensure scrolling has completely settled
            delay(100)

            // Double-check that we're still not scrolling and on the same page
            if (!pagerState.isScrollInProgress && pagerState.currentPage != currentIndex) {
                val targetExpense = group.expenses.getOrNull(pagerState.currentPage)
                if (targetExpense != null) {
                    // Use direct navigation if available, otherwise fall back to relative navigation
                    if (onNavigateToExpense != null) {
                        onNavigateToExpense(targetExpense.id)
                    } else {
                        // Fallback to relative navigation for backward compatibility
                        if (pagerState.currentPage < currentIndex) {
                            onPreviousClick()
                        } else if (pagerState.currentPage > currentIndex) {
                            onNextClick()
                        }
                    }
                }
            }
        }
    }

    // Create coroutine scope for pager navigation
    val coroutineScope = rememberCoroutineScope()

    Column(modifier = Modifier.fillMaxSize()) {
        // Fixed Header - stays stationary during paging
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
            }
            Text(
                text = "Expense Details",
                style = MaterialTheme.typography.headlineLarge,
                color = MaterialTheme.colorScheme.primary
            )
            IconButton(
                onClick = {
                    if (!isOffline) {
                        onEditClick()
                    }
                },
                enabled = !isOffline
            ) {
                Icon(
                    Icons.Default.Edit,
                    contentDescription = "Edit expense",
                    tint = if (isOffline)
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                    else
                        MaterialTheme.colorScheme.primary
                )
            }
        }

        // Scrollable Content Area - only this part moves horizontally
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize(),
                pageSpacing = 0.dp
            ) { page ->
                val shouldLoadContent = abs(page - pagerState.currentPage) <= 1

                if (shouldLoadContent) {
                    val pageExpense = group.expenses.getOrNull(page)

                    ExpenseContentOnly(
                        expense = pageExpense,
                        group = group
                    )
                } else {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
            }
        }

        // Fixed Bottom Navigation - stays stationary during paging
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            IconButton(
                onClick = {
                    if (pagerState.currentPage > 0) {
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(pagerState.currentPage - 1)
                        }
                    }
                },
                enabled = pagerState.currentPage > 0
            ) {
                Icon(
                    modifier = Modifier.size(48.dp),
                    painter = painterResource(id = R.drawable.chevron_left),
                    contentDescription = "Previous expense",
                    tint = if (pagerState.currentPage > 0)
                        MaterialTheme.colorScheme.onSurfaceVariant
                    else
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f)
                )
            }

            IconButton(
                onClick = {
                    if (pagerState.currentPage < group.expenses.size - 1) {
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(pagerState.currentPage + 1)
                        }
                    }
                },
                enabled = pagerState.currentPage < group.expenses.size - 1
            ) {
                Icon(
                    modifier = Modifier.size(48.dp),
                    painter = painterResource(id = R.drawable.chevron_right),
                    contentDescription = "Next expense",
                    tint = if (pagerState.currentPage < group.expenses.size - 1)
                        MaterialTheme.colorScheme.onSurfaceVariant
                    else
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f)
                )
            }
        }
        Spacer(modifier = Modifier.size(24.dp))
    }
}

@Composable
private fun ExpenseContentOnly(
    expense: Expense?,
    group: GroupData
) {
    if (expense == null) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text("Expense not found")
        }
        return
    }

    // Helper function to get member avatar
    fun getMemberAvatar(memberName: String): String? {
        val avatar = group.memberAvatars[memberName]
        return avatar
    }

    // Fixed Surface with scrollable content - matching original design
    Surface(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        shape = MaterialTheme.shapes.medium,
        color = MaterialTheme.colorScheme.surface,
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.colorScheme.secondaryContainer
        ),
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Category emoji with animated border indicating locked state
                val isLocked = expense.isCategoryLocked

                // Animate the border width
                val borderWidth = animateFloatAsState(
                    targetValue = if (isLocked) 2f else 0f,
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    ),
                    label = "borderWidth"
                )

                // Animate the border color
                val borderColor = animateColorAsState(
                    targetValue = if (isLocked)
                        MaterialTheme.colorScheme.primary
                    else
                        Color.Transparent,
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    ),
                    label = "borderColor"
                )

                Surface(
                    shape = MaterialTheme.shapes.extraSmall,
                    border = BorderStroke(borderWidth.value.dp, borderColor.value)
                ) {
                    Text(
                        text = group.categories.find { it.name == expense.category }?.emoji ?: "💰",
                        style = MaterialTheme.typography.headlineLarge,
                        modifier = Modifier.padding(4.dp)
                    )
                }
                Text(
                    text = expense.description,
                    style = MaterialTheme.typography.headlineLarge
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Paid by: ",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.width(4.dp))

                    // Avatar for paid by
                    val paidByAvatar = getMemberAvatar(expense.paidBy)
                    if (paidByAvatar != null) {
                        Text(
                            text = paidByAvatar,
                            style = MaterialTheme.typography.titleLarge,
                            modifier = Modifier.padding(end = 4.dp)
                        )
                    } else {
                        Icon(
                            painter = painterResource(id = R.drawable.account_outline),
                            contentDescription = null,
                            modifier = Modifier.size(24.dp).padding(end = 4.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    Text(
                        text = expense.paidBy,
                        style = MaterialTheme.typography.titleLarge
                    )
                }
                // Animate the expense amount
                val animatedAmount = animateFloatAsState(
                    targetValue = expense.amount.toFloat(),
                    animationSpec = tween(
                        durationMillis = 800,
                        easing = FastOutSlowInEasing
                    ),
                    label = "expenseAmount"
                )

                Text(
                    text = "${String.format("%.2f", animatedAmount.value)}€",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Date",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
                    .format(Date(expense.date)),
                style = MaterialTheme.typography.titleLarge
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Split between",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Column(
                modifier = Modifier.verticalScroll(rememberScrollState())
            ) {
                Spacer(modifier = Modifier.height(8.dp))
                expense.splitBetween.forEach { member ->
                    if (member != expense.paidBy) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Avatar for split between member
                                val memberAvatar = getMemberAvatar(member)
                                if (memberAvatar != null) {
                                    Text(
                                        text = memberAvatar,
                                        style = MaterialTheme.typography.bodyLarge,
                                        modifier = Modifier.padding(end = 4.dp)
                                    )
                                } else {
                                    Icon(
                                        painter = painterResource(id = R.drawable.account_outline),
                                        contentDescription = null,
                                        modifier = Modifier.size(20.dp),
                                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = member,
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                            // Calculate per-person amount with safety check for division by zero
                            val amountPerPerson = if (expense.splitBetween.isNotEmpty()) {
                                expense.amount / expense.splitBetween.size
                            } else {
                                0.0
                            }

                            // Animate the per-person amount
                            val animatedPerPersonAmount = animateFloatAsState(
                                targetValue = amountPerPerson.toFloat(),
                                animationSpec = tween(
                                    durationMillis = 800,
                                    easing = FastOutSlowInEasing
                                ),
                                label = "perPersonAmount"
                            )

                            Text(
                                text = "-${String.format("%.2f", animatedPerPersonAmount.value)}€",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }
            }
        }
    }
}