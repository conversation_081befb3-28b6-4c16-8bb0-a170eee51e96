package com.example.splitexpenses.ui.viewmodels

import com.example.splitexpenses.data.Expense
import java.util.Calendar
import java.util.Date

/**
 * Data class representing the current filter state for expenses
 */
data class ExpenseFilterState(
    // Date filtering (matching Statistics screen)
    val periodType: PeriodType = PeriodType.ALL_TIME,
    val selectedMonth: Int = Calendar.getInstance().get(Calendar.MONTH),
    val selectedYear: Int = Calendar.getInstance().get(Calendar.YEAR),
    val startDate: Long? = null,
    val endDate: Long? = null,

    // Category filtering
    val selectedCategories: Set<String> = emptySet(),

    // Member filtering (only who paid)
    val selectedMembers: Set<String> = emptySet(),

    // Search filtering (description only)
    val searchText: String = "",

    // Amount range filtering
    val minAmount: Float? = null,
    val maxAmount: Float? = null
) {
    /**
     * Check if any filters are active
     */
    fun hasActiveFilters(): Boolean {
        return periodType != PeriodType.ALL_TIME ||
               selectedCategories.isNotEmpty() ||
               selectedMembers.isNotEmpty() ||
               searchText.isNotBlank() ||
               minAmount != null ||
               maxAmount != null
    }

    /**
     * Get the number of active filters
     */
    fun getActiveFilterCount(): Int {
        var count = 0
        if (periodType != PeriodType.ALL_TIME) count++
        if (selectedCategories.isNotEmpty()) count++
        if (selectedMembers.isNotEmpty()) count++
        if (searchText.isNotBlank()) count++
        if (minAmount != null || maxAmount != null) count++
        return count
    }
}

/**
 * Enum for period filter options (matching Statistics screen)
 */
enum class PeriodType(val displayName: String) {
    ALL_TIME("All Time"),
    THIS_MONTH("Month"),
    THIS_YEAR("Year"),
    CUSTOM("Custom")
}

/**
 * Extension functions for filtering expenses (matching Statistics screen logic exactly)
 */
fun List<Expense>.applyFilters(filterState: ExpenseFilterState): List<Expense> {
    return this.filter { expense ->
        // Date filtering (exactly matching Statistics screen logic)
        val passesDateFilter = when (filterState.periodType) {
            PeriodType.ALL_TIME -> true
            PeriodType.THIS_MONTH -> {
                val expenseCalendar = Calendar.getInstance().apply { timeInMillis = expense.date }
                filterState.selectedYear == expenseCalendar.get(Calendar.YEAR) &&
                filterState.selectedMonth == expenseCalendar.get(Calendar.MONTH)
            }
            PeriodType.THIS_YEAR -> {
                val expenseCalendar = Calendar.getInstance().apply { timeInMillis = expense.date }
                filterState.selectedYear == expenseCalendar.get(Calendar.YEAR)
            }
            PeriodType.CUSTOM -> {
                if (filterState.startDate != null && filterState.endDate != null) {
                    // Ensure we include the entire start day (from 00:00:00)
                    val startCalendar = Calendar.getInstance().apply {
                        timeInMillis = filterState.startDate!!
                        set(Calendar.HOUR_OF_DAY, 0)
                        set(Calendar.MINUTE, 0)
                        set(Calendar.SECOND, 0)
                        set(Calendar.MILLISECOND, 0)
                    }
                    val adjustedStartDate = startCalendar.timeInMillis

                    // Ensure we include the entire end day (until 23:59:59.999)
                    val endCalendar = Calendar.getInstance().apply {
                        timeInMillis = filterState.endDate!!
                        set(Calendar.HOUR_OF_DAY, 23)
                        set(Calendar.MINUTE, 59)
                        set(Calendar.SECOND, 59)
                        set(Calendar.MILLISECOND, 999)
                    }
                    val adjustedEndDate = endCalendar.timeInMillis

                    expense.date in adjustedStartDate..adjustedEndDate
                } else {
                    false
                }
            }
        }

        // Category filtering
        val passesCategoryFilter = if (filterState.selectedCategories.isEmpty()) {
            true
        } else {
            expense.category in filterState.selectedCategories
        }

        // Member filtering (only who paid, matching Statistics screen)
        val passesMemberFilter = if (filterState.selectedMembers.isEmpty()) {
            true
        } else {
            expense.paidBy in filterState.selectedMembers
        }

        // Search filtering (description only)
        val passesSearchFilter = if (filterState.searchText.isBlank()) {
            true
        } else {
            val searchLower = filterState.searchText.lowercase()
            expense.description.lowercase().contains(searchLower)
        }

        // Amount range filtering
        val passesAmountFilter = when {
            filterState.minAmount != null && filterState.maxAmount != null -> {
                expense.amount >= filterState.minAmount && expense.amount <= filterState.maxAmount
            }
            filterState.minAmount != null -> {
                expense.amount >= filterState.minAmount
            }
            filterState.maxAmount != null -> {
                expense.amount <= filterState.maxAmount
            }
            else -> true
        }

        passesDateFilter && passesCategoryFilter && passesMemberFilter && passesSearchFilter && passesAmountFilter
    }
}
