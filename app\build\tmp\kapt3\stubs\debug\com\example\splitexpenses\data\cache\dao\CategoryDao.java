package com.example.splitexpenses.data.cache.dao;

/**
 * Data Access Object for Category entities
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\t\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00102\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00100\u00122\u0006\u0010\u0006\u001a\u00020\u0007H\'J\u0018\u0010\u0013\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u000e\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00102\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u0016\u001a\u00020\u00032\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010\u0019\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ \u0010\u001b\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u00072\b\b\u0002\u0010\u001c\u001a\u00020\u001dH\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\f\u00a8\u0006 "}, d2 = {"Lcom/example/splitexpenses/data/cache/dao/CategoryDao;", "", "clearAllCategories", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCategoriesForGroup", "groupId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCategory", "category", "Lcom/example/splitexpenses/data/cache/entities/CategoryEntity;", "(Lcom/example/splitexpenses/data/cache/entities/CategoryEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCategoryById", "categoryId", "getCategoriesForGroup", "", "getCategoriesForGroupFlow", "Lkotlinx/coroutines/flow/Flow;", "getCategoryById", "getUnsyncedCategories", "getUnsyncedCategoriesForGroup", "insertCategories", "categories", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertCategory", "markCategoryAsSynced", "markCategoryAsUnsynced", "timestamp", "", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCategory", "app_debug"})
@androidx.room.Dao()
public abstract interface CategoryDao {
    
    /**
     * Get all categories for a specific group as Flow
     */
    @androidx.room.Query(value = "SELECT * FROM categories WHERE groupId = :groupId ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.splitexpenses.data.cache.entities.CategoryEntity>> getCategoriesForGroupFlow(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId);
    
    /**
     * Get all categories for a specific group
     */
    @androidx.room.Query(value = "SELECT * FROM categories WHERE groupId = :groupId ORDER BY name ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCategoriesForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.CategoryEntity>> $completion);
    
    /**
     * Get a specific category by ID
     */
    @androidx.room.Query(value = "SELECT * FROM categories WHERE id = :categoryId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCategoryById(@org.jetbrains.annotations.NotNull()
    java.lang.String categoryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.data.cache.entities.CategoryEntity> $completion);
    
    /**
     * Get categories that are not synced
     */
    @androidx.room.Query(value = "SELECT * FROM categories WHERE isSynced = 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUnsyncedCategories(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.CategoryEntity>> $completion);
    
    /**
     * Get unsynced categories for a specific group
     */
    @androidx.room.Query(value = "SELECT * FROM categories WHERE groupId = :groupId AND isSynced = 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUnsyncedCategoriesForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.splitexpenses.data.cache.entities.CategoryEntity>> $completion);
    
    /**
     * Insert or replace a category
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertCategory(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.CategoryEntity category, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Insert or replace multiple categories
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertCategories(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.cache.entities.CategoryEntity> categories, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Update a category
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCategory(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.CategoryEntity category, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete a category
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCategory(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.CategoryEntity category, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete a category by ID
     */
    @androidx.room.Query(value = "DELETE FROM categories WHERE id = :categoryId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCategoryById(@org.jetbrains.annotations.NotNull()
    java.lang.String categoryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Delete all categories for a group
     */
    @androidx.room.Query(value = "DELETE FROM categories WHERE groupId = :groupId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCategoriesForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Mark a category as synced
     */
    @androidx.room.Query(value = "UPDATE categories SET isSynced = 1 WHERE id = :categoryId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object markCategoryAsSynced(@org.jetbrains.annotations.NotNull()
    java.lang.String categoryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Mark a category as unsynced
     */
    @androidx.room.Query(value = "UPDATE categories SET isSynced = 0, lastModified = :timestamp WHERE id = :categoryId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object markCategoryAsUnsynced(@org.jetbrains.annotations.NotNull()
    java.lang.String categoryId, long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Clear all categories
     */
    @androidx.room.Query(value = "DELETE FROM categories")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllCategories(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Data Access Object for Category entities
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}