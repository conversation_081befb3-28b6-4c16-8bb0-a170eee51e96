package com.example.splitexpenses.ui.viewmodels;

/**
 * ViewModel for the balance details screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0006\u0010\r\u001a\u00020\u000eJ\u0006\u0010\u000f\u001a\u00020\u000eJ\b\u0010\u0010\u001a\u00020\u0002H\u0016J\u000e\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014J\n\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0002J\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00140\u0018J\u001e\u0010\u0019\u001a\u001a\u0012\u0016\u0012\u0014\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00120\u001a0\u0018J\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u00162\u0006\u0010\u001c\u001a\u00020\u0016J\u000e\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u0013\u001a\u00020\u0014J\u000e\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u0013\u001a\u00020\u0014J\u000e\u0010 \u001a\u00020\u000e2\u0006\u0010!\u001a\u00020\u0016R\u0019\u0010\b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/BalanceDetailsViewModel;", "Lcom/example/splitexpenses/ui/viewmodels/BaseViewModel;", "Lcom/example/splitexpenses/ui/viewmodels/BalanceDetailsUiState;", "expenseRepository", "Lcom/example/splitexpenses/data/repositories/ExpenseRepository;", "groupRepository", "Lcom/example/splitexpenses/data/repositories/GroupRepository;", "(Lcom/example/splitexpenses/data/repositories/ExpenseRepository;Lcom/example/splitexpenses/data/repositories/GroupRepository;)V", "currentGroup", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/example/splitexpenses/data/GroupData;", "getCurrentGroup", "()Lkotlinx/coroutines/flow/StateFlow;", "calculateFinances", "", "clearUserFilters", "createInitialState", "getAbsoluteBalance", "", "userFinance", "Lcom/example/splitexpenses/data/UserFinance;", "getCurrentUserName", "", "getFilteredFinances", "", "getFilteredSettlements", "Lkotlin/Triple;", "getMemberAvatar", "memberName", "isCreditor", "", "isDebtor", "toggleUserFilter", "userId", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class BalanceDetailsViewModel extends com.example.splitexpenses.ui.viewmodels.BaseViewModel<com.example.splitexpenses.ui.viewmodels.BalanceDetailsUiState> {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.ExpenseRepository expenseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.GroupRepository groupRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> currentGroup = null;
    
    @javax.inject.Inject()
    public BalanceDetailsViewModel(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.ExpenseRepository expenseRepository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.GroupRepository groupRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> getCurrentGroup() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.splitexpenses.ui.viewmodels.BalanceDetailsUiState createInitialState() {
        return null;
    }
    
    /**
     * Calculate finances for the current group
     */
    public final void calculateFinances() {
    }
    
    /**
     * Get the current user's name for the current group
     * @return The current user's name or null if not found
     */
    private final java.lang.String getCurrentUserName() {
        return null;
    }
    
    /**
     * Toggle a user filter on or off
     * @param userId The user ID to toggle
     */
    public final void toggleUserFilter(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
    }
    
    /**
     * Clear all user filters
     */
    public final void clearUserFilters() {
    }
    
    /**
     * Get finances filtered by selected users
     * @return Filtered list of UserFinance objects
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.data.UserFinance> getFilteredFinances() {
        return null;
    }
    
    /**
     * Get settlements filtered by selected users
     * @return Filtered list of settlements
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<kotlin.Triple<java.lang.String, java.lang.String, java.lang.Double>> getFilteredSettlements() {
        return null;
    }
    
    /**
     * Get the absolute value of a user's balance
     * @param userFinance The user finance object
     * @return The absolute value of the user's balance
     */
    public final double getAbsoluteBalance(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.UserFinance userFinance) {
        return 0.0;
    }
    
    /**
     * Get the avatar for a member
     * @param memberName The name of the member
     * @return The avatar emoji or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMemberAvatar(@org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
        return null;
    }
    
    /**
     * Check if a user is a creditor (has a positive balance)
     * @param userFinance The user finance object
     * @return True if the user is a creditor, false otherwise
     */
    public final boolean isCreditor(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.UserFinance userFinance) {
        return false;
    }
    
    /**
     * Check if a user is a debtor (has a negative balance)
     * @param userFinance The user finance object
     * @return True if the user is a debtor, false otherwise
     */
    public final boolean isDebtor(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.UserFinance userFinance) {
        return false;
    }
}