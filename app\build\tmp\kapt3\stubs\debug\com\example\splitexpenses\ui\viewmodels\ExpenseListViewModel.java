package com.example.splitexpenses.ui.viewmodels;

/**
 * ViewModel for the expense list screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0010\"\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJF\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00160\u00192\u0006\u0010\u001a\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\b\b\u0002\u0010\u001d\u001a\u00020\u001eJ\b\u0010\u001f\u001a\u00020\u0002H\u0016J\u000e\u0010 \u001a\u00020\u001eH\u0086@\u00a2\u0006\u0002\u0010!J\u0016\u0010\"\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010$J\u0014\u0010%\u001a\u00020\u00122\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00160\'J\u000e\u0010(\u001a\u00020\u001e2\u0006\u0010)\u001a\u00020*J\u0010\u0010+\u001a\u0004\u0018\u00010\u00162\u0006\u0010,\u001a\u00020\u0016J\u0006\u0010-\u001a\u00020\u001eJ\u0016\u0010.\u001a\u00020\u001e2\u0006\u0010,\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010$J\u000e\u0010/\u001a\u00020\u00122\u0006\u00100\u001a\u00020\u001eJ\u0016\u00101\u001a\u00020\u00122\u0006\u00102\u001a\u00020\u00162\u0006\u00103\u001a\u00020\u0016J\u0016\u00104\u001a\u00020\u00122\u0006\u0010#\u001a\u00020\u00162\u0006\u00105\u001a\u00020\u001eJ\u0018\u00106\u001a\u00020\u001e2\b\u00107\u001a\u0004\u0018\u00010\u0016H\u0086@\u00a2\u0006\u0002\u0010$J\u0016\u00108\u001a\u00020\u001e2\u0006\u00109\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010$JN\u0010:\u001a\u00020\u00122\u0006\u0010#\u001a\u00020\u00162\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00160\u00192\u0006\u0010\u001a\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001c2\b\b\u0002\u0010\u001d\u001a\u00020\u001eJ\u000e\u0010;\u001a\u00020\u00122\u0006\u0010<\u001a\u00020=J\u001c\u0010>\u001a\u00020\u001e2\f\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00160\u0019H\u0086@\u00a2\u0006\u0002\u0010@J\u0016\u0010A\u001a\u00020\u001e2\u0006\u00109\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010$R\u0019\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006B"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/ExpenseListViewModel;", "Lcom/example/splitexpenses/ui/viewmodels/OfflineAwareViewModel;", "Lcom/example/splitexpenses/ui/viewmodels/ExpenseListUiState;", "expenseRepository", "Lcom/example/splitexpenses/data/repositories/ExpenseRepository;", "groupRepository", "Lcom/example/splitexpenses/data/repositories/GroupRepository;", "connectivityManager", "Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;", "(Lcom/example/splitexpenses/data/repositories/ExpenseRepository;Lcom/example/splitexpenses/data/repositories/GroupRepository;Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;)V", "currentGroup", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/example/splitexpenses/data/GroupData;", "getCurrentGroup", "()Lkotlinx/coroutines/flow/StateFlow;", "getGroupRepository", "()Lcom/example/splitexpenses/data/repositories/GroupRepository;", "addExpense", "", "amount", "", "description", "", "paidBy", "splitBetween", "", "category", "date", "", "isCategoryLocked", "", "createInitialState", "deleteCurrentGroup", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpense", "expenseId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteExpenses", "expenseIds", "", "exportToCsv", "outputStream", "Ljava/io/OutputStream;", "getMemberAvatar", "memberName", "isCurrentUserGroupCreator", "removeMemberAndKick", "setMultiSelectMode", "enabled", "shareInvitationLink", "groupId", "groupName", "toggleExpenseSelection", "selected", "updateCurrentUserAvatar", "avatarEmoji", "updateCurrentUserName", "newName", "updateExpense", "updateFilterState", "newFilterState", "Lcom/example/splitexpenses/ui/viewmodels/ExpenseFilterState;", "updateGroupMembers", "members", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGroupName", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ExpenseListViewModel extends com.example.splitexpenses.ui.viewmodels.OfflineAwareViewModel<com.example.splitexpenses.ui.viewmodels.ExpenseListUiState> {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.ExpenseRepository expenseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.GroupRepository groupRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> currentGroup = null;
    
    @javax.inject.Inject()
    public ExpenseListViewModel(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.ExpenseRepository expenseRepository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.GroupRepository groupRepository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.connectivity.NetworkConnectivityManager connectivityManager) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.data.repositories.GroupRepository getGroupRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> getCurrentGroup() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.splitexpenses.ui.viewmodels.ExpenseListUiState createInitialState() {
        return null;
    }
    
    /**
     * Add a new expense
     * @param amount The amount of the expense
     * @param description The description of the expense
     * @param paidBy The name of the person who paid
     * @param splitBetween The names of the people to split the expense between
     * @param category The category of the expense
     * @param date The date of the expense
     * @param isCategoryLocked Whether the category is locked and should not be auto-detected
     */
    public final void addExpense(double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String paidBy, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> splitBetween, @org.jetbrains.annotations.NotNull()
    java.lang.String category, long date, boolean isCategoryLocked) {
    }
    
    /**
     * Update an existing expense
     * @param expenseId The ID of the expense to update
     * @param amount The new amount
     * @param description The new description
     * @param paidBy The new payer
     * @param splitBetween The new split
     * @param category The new category
     * @param date The new date
     * @param isCategoryLocked Whether the category is locked and should not be auto-detected
     */
    public final void updateExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String paidBy, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> splitBetween, @org.jetbrains.annotations.NotNull()
    java.lang.String category, long date, boolean isCategoryLocked) {
    }
    
    /**
     * Delete an expense
     * @param expenseId The ID of the expense to delete
     * @return True if the expense was successfully deleted, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteExpense(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Delete multiple expenses
     * @param expenseIds The IDs of the expenses to delete
     */
    public final void deleteExpenses(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> expenseIds) {
    }
    
    /**
     * Toggle multi-select mode
     * @param enabled Whether multi-select mode should be enabled
     */
    public final void setMultiSelectMode(boolean enabled) {
    }
    
    /**
     * Toggle selection of an expense
     * @param expenseId The ID of the expense to toggle
     * @param selected Whether the expense should be selected
     */
    public final void toggleExpenseSelection(@org.jetbrains.annotations.NotNull()
    java.lang.String expenseId, boolean selected) {
    }
    
    /**
     * Export the current group to CSV
     * @param outputStream The output stream to write to
     * @return True if export was successful, false otherwise
     */
    public final boolean exportToCsv(@org.jetbrains.annotations.NotNull()
    java.io.OutputStream outputStream) {
        return false;
    }
    
    /**
     * Delete the current group
     * @return True if the group was successfully deleted, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteCurrentGroup(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update the members of the current group
     * @param members The new list of members
     * @return True if the members were successfully updated, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupMembers(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> members, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Remove a member from the group and kick them out (remove from allowedUsers)
     * This should only be called by the group creator
     * @param memberName The name of the member to remove
     * @return True if successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeMemberAndKick(@org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Check if the current user is the creator of the current group
     * @return True if the current user is the creator, false otherwise
     */
    public final boolean isCurrentUserGroupCreator() {
        return false;
    }
    
    /**
     * Share an invitation link for the current group
     * @param groupId The ID of the group to share
     * @param groupName The name of the group
     */
    public final void shareInvitationLink(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String groupName) {
    }
    
    /**
     * Update the group name
     * @param newName The new name for the group
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupName(@org.jetbrains.annotations.NotNull()
    java.lang.String newName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update the current user's avatar
     * @param avatarEmoji The emoji to use as the avatar, or null to remove the avatar
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateCurrentUserAvatar(@org.jetbrains.annotations.Nullable()
    java.lang.String avatarEmoji, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update the current user's name
     * @param newName The new name for the current user
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateCurrentUserName(@org.jetbrains.annotations.NotNull()
    java.lang.String newName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Get the avatar for a member
     * @param memberName The name of the member
     * @return The avatar emoji or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMemberAvatar(@org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
        return null;
    }
    
    /**
     * Update the filter state
     */
    public final void updateFilterState(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.ui.viewmodels.ExpenseFilterState newFilterState) {
    }
}