package com.example.splitexpenses.data.cache;

/**
 * Room database for offline caching of SplitExpenses data
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u000b2\u00020\u0001:\u0001\u000bB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&\u00a8\u0006\f"}, d2 = {"Lcom/example/splitexpenses/data/cache/SplitExpensesDatabase;", "Landroidx/room/RoomDatabase;", "()V", "categoryDao", "Lcom/example/splitexpenses/data/cache/dao/CategoryDao;", "expenseDao", "Lcom/example/splitexpenses/data/cache/dao/ExpenseDao;", "groupDao", "Lcom/example/splitexpenses/data/cache/dao/GroupDao;", "syncQueueDao", "Lcom/example/splitexpenses/data/cache/dao/SyncQueueDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.example.splitexpenses.data.cache.entities.GroupEntity.class, com.example.splitexpenses.data.cache.entities.ExpenseEntity.class, com.example.splitexpenses.data.cache.entities.CategoryEntity.class, com.example.splitexpenses.data.cache.entities.SyncQueueEntity.class}, version = 2, exportSchema = false)
public abstract class SplitExpensesDatabase extends androidx.room.RoomDatabase {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.splitexpenses.data.cache.SplitExpensesDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.splitexpenses.data.cache.SplitExpensesDatabase.Companion Companion = null;
    
    public SplitExpensesDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.splitexpenses.data.cache.dao.GroupDao groupDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.splitexpenses.data.cache.dao.ExpenseDao expenseDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.splitexpenses.data.cache.dao.CategoryDao categoryDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.splitexpenses.data.cache.dao.SyncQueueDao syncQueueDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\tR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/splitexpenses/data/cache/SplitExpensesDatabase$Companion;", "", "()V", "INSTANCE", "Lcom/example/splitexpenses/data/cache/SplitExpensesDatabase;", "clearInstance", "", "getDatabase", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Get the database instance
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.splitexpenses.data.cache.SplitExpensesDatabase getDatabase(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
        
        /**
         * Clear the database instance (useful for testing)
         */
        public final void clearInstance() {
        }
    }
}