package com.example.splitexpenses.data.cache.dao

import androidx.room.*
import com.example.splitexpenses.data.cache.entities.GroupEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Group entities
 */
@Dao
interface GroupDao {

    /**
     * Get all groups as a Flow for reactive updates
     */
    @Query("SELECT * FROM groups ORDER BY lastModified DESC")
    fun getAllGroupsFlow(): Flow<List<GroupEntity>>

    /**
     * Get all groups
     */
    @Query("SELECT * FROM groups ORDER BY lastModified DESC")
    suspend fun getAllGroups(): List<GroupEntity>

    /**
     * Get a specific group by ID
     */
    @Query("SELECT * FROM groups WHERE id = :groupId")
    suspend fun getGroupById(groupId: String): GroupEntity?

    /**
     * Get a specific group by ID as Flow
     */
    @Query("SELECT * FROM groups WHERE id = :groupId")
    fun getGroupByIdFlow(groupId: String): Flow<GroupEntity?>

    /**
     * Get groups that are not synced
     */
    @Query("SELECT * FROM groups WHERE isSynced = 0")
    suspend fun getUnsyncedGroups(): List<GroupEntity>

    /**
     * Insert or replace a group
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGroup(group: GroupEntity)

    /**
     * Insert or replace multiple groups
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGroups(groups: List<GroupEntity>)

    /**
     * Update a group
     */
    @Update
    suspend fun updateGroup(group: GroupEntity)

    /**
     * Delete a group
     */
    @Delete
    suspend fun deleteGroup(group: GroupEntity)

    /**
     * Delete a group by ID
     */
    @Query("DELETE FROM groups WHERE id = :groupId")
    suspend fun deleteGroupById(groupId: String)

    /**
     * Mark a group as synced
     */
    @Query("UPDATE groups SET isSynced = 1 WHERE id = :groupId")
    suspend fun markGroupAsSynced(groupId: String)

    /**
     * Mark a group as unsynced
     */
    @Query("UPDATE groups SET isSynced = 0, lastModified = :timestamp WHERE id = :groupId")
    suspend fun markGroupAsUnsynced(groupId: String, timestamp: Long = System.currentTimeMillis())

    /**
     * Clear all groups
     */
    @Query("DELETE FROM groups")
    suspend fun clearAllGroups()
}
