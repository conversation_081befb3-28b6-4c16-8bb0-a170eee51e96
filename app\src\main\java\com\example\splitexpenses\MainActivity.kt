package com.example.splitexpenses

import android.content.Intent
import android.os.Bundle

import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController

import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.Repository
import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager
import com.example.splitexpenses.ui.components.BalanceDetailsScreen
import com.example.splitexpenses.ui.components.CreateGroupDialog
import com.example.splitexpenses.ui.components.ExpenseDetailsScreen
import com.example.splitexpenses.ui.components.ExpenseEditScreen
import com.example.splitexpenses.ui.components.ExpenseListScreen
import com.example.splitexpenses.ui.components.GroupListScreen
import com.example.splitexpenses.ui.components.ImportDialog
import com.example.splitexpenses.ui.components.InvitationAcceptDialog
import com.example.splitexpenses.ui.components.JoinGroupDialog
import com.example.splitexpenses.ui.components.ManageMembersDialog
import com.example.splitexpenses.ui.components.StatisticsScreen
import com.example.splitexpenses.ui.navigation.NavDestinations
import com.example.splitexpenses.ui.navigation.navigateWithoutAnimation
import com.example.splitexpenses.ui.theme.SplitExpensesTheme
import com.example.splitexpenses.ui.viewmodels.ExpenseFilterState
import com.example.splitexpenses.util.CsvImportError
import com.example.splitexpenses.util.CsvImportResult
import com.example.splitexpenses.util.InvitationLinkUtil


class MainActivity : ComponentActivity() {
    private lateinit var repository: Repository
    private lateinit var connectivityManager: NetworkConnectivityManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        repository = Repository(this)
        connectivityManager = NetworkConnectivityManager(this)

        // Start listening for real-time updates to available groups
        repository.startListeningForAvailableGroups()

        // Handle deep links
        handleIntent(intent)
    }

    override fun onDestroy() {
        super.onDestroy()

        // Clean up all Firebase listeners
        repository.cleanup()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)

        // Handle deep links in the new intent
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent) {
        // Check if the intent is a deep link
        if (intent.action == Intent.ACTION_VIEW && intent.data != null) {
            // Log the received URI for debugging
            val uri = intent.data
            println("Received deep link: $uri")

            // Extract the group ID from the URI
            val groupId = InvitationLinkUtil.extractGroupId(uri)
            println("Extracted group ID: $groupId")

            if (groupId != null) {
                // Store the group ID to be handled in the MainScreen
                intent.putExtra("invitation_group_id", groupId)

                // Log that we're storing the group ID
                println("Storing group ID in intent: $groupId")
            } else {
                println("Failed to extract group ID from URI: $uri")
            }
        } else if (intent.hasExtra("invitation_group_id")) {
            // Log if we already have a group ID in the intent
            val groupId = intent.getStringExtra("invitation_group_id")
            println("Intent already contains group ID: $groupId")
        }

        setContent {
            var showBalanceDetails by rememberSaveable { mutableStateOf(false) }
            var selectedExpense by rememberSaveable { mutableStateOf<Expense?>(null) }
            var showExpenseEdit by rememberSaveable { mutableStateOf<Expense?>(null) }
            var showManageMembersDialog by remember { mutableStateOf(false) }
            var showStatistics by remember { mutableStateOf(false) }
            var showCreateGroupDialog by remember { mutableStateOf(false) }
            var showDeleteExpenseDialog by remember { mutableStateOf<Expense?>(null) }
            var showDeleteGroupDialog by remember { mutableStateOf(false) }
            var isMultiSelectMode by remember { mutableStateOf(false) }
            var selectedExpenses by remember { mutableStateOf(setOf<String>()) }
            var isGroupMultiSelectMode by remember { mutableStateOf(false) }
            var selectedGroups by remember { mutableStateOf(setOf<String>()) }

            // Handle back button press
            BackHandler {
                // Handle multi-select mode in expenses list
                if (repository.currentGroup.value != null && isMultiSelectMode) {
                    // Exit expense multi-select mode
                    isMultiSelectMode = false
                    selectedExpenses = emptySet()
                }
                // Handle multi-select mode in groups list
                else if (repository.currentGroup.value == null && isGroupMultiSelectMode) {
                    // Exit group multi-select mode
                    isGroupMultiSelectMode = false
                    selectedGroups = emptySet()
                }
                // Regular back button behavior
                else if (repository.currentGroup.value != null) {
                    when {
                        showExpenseEdit != null -> {
                            // If in expense edit, go back to expense details or list
                            showExpenseEdit = null
                        }

                        selectedExpense != null -> {
                            // If in expense details, go back to expense list
                            selectedExpense = null
                        }

                        showBalanceDetails -> {
                            // If in balance details, go back to expense list
                            showBalanceDetails = false
                        }

                        showStatistics -> {
                            // If in statistics, go back to expense list
                            showStatistics = false
                        }

                        else -> {
                            // If in expense list, go back to group selection
                            repository.clearCurrentGroup()
                            showBalanceDetails = false
                            selectedExpense = null
                            showExpenseEdit = null
                            showStatistics = false
                        }
                    }
                } else {
                    // If we're at group selection, let the system handle back press
                    finish()
                }
            }

            SplitExpensesTheme {
                val navController = rememberNavController()
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    MainScreen(
                        repository = repository,
                        connectivityManager = connectivityManager,
                        showBalanceDetails = showBalanceDetails,
                        onShowBalanceDetailsChange = { showBalanceDetails = it },
                        selectedExpense = selectedExpense,
                        onSelectedExpenseChange = { selectedExpense = it },
                        showExpenseEdit = showExpenseEdit,
                        onShowExpenseEditChange = { showExpenseEdit = it },
                        showCreateGroupDialog = showCreateGroupDialog,
                        onShowCreateGroupDialogChange = { showCreateGroupDialog = it },
                        showDeleteExpenseDialog = showDeleteExpenseDialog,
                        onShowDeleteExpenseDialogChange = { showDeleteExpenseDialog = it },
                        showDeleteGroupDialog = showDeleteGroupDialog,
                        onShowDeleteGroupDialogChange = { showDeleteGroupDialog = it },
                        showManageMembersDialog = showManageMembersDialog,
                        onShowManageMembersDialogChange = { showManageMembersDialog = it },
                        showStatistics = showStatistics,
                        onShowStatisticsChange = { showStatistics = it },
                        isMultiSelectMode = isMultiSelectMode,
                        onMultiSelectModeChange = { isMultiSelectMode = it },
                        selectedExpenses = selectedExpenses,
                        onSelectedExpensesChange = { selectedExpenses = it },
                        isGroupMultiSelectMode = isGroupMultiSelectMode,
                        onGroupMultiSelectModeChange = { isGroupMultiSelectMode = it },
                        selectedGroups = selectedGroups,
                        onSelectedGroupsChange = { selectedGroups = it },
                        modifier = Modifier.padding(innerPadding),
                        intent = intent,
                        navController = navController
                    )
                }
            }
        }
    }
}

@Composable
fun MainScreen(
    repository: Repository,
    connectivityManager: NetworkConnectivityManager,
    showBalanceDetails: Boolean,
    onShowBalanceDetailsChange: (Boolean) -> Unit,
    selectedExpense: Expense?,
    onSelectedExpenseChange: (Expense?) -> Unit,
    showExpenseEdit: Expense?,
    onShowExpenseEditChange: (Expense?) -> Unit,
    showCreateGroupDialog: Boolean,
    onShowCreateGroupDialogChange: (Boolean) -> Unit,
    showDeleteExpenseDialog: Expense?,
    onShowDeleteExpenseDialogChange: (Expense?) -> Unit,
    showDeleteGroupDialog: Boolean,
    onShowDeleteGroupDialogChange: (Boolean) -> Unit,
    showManageMembersDialog: Boolean,
    onShowManageMembersDialogChange: (Boolean) -> Unit,
    showStatistics: Boolean,
    onShowStatisticsChange: (Boolean) -> Unit,
    isMultiSelectMode: Boolean,
    onMultiSelectModeChange: (Boolean) -> Unit,
    selectedExpenses: Set<String>,
    onSelectedExpensesChange: (Set<String>) -> Unit,
    isGroupMultiSelectMode: Boolean,
    onGroupMultiSelectModeChange: (Boolean) -> Unit,
    selectedGroups: Set<String>,
    onSelectedGroupsChange: (Set<String>) -> Unit,
    modifier: Modifier = Modifier,
    intent: Intent? = null,
    navController: NavHostController
) {
    val scope = rememberCoroutineScope()
    val currentGroup by repository.currentGroup.collectAsState()
    val availableGroups by repository.availableGroups.collectAsState()
    val isConnected by connectivityManager.isConnected().collectAsState(initial = true)
    var showJoinGroupDialog by remember { mutableStateOf(false) }
    var showImportDialog by remember { mutableStateOf(false) }

    // State for invitation handling
    var showInvitationDialog by remember { mutableStateOf(false) }
    var invitationGroupId by remember { mutableStateOf("") }
    var invitationGroupName by remember { mutableStateOf("") }
    var unassignedMembers by remember { mutableStateOf(listOf<String>()) }

    // We don't need to load available groups here anymore since we're using real-time listeners
    // The repository.startListeningForAvailableGroups() call in onCreate handles this

    // Handle invitation deep link
    LaunchedEffect(intent) {
        intent?.let {
            val groupId = it.getStringExtra("invitation_group_id")
            println("LaunchedEffect detected group ID: $groupId")

            if (groupId != null) {
                println("Processing invitation for group: $groupId")

                // Get group details and unassigned members
                try {
                    // Check if the group exists
                    println("Fetching group data from Firebase...")
                    val snapshot = repository.database.child("groups").child(groupId).get().await()

                    if (!snapshot.exists()) {
                        println("Group not found in Firebase: $groupId")
                        // TODO: Show error dialog for non-existent group
                        return@let
                    }

                    // Use the correct type parameter for getValue
                    val group = snapshot.getValue(GroupData::class.java)
                    println("Group data retrieved: ${group != null}")

                    if (group != null) {
                        // Store the group information
                        invitationGroupId = groupId
                        invitationGroupName = group.name
                        println("Group name: ${group.name}")

                        // Get unassigned members
                        println("Getting unassigned members...")
                        unassignedMembers = repository.getUnassignedMembers(groupId)
                        println("Unassigned members: $unassignedMembers")

                        // Check if the current user is already in the group
                        val currentUserUid = repository.userPreferences.getDeviceUid()
                        if (currentUserUid in group.allowedUsers) {
                            println("User is already a member of this group")

                            // Get the user's name in this group
                            val userName = repository.userPreferences.getSavedUserForGroup(groupId)
                            if (userName != null) {
                                // Join the group directly
                                println("Joining group directly as: $userName")
                                repository.joinGroup(groupId, userName)
                                return@let
                            }
                        }

                        // Show the invitation dialog
                        println("Showing invitation dialog")
                        showInvitationDialog = true
                    } else {
                        println("Group data is null for ID: $groupId")
                        // TODO: Show error dialog for invalid group data
                    }
                } catch (e: Exception) {
                    println("Error loading invitation group: ${e.message}")
                    e.printStackTrace()
                    // TODO: Show error dialog for network/database errors
                }
            }
        }
    }

    // Observe access lost events and navigate back to group list
    LaunchedEffect(Unit) {
        repository.accessLost.collect { accessLost ->
            println("MainActivity: Access lost state changed: $accessLost")
            if (accessLost) {
                println("MainActivity: User lost access to group, clearing current group")
                repository.clearCurrentGroup()
                // Reset the access lost flag
                repository.resetAccessLost()
                println("MainActivity: Current group cleared and access lost flag reset")
            }
        }
    }

    Box(modifier = modifier.fillMaxSize()) {
        // Main Content
        when {
            // Statistics Screen
            showStatistics && currentGroup != null -> {
                StatisticsScreen(
                    group = currentGroup,
                    onBackClick = { onShowStatisticsChange(false) }
                )
            }
            // Group screen
            currentGroup != null -> {
                when {
                    // Expense Edit Screen
                    showExpenseEdit != null -> {
                        // Get the current user from UserPreferences
                        val currentUser = repository.getSavedUserForGroup(currentGroup!!.id) ?: ""

                        ExpenseEditScreen(
                            expense = showExpenseEdit,
                            group = currentGroup!!,
                            currentUser = currentUser,
                            onSave = { amount, description, paidBy, splitBetween, category, date, isCategoryLocked ->
                                scope.launch {
                                    if (showExpenseEdit.id.isEmpty()) {
                                        repository.addExpense(
                                            amount,
                                            description,
                                            paidBy,
                                            splitBetween,
                                            category,
                                            date,
                                            isCategoryLocked
                                        )
                                    } else {
                                        repository.updateExpense(
                                            showExpenseEdit.id,
                                            amount,
                                            description,
                                            paidBy,
                                            splitBetween,
                                            category,
                                            date,
                                            isCategoryLocked
                                        )
                                    }
                                    onShowExpenseEditChange(null)
                                }
                            },
                            onCancel = { onShowExpenseEditChange(null) },
                            isOffline = !isConnected
                        )
                    }
                    // Expense Details Screen
                    selectedExpense != null -> {
                        ExpenseDetailsScreen(
                            expense = selectedExpense,
                            group = currentGroup!!,
                            onBackClick = { onSelectedExpenseChange(null) },
                            onEditClick = {
                                onShowExpenseEditChange(selectedExpense)
                                onSelectedExpenseChange(null)
                            },
                            onPreviousClick = {
                                val currentIndex =
                                    currentGroup?.expenses?.indexOf(selectedExpense) ?: -1
                                if (currentIndex > 0) {
                                    onSelectedExpenseChange(currentGroup?.expenses?.get(currentIndex - 1))
                                }
                            },
                            onNextClick = {
                                val currentIndex =
                                    currentGroup?.expenses?.indexOf(selectedExpense) ?: -1
                                if (currentIndex < (currentGroup?.expenses?.size ?: 0) - 1) {
                                    onSelectedExpenseChange(currentGroup?.expenses?.get(currentIndex + 1))
                                }
                            },
                            hasPrevious = (currentGroup?.expenses?.indexOf(selectedExpense)
                                ?: -1) > 0,
                            hasNext = (currentGroup?.expenses?.indexOf(selectedExpense)
                                ?: -1) < (currentGroup?.expenses?.size ?: 0) - 1,
                            onNavigateToExpense = { targetExpenseId ->
                                // Direct navigation to any expense by ID
                                val targetExpense = currentGroup?.expenses?.find { it.id == targetExpenseId }
                                if (targetExpense != null) {
                                    onSelectedExpenseChange(targetExpense)
                                }
                            },
                            isOffline = !isConnected
                        )
                    }
                    // Balance Details Screen
                    showBalanceDetails -> {
                        // The BalanceDetailsScreen now uses hiltViewModel() internally
                        // and handles its own state management
                        BalanceDetailsScreen(
                            onBackClick = { onShowBalanceDetailsChange(false) }
                        )
                    }
                    // Expense List Screen (default group view)
                    else -> {
                        ExpenseListScreen(
                            group = currentGroup!!,
                            onExpenseClick = { onSelectedExpenseChange(it) },
                            onShowBalanceDetailsClick = { onShowBalanceDetailsChange(true) },
                            onAddExpenseClick = { onShowExpenseEditChange(Expense()) },
                            onDeleteExpense = { expenseIds ->
                                scope.launch { repository.deleteExpenses(expenseIds) }
                            },
                            onShowStatisticsClick = { onShowStatisticsChange(true) },
                            onShowManageMembersClick = { onShowManageMembersDialogChange(true) },
                            onShowDeleteGroupDialog = { onShowDeleteGroupDialogChange(true) },
                            onExportToCsv = { outputStream ->
                                repository.exportGroupToCsv(outputStream)
                            },
                            isMultiSelectMode = isMultiSelectMode,
                            onMultiSelectModeChange = onMultiSelectModeChange,
                            selectedExpenses = selectedExpenses,
                            onSelectedExpensesChange = { expenseId, selected ->
                                val newSelectedExpenses = if (selected) {
                                    selectedExpenses + expenseId
                                } else {
                                    selectedExpenses - expenseId
                                }
                                onSelectedExpensesChange(newSelectedExpenses)
                            },
                            onNavigateToManageCategories = {
                                // Navigate to the manage categories screen
                                navController.navigateWithoutAnimation(NavDestinations.MANAGE_CATEGORIES_ROUTE)
                            },
                            isOffline = !isConnected,
                            // Default filter state for legacy MainActivity
                            filterState = ExpenseFilterState(),
                            onFilterStateChange = { /* No-op for legacy MainActivity */ }
                        )
                    }
                }
            }
            // Groups list screen (default app view)
            else -> {
                GroupListScreen(
                    groups = availableGroups,
                    onGroupClick = { groupId, member ->
                        scope.launch {
                            repository.joinGroup(groupId, member)
                        }
                    },
                    onCreateGroupClick = { onShowCreateGroupDialogChange(true) },
                    onJoinGroupClick = { showJoinGroupDialog = true },
                    isMultiSelectMode = isGroupMultiSelectMode,
                    onMultiSelectModeChange = onGroupMultiSelectModeChange,
                    selectedGroups = selectedGroups,
                    onSelectedGroupsChange = { groupId, selected ->
                        val newSelectedGroups = if (selected) {
                            selectedGroups + groupId
                        } else {
                            selectedGroups - groupId
                        }
                        onSelectedGroupsChange(newSelectedGroups)
                    },
                    onDeleteSelectedGroups = {
                        scope.launch {
                            selectedGroups.forEach { groupId ->
                                repository.deleteGroup(groupId)
                            }
                            onSelectedGroupsChange(emptySet())
                            onGroupMultiSelectModeChange(false)
                        }
                    },
                    getSavedUserForGroup = repository::getSavedUserForGroup,
                    isCurrentUserGroupCreator = repository::isCurrentUserGroupCreator,
                    isOffline = !isConnected
                )
            }
        }

        // Dialogs
        if (showCreateGroupDialog) {
            CreateGroupDialog(
                onDismiss = { onShowCreateGroupDialogChange(false) },
                onCreate = { name, members, creatorName, callback ->
                    scope.launch {
                        try {
                            repository.createGroup(name, members, creatorName)
                            onShowCreateGroupDialogChange(false)
                            callback(true, null)
                        } catch (e: Exception) {
                            println("Error creating group: ${e.message}")
                            e.printStackTrace()
                            callback(false, e.message)
                        }
                    }
                },
                onImportClick = {
                    showImportDialog = true
                    onShowCreateGroupDialogChange(false)
                }
            )
        }

        // Import Dialog
        if (showImportDialog) {
            ImportDialog(
                onDismiss = { showImportDialog = false },
                onImport = { uri, currentUser ->
                    val inputStream = repository.context.contentResolver.openInputStream(uri)
                    if (inputStream != null) {
                        // Return the full import result
                        repository.importGroupFromCsv(inputStream, currentUser)
                    } else {
                        // Create an error result if we couldn't open the file
                        CsvImportResult(
                            success = false,
                            errors = listOf(
                                CsvImportError(
                                    message = "Could not open the selected file"
                                )
                            )
                        )
                    }
                }
            )
        }

        // Delete Expense Dialog
        showDeleteExpenseDialog?.let { expense ->
            DeleteExpenseDialog(
                onDismiss = { onShowDeleteExpenseDialogChange(null) },
                onConfirm = {
                    scope.launch {
                        try {
                            if (expense.id.isNotEmpty()) {
                                // Use deleteExpense instead of deleteExpenses for a single expense
                                repository.deleteExpense(expense.id)
                                println("Expense deleted successfully: ${expense.id}")
                            } else {
                                println("Cannot delete expense: Invalid expense ID")
                            }
                            onShowDeleteExpenseDialogChange(null)
                        } catch (e: Exception) {
                            println("Error deleting expense: ${e.message}")
                            e.printStackTrace()
                        }
                    }
                }
            )
        }

        // Delete Group Dialog
        if (showDeleteGroupDialog) {
            DeleteGroupDialog(
                onDismiss = { onShowDeleteGroupDialogChange(false) },
                onConfirm = {
                    scope.launch {
                        val groupId = currentGroup?.id
                        if (groupId != null && groupId.isNotEmpty()) {
                            try {
                                println("Deleting group with ID: $groupId")

                                // Stop listening for updates to this group before deleting
                                repository.stopListeningForCurrentGroup()

                                // Delete the group from Firebase
                                repository.deleteGroup(groupId)

                                // Clear the current group selection
                                repository.clearCurrentGroup()

                                // Close the dialog
                                onShowDeleteGroupDialogChange(false)

                                println("Group deleted successfully: $groupId")
                            } catch (e: Exception) {
                                println("Error deleting group: ${e.message}")
                                e.printStackTrace()
                            }
                        } else {
                            println("Cannot delete group: Invalid group ID")
                        }
                    }
                }
            )
        }

        // Manage Members Dialog
        if (showManageMembersDialog) {
            println("MainActivity: Showing ManageMembersDialog, currentGroup: ${currentGroup?.id}")

            // Make sure we have a current group
            currentGroup?.let { group ->
                // Get the current user from UserPreferences
                val currentUser = repository.getSavedUserForGroup(group.id) ?: ""

                ManageMembersDialog(
                    group = group,
                    currentUser = currentUser,
                    onDismiss = {
                        println("ManageMembersDialog: onDismiss called")
                        onShowManageMembersDialogChange(false)
                    },
                    onSave = { updatedMembers ->
                        println("ManageMembersDialog: onSave called with ${updatedMembers.size} members")
                        scope.launch {
                            try {
                                val groupId = group.id
                                if (groupId.isNotEmpty()) {
                                    println("Updating members for group: $groupId")
                                    repository.updateGroupMembers(groupId, updatedMembers)
                                    println("Members updated successfully")
                                }
                                onShowManageMembersDialogChange(false)
                            } catch (e: Exception) {
                                println("Error updating members: ${e.message}")
                                e.printStackTrace()
                            }
                        }
                    },
                    onAddMember = { newMember ->
                        println("ManageMembersDialog: Adding new member: $newMember")
                    },
                    onRemoveMember = { member ->
                        println("ManageMembersDialog: Removing member and kicking: $member")
                        // Use the proper removeMemberAndKick function to remove the member and kick them from allowedUsers
                        scope.launch {
                            try {
                                val success = repository.removeMemberAndKick(group.id, member)
                                if (success) {
                                    println("ManageMembersDialog: Member removed and kicked successfully: $member")
                                } else {
                                    println("ManageMembersDialog: Failed to remove and kick member: $member")
                                }
                            } catch (e: Exception) {
                                println("ManageMembersDialog: Error removing and kicking member: ${e.message}")
                                e.printStackTrace()
                            }
                        }
                    },
                    isCurrentUserGroupCreator = repository.isCurrentUserGroupCreator(group.id),
                    getUidForUserInGroup = { groupId, userName ->
                        repository.userPreferences.getUidForUserInGroup(groupId, userName)
                    },
                    getUserNameForUidInGroup = { groupId, uid ->
                        repository.userPreferences.getUserNameForUidInGroup(groupId, uid)
                    },
                    onInviteClick = { groupId, groupName ->
                        // Generate and share the invitation link
                        println("ManageMembersDialog: Sharing invitation link for group: $groupId")
                        val context = repository.context
                        InvitationLinkUtil.shareInvitationLink(context, groupId, groupName)
                    },
                    isOffline = !isConnected
                )
            } ?: run {
                println("Cannot show ManageMembersDialog: No current group")
                // Auto-dismiss the dialog if there's no current group
                onShowManageMembersDialogChange(false)
            }
        }

        // Invitation Accept Dialog
        if (showInvitationDialog) {
            InvitationAcceptDialog(
                groupName = invitationGroupName,
                unassignedMembers = unassignedMembers,
                onAccept = { memberName ->
                    scope.launch {
                        val success = repository.acceptInvitation(invitationGroupId, memberName)
                        if (success) {
                            // Close the dialog
                            showInvitationDialog = false
                        }
                    }
                },
                onDismiss = {
                    showInvitationDialog = false
                }
            )
        }

        // Join Group Dialog
        if (showJoinGroupDialog) {
            // Collect loading state
            val isLoading by repository.isLoadingGroups.collectAsState()
            val error by repository.groupsError.collectAsState()

            // Local state for join group dialog errors
            var joinGroupError by remember { mutableStateOf<String?>(null) }
            var joinGroupLoading by remember { mutableStateOf(false) }

            JoinGroupDialog(
                onDismiss = {
                    showJoinGroupDialog = false
                    joinGroupError = null
                },
                onJoin = { groupId, userName ->
                    scope.launch {
                        joinGroupLoading = true
                        joinGroupError = null

                        try {
                            // Check if the group exists
                            val snapshot = repository.database.child("groups").child(groupId).get().await()
                            val group = snapshot.getValue(GroupData::class.java)

                            if (group == null) {
                                // Group not found
                                joinGroupError = "Group not found. Please check the Group ID and try again."
                                return@launch
                            }

                            // Check if the current user is already a member of this group
                            val currentUserUid = repository.userPreferences.getDeviceUid()
                            if (currentUserUid in group.allowedUsers) {
                                joinGroupError = "You are already a member of this group."
                                return@launch
                            }

                            // Join the group with the provided user name
                            repository.joinGroup(groupId, userName)

                            // Close join dialog on success
                            showJoinGroupDialog = false
                            joinGroupError = null

                        } catch (e: Exception) {
                            println("Error joining group: ${e.message}")
                            joinGroupError = "Failed to join group: ${e.message}"
                        } finally {
                            joinGroupLoading = false
                        }
                    }
                },
                isLoading = isLoading || joinGroupLoading,
                error = joinGroupError ?: error
            )
        }

    }
}

@Composable
fun DeleteExpenseDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Delete Expense") },
        text = { Text("Are you sure you want to delete this expense?") },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("Delete")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun DeleteGroupDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Delete Group") },
        text = { Text("Are you sure you want to delete this group? This will delete all expenses in the group.") },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("Delete")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

