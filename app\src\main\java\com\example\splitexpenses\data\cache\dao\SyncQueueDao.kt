package com.example.splitexpenses.data.cache.dao

import androidx.room.*
import com.example.splitexpenses.data.cache.entities.SyncQueueEntity
import com.example.splitexpenses.data.cache.entities.SyncEntityType
import com.example.splitexpenses.data.cache.entities.SyncOperationType
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for SyncQueue entities
 */
@Dao
interface SyncQueueDao {

    /**
     * Get all sync queue items as Flow
     */
    @Query("SELECT * FROM sync_queue ORDER BY timestamp ASC")
    fun getAllSyncItemsFlow(): Flow<List<SyncQueueEntity>>

    /**
     * Get all sync queue items
     */
    @Query("SELECT * FROM sync_queue ORDER BY timestamp ASC")
    suspend fun getAllSyncItems(): List<SyncQueueEntity>

    /**
     * Get sync items by entity type
     */
    @Query("SELECT * FROM sync_queue WHERE entityType = :entityType ORDER BY timestamp ASC")
    suspend fun getSyncItemsByType(entityType: SyncEntityType): List<SyncQueueEntity>

    /**
     * Get sync items by entity ID
     */
    @Query("SELECT * FROM sync_queue WHERE entityId = :entityId ORDER BY timestamp ASC")
    suspend fun getSyncItemsByEntityId(entityId: String): List<SyncQueueEntity>

    /**
     * Get sync items with retry count less than max
     */
    @Query("SELECT * FROM sync_queue WHERE retryCount < :maxRetries ORDER BY timestamp ASC")
    suspend fun getSyncItemsForRetry(maxRetries: Int = 3): List<SyncQueueEntity>

    /**
     * Insert a sync queue item
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSyncItem(syncItem: SyncQueueEntity)

    /**
     * Insert multiple sync queue items
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSyncItems(syncItems: List<SyncQueueEntity>)

    /**
     * Update a sync queue item
     */
    @Update
    suspend fun updateSyncItem(syncItem: SyncQueueEntity)

    /**
     * Delete a sync queue item
     */
    @Delete
    suspend fun deleteSyncItem(syncItem: SyncQueueEntity)

    /**
     * Delete a sync queue item by ID
     */
    @Query("DELETE FROM sync_queue WHERE id = :id")
    suspend fun deleteSyncItemById(id: Int)

    /**
     * Delete sync items by entity ID
     */
    @Query("DELETE FROM sync_queue WHERE entityId = :entityId")
    suspend fun deleteSyncItemsByEntityId(entityId: String)

    /**
     * Increment retry count for a sync item
     */
    @Query("UPDATE sync_queue SET retryCount = retryCount + 1, lastError = :error WHERE id = :id")
    suspend fun incrementRetryCount(id: Int, error: String)

    /**
     * Clear all sync queue items
     */
    @Query("DELETE FROM sync_queue")
    suspend fun clearAllSyncItems()

    /**
     * Get count of pending sync items
     */
    @Query("SELECT COUNT(*) FROM sync_queue")
    suspend fun getPendingSyncCount(): Int

    /**
     * Get count of pending sync items as Flow
     */
    @Query("SELECT COUNT(*) FROM sync_queue")
    fun getPendingSyncCountFlow(): Flow<Int>
}
