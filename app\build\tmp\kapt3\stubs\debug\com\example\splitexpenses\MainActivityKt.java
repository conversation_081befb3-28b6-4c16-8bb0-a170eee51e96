package com.example.splitexpenses;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000N\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a$\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a$\u0010\u0005\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u00aa\u0003\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0014\u0010\u0011\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0010\u0012\u0004\u0012\u00020\u00010\u000e2\b\u0010\u0012\u001a\u0004\u0018\u00010\u00102\u0014\u0010\u0013\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0010\u0012\u0004\u0012\u00020\u00010\u000e2\u0006\u0010\u0014\u001a\u00020\f2\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000e2\b\u0010\u0016\u001a\u0004\u0018\u00010\u00102\u0014\u0010\u0017\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0010\u0012\u0004\u0012\u00020\u00010\u000e2\u0006\u0010\u0018\u001a\u00020\f2\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000e2\u0006\u0010\u001a\u001a\u00020\f2\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000e2\u0006\u0010\u001c\u001a\u00020\f2\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000e2\u0006\u0010\u001e\u001a\u00020\f2\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\"0!2\u0018\u0010#\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0!\u0012\u0004\u0012\u00020\u00010\u000e2\u0006\u0010$\u001a\u00020\f2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\"0!2\u0018\u0010\'\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0!\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010(\u001a\u00020)2\n\b\u0002\u0010*\u001a\u0004\u0018\u00010+2\u0006\u0010,\u001a\u00020-H\u0007\u00a8\u0006."}, d2 = {"DeleteExpenseDialog", "", "onDismiss", "Lkotlin/Function0;", "onConfirm", "DeleteGroupDialog", "MainScreen", "repository", "Lcom/example/splitexpenses/data/Repository;", "connectivityManager", "Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;", "showBalanceDetails", "", "onShowBalanceDetailsChange", "Lkotlin/Function1;", "selectedExpense", "Lcom/example/splitexpenses/data/Expense;", "onSelectedExpenseChange", "showExpenseEdit", "onShowExpenseEditChange", "showCreateGroupDialog", "onShowCreateGroupDialogChange", "showDeleteExpenseDialog", "onShowDeleteExpenseDialogChange", "showDeleteGroupDialog", "onShowDeleteGroupDialogChange", "showManageMembersDialog", "onShowManageMembersDialogChange", "showStatistics", "onShowStatisticsChange", "isMultiSelectMode", "onMultiSelectModeChange", "selectedExpenses", "", "", "onSelectedExpensesChange", "isGroupMultiSelectMode", "onGroupMultiSelectModeChange", "selectedGroups", "onSelectedGroupsChange", "modifier", "Landroidx/compose/ui/Modifier;", "intent", "Landroid/content/Intent;", "navController", "Landroidx/navigation/NavHostController;", "app_debug"})
public final class MainActivityKt {
    
    @androidx.compose.runtime.Composable()
    public static final void MainScreen(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Repository repository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.connectivity.NetworkConnectivityManager connectivityManager, boolean showBalanceDetails, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onShowBalanceDetailsChange, @org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.Expense selectedExpense, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.splitexpenses.data.Expense, kotlin.Unit> onSelectedExpenseChange, @org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.Expense showExpenseEdit, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.splitexpenses.data.Expense, kotlin.Unit> onShowExpenseEditChange, boolean showCreateGroupDialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onShowCreateGroupDialogChange, @org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.Expense showDeleteExpenseDialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.splitexpenses.data.Expense, kotlin.Unit> onShowDeleteExpenseDialogChange, boolean showDeleteGroupDialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onShowDeleteGroupDialogChange, boolean showManageMembersDialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onShowManageMembersDialogChange, boolean showStatistics, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onShowStatisticsChange, boolean isMultiSelectMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onMultiSelectModeChange, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedExpenses, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.Set<java.lang.String>, kotlin.Unit> onSelectedExpensesChange, boolean isGroupMultiSelectMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onGroupMultiSelectModeChange, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedGroups, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.Set<java.lang.String>, kotlin.Unit> onSelectedGroupsChange, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable()
    android.content.Intent intent, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavHostController navController) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DeleteExpenseDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DeleteGroupDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm) {
    }
}