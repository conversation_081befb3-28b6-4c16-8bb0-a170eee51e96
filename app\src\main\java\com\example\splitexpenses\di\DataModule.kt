package com.example.splitexpenses.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent

import android.content.Context

import javax.inject.Singleton

import com.example.splitexpenses.data.source.DataSource
import com.example.splitexpenses.data.source.FirebaseDataSource
import com.example.splitexpenses.data.source.LocalDataSource
import com.example.splitexpenses.data.source.OfflineDataSource
import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager
import com.example.splitexpenses.data.cache.SplitExpensesDatabase
import com.example.splitexpenses.data.sync.SyncQueueManager
import javax.inject.Named

/**
 * Hilt module that provides data source dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object DataModule {

    /**
     * Provides the Firebase data source
     * @return The Firebase data source implementation
     */
    @Provides
    @Singleton
    @Named("firebase")
    fun provideFirebaseDataSource(): DataSource {
        return FirebaseDataSource()
    }

    /**
     * Provides the local data source
     * @param context The application context
     * @return The local data source implementation
     */
    @Provides
    @Singleton
    fun provideLocalDataSource(@ApplicationContext context: Context): LocalDataSource {
        return LocalDataSource(context)
    }

    /**
     * Provides the network connectivity manager
     * @param context The application context
     * @return The network connectivity manager
     */
    @Provides
    @Singleton
    fun provideNetworkConnectivityManager(@ApplicationContext context: Context): NetworkConnectivityManager {
        return NetworkConnectivityManager(context)
    }

    /**
     * Provides the Room database
     * @param context The application context
     * @return The Room database instance
     */
    @Provides
    @Singleton
    fun provideSplitExpensesDatabase(@ApplicationContext context: Context): SplitExpensesDatabase {
        return SplitExpensesDatabase.getDatabase(context)
    }

    /**
     * Provides the offline data source
     * @param database The Room database
     * @return The offline data source implementation
     */
    @Provides
    @Singleton
    @Named("offline")
    fun provideOfflineDataSource(database: SplitExpensesDatabase): OfflineDataSource {
        return OfflineDataSource(database)
    }

    /**
     * Provides the sync queue manager
     * @param database The Room database
     * @param remoteDataSource The Firebase data source
     * @return The sync queue manager
     */
    @Provides
    @Singleton
    fun provideSyncQueueManager(
        database: SplitExpensesDatabase,
        @Named("firebase") remoteDataSource: DataSource
    ): SyncQueueManager {
        return SyncQueueManager(database, remoteDataSource)
    }
}
