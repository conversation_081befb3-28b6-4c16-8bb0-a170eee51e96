package com.example.splitexpenses.data.source

import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.Expense
import kotlinx.coroutines.flow.Flow

/**
 * Interface for data sources that provide access to groups and expenses
 */
interface DataSource {
    /**
     * Get a group by ID
     * @param groupId The ID of the group to fetch
     * @return The group data or null if not found
     */
    suspend fun getGroup(groupId: String): GroupData?
    
    /**
     * Get all available groups
     * @return A list of all available groups
     */
    suspend fun getAvailableGroups(): List<GroupData>
    
    /**
     * Observe a specific group for changes
     * @param groupId The ID of the group to observe
     * @return A Flow emitting the group data whenever it changes
     */
    fun observeGroup(groupId: String): Flow<GroupData?>
    
    /**
     * Observe all available groups for changes
     * @return A Flow emitting the list of available groups whenever it changes
     */
    fun observeAvailableGroups(): Flow<List<GroupData>>
    
    /**
     * Save a group
     * @param group The group to save
     */
    suspend fun saveGroup(group: GroupData)
    
    /**
     * Update a specific field in a group
     * @param groupId The ID of the group to update
     * @param field The field to update
     * @param value The new value for the field
     */
    suspend fun updateGroupField(groupId: String, field: String, value: Any)
    
    /**
     * Delete a group
     * @param groupId The ID of the group to delete
     */
    suspend fun deleteGroup(groupId: String)
    
    /**
     * Add an expense to a group
     * @param groupId The ID of the group to add the expense to
     * @param expense The expense to add
     */
    suspend fun addExpense(groupId: String, expense: Expense)
    
    /**
     * Update an expense in a group
     * @param groupId The ID of the group containing the expense
     * @param expense The updated expense
     */
    suspend fun updateExpense(groupId: String, expense: Expense)
    
    /**
     * Delete an expense from a group
     * @param groupId The ID of the group containing the expense
     * @param expenseId The ID of the expense to delete
     */
    suspend fun deleteExpense(groupId: String, expenseId: String)
    
    /**
     * Delete multiple expenses from a group
     * @param groupId The ID of the group containing the expenses
     * @param expenseIds The IDs of the expenses to delete
     */
    suspend fun deleteExpenses(groupId: String, expenseIds: Set<String>)
    
    /**
     * Clean up any resources used by this data source
     */
    fun cleanup()
}
