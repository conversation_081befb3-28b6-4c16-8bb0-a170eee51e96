package com.example.splitexpenses.ui.viewmodels;

/**
 * Base ViewModel class that provides offline awareness functionality
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\b\'\u0018\u0000*\b\b\u0000\u0010\u0001*\u00020\u00022\b\u0012\u0004\u0012\u0002H\u00010\u0003B\r\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\u000eH\u0004J+\u0010\u000f\u001a\u00020\u00102\u001c\u0010\u0011\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u0013\u0012\u0006\u0012\u0004\u0018\u00010\u00140\u0012H\u0004\u00a2\u0006\u0002\u0010\u0015J;\u0010\u0016\u001a\u00020\u00102\u001c\u0010\u0011\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u0013\u0012\u0006\u0012\u0004\u0018\u00010\u00140\u00122\u000e\b\u0002\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00100\u0018H\u0004\u00a2\u0006\u0002\u0010\u0019J;\u0010\u001a\u001a\u00020\u00102\u001c\u0010\u0011\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u0013\u0012\u0006\u0012\u0004\u0018\u00010\u00140\u00122\u000e\b\u0002\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00100\u0018H\u0004\u00a2\u0006\u0002\u0010\u0019J\b\u0010\u001b\u001a\u00020\u0010H\u0014J\b\u0010\u001c\u001a\u00020\u0010H\u0014J\b\u0010\n\u001a\u00020\tH\u0004R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\f\u00a8\u0006\u001d"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/OfflineAwareViewModel;", "S", "Lcom/example/splitexpenses/ui/viewmodels/UiState;", "Lcom/example/splitexpenses/ui/viewmodels/BaseViewModel;", "connectivityManager", "Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;", "(Lcom/example/splitexpenses/data/connectivity/NetworkConnectivityManager;)V", "_isConnected", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "isConnected", "Lkotlinx/coroutines/flow/StateFlow;", "()Lkotlinx/coroutines/flow/StateFlow;", "connectivityFlow", "Lkotlinx/coroutines/flow/Flow;", "executeCreateOperation", "", "operation", "Lkotlin/Function1;", "Lkotlin/coroutines/Continuation;", "", "(Lkotlin/jvm/functions/Function1;)V", "executeEditOperationIfConnected", "onOfflineError", "Lkotlin/Function0;", "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V", "executeIfConnected", "handleOfflineEditError", "handleOfflineError", "app_debug"})
public abstract class OfflineAwareViewModel<S extends com.example.splitexpenses.ui.viewmodels.UiState> extends com.example.splitexpenses.ui.viewmodels.BaseViewModel<S> {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.connectivity.NetworkConnectivityManager connectivityManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isConnected = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isConnected = null;
    
    public OfflineAwareViewModel(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.connectivity.NetworkConnectivityManager connectivityManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isConnected() {
        return null;
    }
    
    /**
     * Check if the device is currently connected
     */
    protected final boolean isConnected() {
        return false;
    }
    
    /**
     * Get connectivity status as a flow
     */
    @org.jetbrains.annotations.NotNull()
    protected final kotlinx.coroutines.flow.Flow<java.lang.Boolean> connectivityFlow() {
        return null;
    }
    
    /**
     * Execute an operation only if connected, otherwise show offline error
     */
    protected final void executeIfConnected(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> operation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onOfflineError) {
    }
    
    /**
     * Execute an operation that modifies existing data only if connected
     * This prevents editing existing content when offline
     */
    protected final void executeEditOperationIfConnected(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> operation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onOfflineError) {
    }
    
    /**
     * Execute an operation that creates new data - allowed offline
     * New content can be created offline and synced later
     */
    protected final void executeCreateOperation(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> operation) {
    }
    
    /**
     * Default handler for offline errors
     */
    protected void handleOfflineError() {
    }
    
    /**
     * Default handler for offline edit errors
     */
    protected void handleOfflineEditError() {
    }
}