package com.example.splitexpenses.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.Dialog

/**
 * Dialog for accepting a group invitation
 * @param groupName The name of the group
 * @param unassignedMembers List of unassigned member names to choose from
 * @param onAccept Callback when the user accepts the invitation with a selected member name
 * @param onDismiss Callback when the user dismisses the dialog
 * @param allMembers List of all members in the group (optional)
 * @param assignedMembers List of already assigned members (optional)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InvitationAcceptDialog(
    groupName: String,
    unassignedMembers: List<String>,
    onAccept: (String) -> Unit,
    onDismiss: () -> Unit,
    allMembers: List<String> = unassignedMembers,
    assignedMembers: List<String> = emptyList()
) {
    var selectedMember by remember { mutableStateOf("") }
    var manualName by remember { mutableStateOf("") }
    var showManualNameError by remember { mutableStateOf(false) }
    var manualNameErrorMessage by remember { mutableStateOf("Name cannot be empty") }

    // Computed property to determine if all members are assigned
    val allMembersAssigned = unassignedMembers.isEmpty() && allMembers.isNotEmpty()

    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = MaterialTheme.shapes.medium,
            color = MaterialTheme.colorScheme.surface,
            tonalElevation = 8.dp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(24.dp)
                    .fillMaxWidth()
            ) {
                // Title
                Text(
                    text = "Join Group",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Group name
                Text(
                    text = "You've been invited to join \"$groupName\"",
                    style = MaterialTheme.typography.bodyLarge
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Always show the member list first
                Text(
                    text = "Members in this group:",
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Member selection list
                Column(
                    modifier = Modifier
                        .weight(1f, fill = false)
                        .heightIn(max = 200.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    // Show all members, but disable selection for assigned ones
                    allMembers.forEach { member ->
                        val isAssigned = assignedMembers.contains(member)
                        val isSelectable = !isAssigned

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .let {
                                    if (isSelectable) {
                                        it.clickable { selectedMember = member }
                                    } else {
                                        it
                                    }
                                }
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = selectedMember == member,
                                onClick = {
                                    if (isSelectable) {
                                        selectedMember = member
                                    }
                                },
                                enabled = isSelectable
                            )

                            Column(modifier = Modifier.padding(start = 8.dp)) {
                                Text(
                                    text = member,
                                    color = if (isAssigned) MaterialTheme.colorScheme.secondaryContainer else MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyLarge,
                                    textDecoration = if (isAssigned) TextDecoration.LineThrough else TextDecoration.None
                                )

                                if (isAssigned) {
                                    Text(
                                        text = "Already taken",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.error
                                    )
                                }
                            }
                        }
                    }

                    // If no members are available, show a message
                    if (allMembers.isEmpty()) {
                        Text(
                            text = "No members found in this group",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.secondaryContainer,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Manual name entry section - always show this
                Text(
                    text = if (allMembersAssigned)
                        "All members are already assigned. Please enter a new name:"
                    else if (unassignedMembers.isEmpty() && allMembers.isEmpty())
                        "No members found. Please enter your name:"
                    else
                        "",
                    style = MaterialTheme.typography.bodyMedium
                )
                if (allMembersAssigned || (unassignedMembers.isEmpty() && allMembers.isEmpty())) {
                    OutlinedTextField(
                        value = manualName,
                        onValueChange = {
                            manualName = it
                            showManualNameError = false

                            // Check if name is already taken
                            if (allMembers.contains(it)) {
                                showManualNameError = true
                                manualNameErrorMessage = "This name is already taken"
                            }
                        },
                        label = { Text("Your Name") },
                        modifier = Modifier.fillMaxWidth(),
                        isError = showManualNameError,
                        singleLine = true,
                        supportingText = {
                            if (showManualNameError) {
                                Text(
                                    manualNameErrorMessage,
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                        }
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Button(
                        onClick = {
                            // Check if we're using a selected member or manual name
                            if (selectedMember.isNotBlank()) {
                                // Using selected member
                                if (assignedMembers.contains(selectedMember)) {
                                    // This shouldn't happen due to UI constraints, but check anyway
                                    return@Button
                                }
                                onAccept(selectedMember)
                            } else {
                                // Using manual name
                                if (manualName.isBlank()) {
                                    showManualNameError = true
                                    manualNameErrorMessage = "Name cannot be empty"
                                    return@Button
                                }

                                if (allMembers.contains(manualName)) {
                                    showManualNameError = true
                                    manualNameErrorMessage = "This name is already taken"
                                    return@Button
                                }

                                onAccept(manualName)
                            }
                        },
                        enabled = selectedMember.isNotBlank() || (manualName.isNotBlank() && !allMembers.contains(manualName))
                    ) {
                        Text("Join Group")
                    }
                }
            }
        }
    }
}
