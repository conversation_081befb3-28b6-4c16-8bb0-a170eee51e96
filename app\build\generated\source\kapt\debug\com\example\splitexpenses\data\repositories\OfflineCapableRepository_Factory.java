package com.example.splitexpenses.data.repositories;

import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager;
import com.example.splitexpenses.data.source.DataSource;
import com.example.splitexpenses.data.source.OfflineDataSource;
import com.example.splitexpenses.data.sync.SyncQueueManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OfflineCapableRepository_Factory implements Factory<OfflineCapableRepository> {
  private final Provider<DataSource> remoteDataSourceProvider;

  private final Provider<OfflineDataSource> offlineDataSourceProvider;

  private final Provider<NetworkConnectivityManager> networkConnectivityManagerProvider;

  private final Provider<SyncQueueManager> syncQueueManagerProvider;

  public OfflineCapableRepository_Factory(Provider<DataSource> remoteDataSourceProvider,
      Provider<OfflineDataSource> offlineDataSourceProvider,
      Provider<NetworkConnectivityManager> networkConnectivityManagerProvider,
      Provider<SyncQueueManager> syncQueueManagerProvider) {
    this.remoteDataSourceProvider = remoteDataSourceProvider;
    this.offlineDataSourceProvider = offlineDataSourceProvider;
    this.networkConnectivityManagerProvider = networkConnectivityManagerProvider;
    this.syncQueueManagerProvider = syncQueueManagerProvider;
  }

  @Override
  public OfflineCapableRepository get() {
    return newInstance(remoteDataSourceProvider.get(), offlineDataSourceProvider.get(), networkConnectivityManagerProvider.get(), syncQueueManagerProvider.get());
  }

  public static OfflineCapableRepository_Factory create(
      Provider<DataSource> remoteDataSourceProvider,
      Provider<OfflineDataSource> offlineDataSourceProvider,
      Provider<NetworkConnectivityManager> networkConnectivityManagerProvider,
      Provider<SyncQueueManager> syncQueueManagerProvider) {
    return new OfflineCapableRepository_Factory(remoteDataSourceProvider, offlineDataSourceProvider, networkConnectivityManagerProvider, syncQueueManagerProvider);
  }

  public static OfflineCapableRepository newInstance(DataSource remoteDataSource,
      OfflineDataSource offlineDataSource, NetworkConnectivityManager networkConnectivityManager,
      SyncQueueManager syncQueueManager) {
    return new OfflineCapableRepository(remoteDataSource, offlineDataSource, networkConnectivityManager, syncQueueManager);
  }
}
