package com.example.splitexpenses.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.Dialog
import kotlinx.coroutines.launch

/**
 * Dialog for manually joining a group by entering a group ID and selecting a user name
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JoinGroupDialog(
    onDismiss: () -> Unit,
    onJoin: (String, String) -> Unit,
    isLoading: Boolean = false,
    error: String? = null,
    onFetchUnassignedMembers: suspend (String) -> List<String> = { _ -> emptyList() },
    onFetchMembersWithStatus: suspend (String) -> Pair<List<String>, List<String>> = { _ -> Pair(emptyList(), emptyList()) }
) {
    var groupId by remember { mutableStateOf("") }
    var userName by remember { mutableStateOf("") }
    var showGroupIdError by remember { mutableStateOf(false) }
    var showUserNameError by remember { mutableStateOf(false) }
    // Only show manual entry if all members are assigned
    var showManualEntry by remember { mutableStateOf(false) }
    var unassignedMembers by remember { mutableStateOf(listOf<String>()) }
    var allMembers by remember { mutableStateOf(listOf<String>()) }
    var assignedMembers by remember { mutableStateOf(listOf<String>()) }
    var selectedMember by remember { mutableStateOf("") }
    var isFetchingMembers by remember { mutableStateOf(false) }
    var userNameErrorMessage by remember { mutableStateOf("Name cannot be empty") }

    // Computed property to determine if all members are assigned
    val allMembersAssigned by derivedStateOf {
        unassignedMembers.isEmpty() && allMembers.isNotEmpty()
    }

    // Automatically fetch members when group ID changes
    val coroutineScope = rememberCoroutineScope()

    // Function to fetch members when group ID changes
    val fetchMembers = {
        if (groupId.isNotBlank()) {
            isFetchingMembers = true
            coroutineScope.launch {
                try {
                    // Get members with their status
                    val (members, assigned) = onFetchMembersWithStatus(groupId)

                    // Update state with the fetched data
                    allMembers = members
                    assignedMembers = assigned
                    unassignedMembers = members.filter { it !in assigned }

                    // Reset selection
                    selectedMember = ""
                } catch (e: Exception) {
                    println("Error fetching members: ${e.message}")
                } finally {
                    isFetchingMembers = false
                }
            }
        } else {
            unassignedMembers = emptyList()
            allMembers = emptyList()
            assignedMembers = emptyList()
            selectedMember = ""
        }
    }

    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = MaterialTheme.shapes.medium,
            color = MaterialTheme.colorScheme.surface,
            tonalElevation = 8.dp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(24.dp)
                    .fillMaxWidth()
            ) {
                // Title
                Text(
                    text = "Join Group",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Group ID field
                OutlinedTextField(
                    value = groupId,
                    onValueChange = {
                        groupId = it
                        showGroupIdError = false

                        // Automatically fetch members when group ID changes
                        fetchMembers()
                    },
                    label = { Text("Group ID") },
                    modifier = Modifier.fillMaxWidth(),
                    isError = (showGroupIdError && groupId.isBlank()) || error != null,
                    singleLine = true,
                    supportingText = {
                        when {
                            error != null -> Text(error, color = MaterialTheme.colorScheme.error)
                            showGroupIdError && groupId.isBlank() -> Text("Group ID cannot be empty", color = MaterialTheme.colorScheme.error)
                            else -> Text("Enter the group ID you received from the group creator")
                        }
                    },
                    enabled = !isLoading && !isFetchingMembers,
                    colors = TextFieldDefaults.colors(
                        unfocusedContainerColor = Color.Transparent,
                        focusedContainerColor = Color.Transparent,
                        unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                    )
                )

                Spacer(modifier = Modifier.height(8.dp))

                if (isFetchingMembers) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    }
                }

                // Member selection and manual entry
                if (groupId.isNotBlank() && !isFetchingMembers) {
                    Spacer(modifier = Modifier.height(8.dp))

                    // Always show the member list first
                    Text(
                        text = "Members in this group:",
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Member list
                    Column(
                        modifier = Modifier
                            .weight(1f, fill = false)
                            .heightIn(max = 200.dp)
                            .verticalScroll(rememberScrollState())
                    ) {
                        // Show all members, but disable selection for assigned ones
                        allMembers.forEach { member ->
                            val isAssigned = assignedMembers.contains(member)
                            val isSelectable = !isAssigned

                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .let {
                                        if (isSelectable) {
                                            it.clickable { selectedMember = member }
                                        } else {
                                            it
                                        }
                                    }
                                    .padding(vertical = 2.dp),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                RadioButton(
                                    selected = selectedMember == member,
                                    onClick = {
                                        if (isSelectable) {
                                            selectedMember = member
                                        }
                                    },
                                    enabled = isSelectable
                                )

                                Column(modifier = Modifier.padding(start = 8.dp)) {
                                    Text(
                                        text = member,
                                        color = if (isAssigned) MaterialTheme.colorScheme.secondaryContainer else Color.Unspecified,
                                        textDecoration = if (isAssigned) TextDecoration.LineThrough else TextDecoration.None
                                    )

                                    if (isAssigned) {
                                        Text(
                                            text = "Already taken",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.error
                                        )
                                    }
                                }
                            }
                        }

                        // If no members are available, show a message
                        if (allMembers.isEmpty()) {
                            Text(
                                text = "No members found in this group",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.secondaryContainer,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }
                    }
                    if (allMembersAssigned || (unassignedMembers.isEmpty() && allMembers.isEmpty()))
                    {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                    // Manual name entry section - always show this
                    Text(
                        text = if (allMembersAssigned)
                            "All members are already assigned. Please enter a new name:"
                        else if (unassignedMembers.isEmpty() && allMembers.isEmpty())
                            "No members found. Please enter your name:"
                        else
                            "",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    if (allMembersAssigned || (unassignedMembers.isEmpty() && allMembers.isEmpty())) {
                        Spacer(modifier = Modifier.height(8.dp))

                        OutlinedTextField(
                            value = userName,
                            onValueChange = {
                                userName = it
                                showUserNameError = false

                                // Check if name is already taken
                                if (allMembers.contains(it)) {
                                    showUserNameError = true
                                    userNameErrorMessage = "This name is already taken"
                                }
                            },
                            label = { Text("Your Name") },
                            modifier = Modifier.fillMaxWidth(),
                            isError = showUserNameError,
                            singleLine = true,
                            supportingText = {
                                if (showUserNameError) {
                                    Text(
                                        userNameErrorMessage,
                                        color = MaterialTheme.colorScheme.error
                                    )
                                }
                            },
                            enabled = !isLoading,
                            colors = TextFieldDefaults.colors(
                                unfocusedContainerColor = Color.Transparent,
                                focusedContainerColor = Color.Transparent,
                                unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                            )
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Loading indicator
                if (isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    }
                }

                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = onDismiss,
                        enabled = !isLoading && !isFetchingMembers
                    ) {
                        Text("Cancel")
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Button(
                        onClick = {
                            var hasError = false
                            if (groupId.isBlank()) {
                                showGroupIdError = true
                                hasError = true
                            }

                            // Check if we're using a selected member or manual name
                            if (selectedMember.isNotBlank()) {
                                // Using selected member
                                if (assignedMembers.contains(selectedMember)) {
                                    // This shouldn't happen due to UI constraints, but check anyway
                                    hasError = true
                                }
                            } else {
                                // Using manual name
                                if (userName.isBlank()) {
                                    showUserNameError = true
                                    userNameErrorMessage = "Name cannot be empty"
                                    hasError = true
                                } else if (allMembers.contains(userName)) {
                                    showUserNameError = true
                                    userNameErrorMessage = "This name is already taken"
                                    hasError = true
                                }
                            }

                            if (hasError) {
                                return@Button
                            }

                            // Determine which name to use
                            val finalName = if (selectedMember.isNotBlank()) selectedMember else userName
                            onJoin(groupId, finalName)
                        },
                        enabled = !isLoading && !isFetchingMembers && groupId.isNotBlank() &&
                                 (selectedMember.isNotBlank() || (userName.isNotBlank() && !allMembers.contains(userName)))
                    ) {
                        Text("Join")
                    }
                }
            }
        }
    }
}
