package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0003\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/example/splitexpenses/ui/components/ChartType;", "", "(Ljava/lang/String;I)V", "PIE_CHART", "app_debug"})
public enum ChartType {
    /*public static final*/ PIE_CHART /* = new PIE_CHART() */;
    
    ChartType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.splitexpenses.ui.components.ChartType> getEntries() {
        return null;
    }
}