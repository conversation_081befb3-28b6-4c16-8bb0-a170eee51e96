package com.example.splitexpenses.data.sync;

/**
 * Manages the sync queue for offline changes
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010\u0010\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u000fJ\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\f0\u0012J\u000e\u0010\u0013\u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u0016\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010\u001a\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010\u001b\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0002\u0010\u0018J&\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\b2\u0006\u0010 \u001a\u00020!H\u0086@\u00a2\u0006\u0002\u0010\"J\u001e\u0010#\u001a\u00020\u000e2\u0006\u0010$\u001a\u00020%2\u0006\u0010 \u001a\u00020!H\u0086@\u00a2\u0006\u0002\u0010&J\u0016\u0010\'\u001a\u00020\u000e2\u0006\u0010(\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010)R\u000e\u0010\u0007\u001a\u00020\bX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006*"}, d2 = {"Lcom/example/splitexpenses/data/sync/SyncQueueManager;", "", "database", "Lcom/example/splitexpenses/data/cache/SplitExpensesDatabase;", "remoteDataSource", "Lcom/example/splitexpenses/data/source/DataSource;", "(Lcom/example/splitexpenses/data/cache/SplitExpensesDatabase;Lcom/example/splitexpenses/data/source/DataSource;)V", "TAG", "", "gson", "Lcom/google/gson/Gson;", "maxRetries", "", "clearAllSyncItems", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPendingSyncCount", "getPendingSyncCountFlow", "Lkotlinx/coroutines/flow/Flow;", "processPendingSyncItems", "", "processSyncCategoryItem", "item", "Lcom/example/splitexpenses/data/cache/entities/SyncQueueEntity;", "(Lcom/example/splitexpenses/data/cache/entities/SyncQueueEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processSyncExpenseItem", "processSyncGroupItem", "processSyncItem", "queueExpenseOperation", "expense", "Lcom/example/splitexpenses/data/Expense;", "groupId", "operationType", "Lcom/example/splitexpenses/data/cache/entities/SyncOperationType;", "(Lcom/example/splitexpenses/data/Expense;Ljava/lang/String;Lcom/example/splitexpenses/data/cache/entities/SyncOperationType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "queueGroupOperation", "group", "Lcom/example/splitexpenses/data/GroupData;", "(Lcom/example/splitexpenses/data/GroupData;Lcom/example/splitexpenses/data/cache/entities/SyncOperationType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeSyncItemsForEntity", "entityId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class SyncQueueManager {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.cache.SplitExpensesDatabase database = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.source.DataSource remoteDataSource = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "SyncQueueManager";
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    private final int maxRetries = 3;
    
    @javax.inject.Inject()
    public SyncQueueManager(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.SplitExpensesDatabase database, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.source.DataSource remoteDataSource) {
        super();
    }
    
    /**
     * Add a group operation to the sync queue
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object queueGroupOperation(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncOperationType operationType, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Add an expense operation to the sync queue
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object queueExpenseOperation(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Expense expense, @org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.cache.entities.SyncOperationType operationType, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Process all pending sync items
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object processPendingSyncItems(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Process a single sync item
     */
    private final java.lang.Object processSyncItem(com.example.splitexpenses.data.cache.entities.SyncQueueEntity item, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Process a group sync item
     */
    private final java.lang.Object processSyncGroupItem(com.example.splitexpenses.data.cache.entities.SyncQueueEntity item, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Process an expense sync item
     */
    private final java.lang.Object processSyncExpenseItem(com.example.splitexpenses.data.cache.entities.SyncQueueEntity item, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Process a category sync item
     */
    private final java.lang.Object processSyncCategoryItem(com.example.splitexpenses.data.cache.entities.SyncQueueEntity item, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Get pending sync count as Flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Integer> getPendingSyncCountFlow() {
        return null;
    }
    
    /**
     * Get pending sync count
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPendingSyncCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    /**
     * Clear all sync items (use with caution)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearAllSyncItems(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Remove sync items for a specific entity
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeSyncItemsForEntity(@org.jetbrains.annotations.NotNull()
    java.lang.String entityId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}