package com.example.splitexpenses.di;

import com.example.splitexpenses.data.cache.SplitExpensesDatabase;
import com.example.splitexpenses.data.source.DataSource;
import com.example.splitexpenses.data.sync.SyncQueueManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataModule_ProvideSyncQueueManagerFactory implements Factory<SyncQueueManager> {
  private final Provider<SplitExpensesDatabase> databaseProvider;

  private final Provider<DataSource> remoteDataSourceProvider;

  public DataModule_ProvideSyncQueueManagerFactory(Provider<SplitExpensesDatabase> databaseProvider,
      Provider<DataSource> remoteDataSourceProvider) {
    this.databaseProvider = databaseProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public SyncQueueManager get() {
    return provideSyncQueueManager(databaseProvider.get(), remoteDataSourceProvider.get());
  }

  public static DataModule_ProvideSyncQueueManagerFactory create(
      Provider<SplitExpensesDatabase> databaseProvider,
      Provider<DataSource> remoteDataSourceProvider) {
    return new DataModule_ProvideSyncQueueManagerFactory(databaseProvider, remoteDataSourceProvider);
  }

  public static SyncQueueManager provideSyncQueueManager(SplitExpensesDatabase database,
      DataSource remoteDataSource) {
    return Preconditions.checkNotNullFromProvides(DataModule.INSTANCE.provideSyncQueueManager(database, remoteDataSource));
  }
}
